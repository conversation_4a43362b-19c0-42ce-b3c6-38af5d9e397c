import react from '@vitejs/plugin-react';
import fs from 'fs';
import path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import { defineConfig, loadEnv } from 'vite';
import svgr from 'vite-plugin-svgr';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');

  const httpsConfig = (() => {
    const keyPath = './affiliate.esto.test.key';
    const certPath = './affiliate.esto.test.crt';

    if (fs.existsSync(keyPath) && fs.existsSync(certPath)) {
      return {
        key: fs.readFileSync(keyPath),
        cert: fs.readFileSync(certPath),
      };
    }
    return undefined;
  })();

  return {
    plugins: [
      react(),
      svgr(),
      mode === 'analyze' &&
        visualizer({
          filename: 'build/bundle-stats.html',
          open: true,
          gzipSize: true,
          brotliSize: true,
        }),
    ].filter(Boolean),

    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        src: path.resolve(__dirname, './src'),
        app: path.resolve(__dirname, './src/app'),
        shared: path.resolve(__dirname, './src/shared'),
        modules: path.resolve(__dirname, './src/modules'),
        pages: path.resolve(__dirname, './src/pages'),
      },
    },

    server: {
      port: 3000,
      host: env.HOST || 'localhost',
      https: httpsConfig,
      open: false,
    },

    build: {
      outDir: 'build',
      sourcemap: true,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            apollo: ['@apollo/client'],
            chakra: ['@chakra-ui/react'],
            effector: ['effector', 'effector-react'],
          },
        },
      },
    },

    define: {
      ...Object.keys(process.env).reduce(
        (prev, key) => {
          if (key.startsWith('REACT_APP_')) {
            prev[`process.env.${key}`] = JSON.stringify(process.env[key]);
          }
          return prev;
        },
        {} as Record<string, string>,
      ),

      'process.env.NODE_ENV': JSON.stringify(
        mode === 'development' ? 'development' : 'production',
      ),
    },

    css: {
      devSourcemap: true,
    },

    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        '@apollo/client',
        '@chakra-ui/react',
        'effector',
        'effector-react',
        'framer-motion',
        'lodash',
      ],
    },
  };
});
