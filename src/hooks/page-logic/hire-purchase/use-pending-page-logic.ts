import {
  AppRoutePaths,
  HirePurchaseRoutePaths,
  REDIRECT_URLS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { useGetPageAttributes } from 'hooks/use-get-page-attributes';
import { useRudderStack } from 'hooks/use-rudderstack';
import { useEffectOnce } from 'hooks/utils';
import type { PendingPageLogic } from 'models';
import { useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { convertPageAttributeNamesToObject } from 'services';

export const usePendingPageLogic = (): PendingPageLogic => {
  const { ruderStackEvents } = useRudderStack();
  const { search } = useLocation();
  const {
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing,
    applicationPrivateInfo,
    user,
  } = useRootContext();

  const { pageAttributes, pageAttributesLoading, getPageAttributes } =
    useGetPageAttributes();

  const [isRedirecting, setIsRedirecting] = useState(false);

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const unpaidInvoiceAmount = user?.unpaid_invoice_amount;
  const referenceKey = user?.reference_key;

  const creditAccount = user?.credit_accounts?.[0];
  const creditLimit = creditAccount?.credit_limit ?? 0;
  const unpaidPrincipal = creditAccount?.unpaid_principal ?? 0;
  const availableCreditLimit = creditLimit - unpaidPrincipal;

  const onEstoAccountButtonClick = () => {
    window.location.href = REDIRECT_URLS.newCustomerProfile;
  };

  const sendAnalyticsEvent = () => {
    if (applicationPrivateInfo) {
      ruderStackEvents.verifyInformation({
        applicationId: applicationPrivateInfo.application_id,
      });
    }
  };

  const onCheckIncomeButtonClick = async () => {
    sendAnalyticsEvent();
    getPageUrlAndNavigate(null, {
      customCurrentPageUrl: `/${AppRoutePaths.HIRE_PURCHASE}/${HirePurchaseRoutePaths.CONTACT_EXTRA}${search}`,
    });
  };

  const onPayDebtButtonClick = () => {
    if (!referenceKey) {
      throw new Error('Reference key is missing');
    }
    const paymentUrl = `${REDIRECT_URLS.newCustomerProfileInvoicePayment}?referenceKey=${referenceKey}`;
    window.location.href = paymentUrl;
  };

  const onCreditLineOnboardingButtonClick = () => {
    setIsRedirecting(true);
    window.location.href = REDIRECT_URLS.newCustomerProfileCreditLine;
  };

  const onCreditLineWithdrawalOnboardingButtonClick = () => {
    setIsRedirecting(true);
    window.location.href = REDIRECT_URLS.creditAccountWithdrawal;
  };

  useEffectOnce(() => {
    getPageAttributes();
  });

  return {
    visiblePageAttributes,
    debtAmount: unpaidInvoiceAmount,
    onPayDebtButtonClick,
    onEstoAccountButtonClick,
    onCheckIncomeButtonClick,
    processingPendingPage: pageAttributesLoading,
    pageUrlAndNavigationProcessing,
    onCreditLineOnboardingButtonClick,
    onCreditLineWithdrawalOnboardingButtonClick,
    isRedirecting,
    availableCreditLimit,
    creditLimit,
  };
};
