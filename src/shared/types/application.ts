import {
  AdminApplicationStatuses,
  type MerchantApplicationDetailsQuery,
} from 'shared/api';

export type ApplicationDirectPaymentRefundItem = ArrayElement<
  NonNullOrUndefined<
    NonNullOrUndefined<
      MerchantApplicationDetailsQuery['application']
    >['direct_payment_refunds']
  >
>;

export type ApplicationDirectPaymentRefunds =
  Array<ApplicationDirectPaymentRefundItem | null> | null;

export type ApplicationType = NonNullOrUndefined<
  MerchantApplicationDetailsQuery['application']
>;

export const STATUSES_WITH_BADGES = [
  AdminApplicationStatuses.NO_USER,
  AdminApplicationStatuses.NO_ACTION,
  AdminApplicationStatuses.SIGNED,
  AdminApplicationStatuses.CANCELLED,
  AdminApplicationStatuses.REJECTED,
] as const;
export type StatusWithBadge = (typeof STATUSES_WITH_BADGES)[number];

export enum ApplicationLinkAction {
  SendLink = 'send-link',
  GenerateQR = 'generate-qr',
  CopyLink = 'copy-link',
  OpenInNewTab = 'open-in-new-tab',
}

export enum ApplicationRequestType {
  AmountModification = 'amount-modification',
  Cancellation = 'cancellation',
  RefundRequest = 'refund-requested',
}

export enum RefundStatuses {
  PENDING = 'PENDING',
  FAILURE = 'FAILURE',
  COMPLETED = 'COMPLETED',
}

export const MIN_AMOUNT_REFUND_REQUEST = 0.1;
