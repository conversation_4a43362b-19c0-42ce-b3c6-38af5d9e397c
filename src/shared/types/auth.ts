export enum LoginMethods {
  MobileId = 'mobile-id',
  SmartId = 'smart-id',
  IdCard = 'id-card',
  Password = 'password',
}

export type LoginSession = {
  sessionId: string;
  challengeId: string;
  time: number;
};

export enum UserPermissions {
  Admin = 1 << 2,
}

export enum MerchantPermissions {
  None = 0,
  Cashier = 1 << 0,
  Developer = 1 << 1,
  Admin = 1 << 2,
  Accountant = 1 << 3,
}

export const PASSWORD_LENGTH = 8;
