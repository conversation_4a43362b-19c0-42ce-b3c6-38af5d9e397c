import { z } from 'zod';

export enum CalculatorTypes {
  ViewOnly = 'hire',
  WithButton = 'dynamic',
}

export enum ChosenLanguage {
  Estonian = 'et-ee',
  Russian = 'ru-ru',
  English = 'en-us',
}

export enum ExtendLanguageAliases {
  et = 'et-ee',
  ru = 'ru-ru',
  en = 'en-us',
  lv = 'lv-lv',
  lt = 'lt-lt',
}
export type AvailableLanguage = 'et-ee' | 'en-us' | 'ru-ru';

export const CalculatorFormSchema = z.object({
  type: z.nativeEnum(CalculatorTypes).nullable(),
  language: z.string().optional(),
  amount: z.number().nullable(),
  width: z.number(),
  height: z.number(),
});

export type CalculatorForm = z.infer<typeof CalculatorFormSchema>;
