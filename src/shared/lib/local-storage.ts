export function getItem<T>(key: string): T | null {
  const raw = localStorage.getItem(key);

  if (!raw) {
    return null;
  }

  try {
    const item = JSON.parse(raw) as T;
    return item;
  } catch {
    return null;
  }
}

export function setItem<T>(key: string, item: T | null): void {
  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  item === null
    ? localStorage.removeItem(key)
    : localStorage.setItem(key, JSON.stringify(item));
}
