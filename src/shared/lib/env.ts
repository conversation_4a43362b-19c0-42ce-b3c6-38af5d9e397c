import type { UserDocumentType } from 'shared/api';
import type { AvailableLanguage, LoginMethods } from 'shared/types';

export const availableLanguages =
  (process.env.REACT_APP_LANGUAGES?.split(',') as Array<AvailableLanguage>) ||
  [];
export const availableLoginMethods =
  (process.env.REACT_APP_LOGIN_METHODS?.split(',') as Array<LoginMethods>) ||
  [];
export const availableIdentityDocuments =
  (process.env.REACT_APP_AVAILABLE_IDENTITY_DOCUMENTS?.split(
    ',',
  ) as Array<UserDocumentType>) || [];

export const affiliateContacts = {
  email: process.env.REACT_APP_PARTNER_EMAIL,
  phone: process.env.REACT_APP_PARTNER_PHONE,
  phoneLoggedIn: process.env.REACT_APP_PARTNER_PHONE_LOGGED_IN,
};

export const customerContacts = {
  email: process.env.REACT_APP_INFO_EMAIL,
  phone: process.env.REACT_APP_INFO_PHONE,
};

export const partnerV1Url = process.env.REACT_APP_PARTNER_V1_URL;

export const phonePrefix = process.env.REACT_APP_PHONE_PREFIX;

export const idUrl = process.env.REACT_APP_ID_URL;

export const reactEnv = process.env.REACT_APP_ENV;

export const region = process.env.REACT_APP_REGION;

export const isProd =
  reactEnv && ['prod-et', 'prod-lt', 'prod-lv'].includes(reactEnv);

export const sentryDsn = process.env.REACT_APP_SENTRY_DSN;

export const googleAnalyticsContainerId = process.env.REACT_APP_GA_CONTAINER_ID;

export const googleAnalyticsId = process.env.REACT_APP_GA_ID;

export const postHogKey = process.env.REACT_APP_POSTHOG_KEY;
export const postHogHost = process.env.REACT_APP_POSTHOG_HOST;

export const calculatorIFrameUrl = process.env.REACT_APP_API_URL;

export const zendeskChatKeyCode = process.env.REACT_APP_ZENDESK_CHAT_KEY_CODE;

export const isEligibilityStatusTooltipEnabled =
  process.env.REACT_APP_IS_ELIGIBILITY_STATUS_TOOLTIP_ENABLED === 'true';

export const apiEndpoint = process.env.REACT_APP_API_ENDPOINT || '';
