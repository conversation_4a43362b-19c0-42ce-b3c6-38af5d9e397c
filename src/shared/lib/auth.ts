import {
  AccountRoute,
  ApplicationListRoute,
  ForDeveloperRoute,
  LoginRoute,
  SettingsRoute,
  TerminalRoute,
} from 'shared/constants/routes';
import { MerchantPermissions, UserPermissions } from 'shared/types';
import { hasPermissions } from 'shared/utils';

export const LOGIN_SESSION_STORAGE_KEY = 'login-session';
export const LOGIN_SESSION_STORAGE_TIMEOUT = 1000 * 60 * 5; // 5 minutes

export function getDefaultRoleRedirect(
  userPermissionBits: number | undefined,
  merchantPermissionBits: number | undefined,
): string {
  if (!userPermissionBits) {
    return LoginRoute.format();
  }

  if (hasPermissions(userPermissionBits, [UserPermissions.Admin])) {
    return TerminalRoute.format();
  }

  if (!merchantPermissionBits) {
    return AccountRoute.format();
  }

  if (hasPermissions(merchantPermissionBits, [MerchantPermissions.Cashier])) {
    return TerminalRoute.format();
  }

  if (hasPermissions(merchantPermissionBits, [MerchantPermissions.Admin])) {
    return SettingsRoute.format();
  }

  if (
    hasPermissions(merchantPermissionBits, [MerchantPermissions.Accountant])
  ) {
    return ApplicationListRoute.format();
  }

  if (hasPermissions(merchantPermissionBits, [MerchantPermissions.Developer])) {
    return ForDeveloperRoute.format();
  }

  return AccountRoute.format();
}
