import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-locize-backend';
import { initReactI18next } from 'react-i18next';
import { availableLanguages } from 'shared/lib';

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    defaultNS: 'common',
    supportedLngs: availableLanguages,
    fallbackLng: 'en',
    ns: [],
    backend: {
      projectId: '3ca71ea5-cd70-4de7-bca0-8df655d215ae',
      referenceLng: 'en',
    },
    detection: {
      order: ['localStorage'],
      caches: ['localStorage'],
    },
  });

export default i18n;
