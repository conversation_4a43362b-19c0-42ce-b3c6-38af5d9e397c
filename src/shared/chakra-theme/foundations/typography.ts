import type { ThemeConfig } from '../config';

const body1Styles = {
  fontFamily: 'karla',
  fontWeight: 'normal',
  fontSize: '1rem',
  letterSpacing: '-0.03em',
  lineHeight: 1.5,
};

const body2Styles = {
  fontFamily: 'karla',
  fontWeight: 'normal',
  fontSize: '0.875rem',
  letterSpacing: '-0.01em',
  lineHeight: 1.43,
};

const captionStyles = {
  fontFamily: 'karla',
  fontWeight: 'normal',
  fontSize: '0.75rem',
  lineHeight: 1.33,
};

export const textStyles = {
  h1: {
    fontFamily: 'karla',
    fontWeight: 'bold',
    fontSize: ['2rem', '2.5rem'],
    letterSpacing: '-0.04em',
    lineHeight: [1.19, 1.15],
  },
  h2: {
    fontFamily: 'karla',
    fontWeight: 'bold',
    fontSize: ['1.5rem', '2rem'],
    letterSpacing: ['-0.03em', '-0.04em'],
    lineHeight: [1.25, 1.19],
  },
  h3: {
    fontFamily: 'karla',
    fontWeight: 'bold',
    fontSize: ['1.25rem', '1.5rem'],
    letterSpacing: ['-0.02em', '-0.03em'],
    lineHeight: [1.4, 1.25],
  },
  h4: {
    fontFamily: 'karla',
    fontWeight: 'bold',
    fontSize: ['1.125rem', '1.25rem'],
    letterSpacing: '-0.02em',
    lineHeight: [1.33, 1.4],
  },
  body1: body1Styles,
  'body1-highlight': {
    ...body1Styles,
    fontWeight: 'bold',
    letterSpacing: '-0.02em',
  },
  body2: body2Styles,
  'body2-highlight': {
    ...body2Styles,
    fontWeight: 'bold',
    letterSpacing: '-0.02em',
  },
  caption: captionStyles,
  'caption-highlight': {
    ...captionStyles,
    fontWeight: 'bold',
    letterSpacing: '-0.02em',
  },
  calculator1: {
    fontFamily: 'karla',
    fontWeight: 'normal',
    fontSize: '1rem',
    lineHeight: 1.5,
  },
};

const getTypography = ({ language }: ThemeConfig) => ({
  fonts: {
    karla:
      language === 'ru'
        ? `'IBM Plex Sans', sans-serif`
        : `'Karla', 'IBM Plex Sans', sans-serif`,
  },
  textStyles,
});

export default getTypography;
