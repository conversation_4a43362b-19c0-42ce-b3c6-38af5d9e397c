import { textStyles } from '../foundations/typography';

const baseStyle = {
  field: {
    width: '100%',
    outline: 0,
    position: 'relative',
    appearance: 'none',
    transition: 'all 0.2s, border-width 0s, padding 0s',
  },
};

const size = {
  md: {
    ...textStyles.body1,
    color: 'neutral.900',
    px: 4,
    h: 10,
    borderRadius: '4px',
    _focus: {
      px: 'calc(1rem - 1px)',
    },
  },
};

const sizes = {
  md: {
    field: size.md,
  },
};

function variantOutline() {
  return {
    field: {
      border: '1px solid',
      borderColor: 'neutral.200',
      bg: 'white',
      boxShadow: 'none',
      outline: 'none',

      '&[type="password"]': {
        letterSpacing: '0.06em',
      },

      _hover: {
        borderColor: 'neutral.800',
      },
      _disabled: {
        bg: 'neutral.50',
        cursor: 'not-allowed',
        borderColor: 'neutral.200',
      },
      _focus: {
        border: '2px solid',
        boxShadow: 'none',
        borderColor: 'neutral.900',
      },
      _invalid: {
        borderColor: 'red.700',
        boxShadow: 'none',
      },
      _placeholder: {
        color: 'neutral.400',
      },
    },
  };
}

const variants = {
  outline: variantOutline,
};

const defaultProps = {
  size: 'md',
  variant: 'outline',
};

export default {
  baseStyle,
  sizes,
  variants,
  defaultProps,
};
