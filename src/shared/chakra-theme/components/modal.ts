import { getColor, transparentize } from '@chakra-ui/theme-tools';

import { textStyles } from '../foundations/typography';

type Dict = Record<string, any>;

const baseStyleOverlay = (props: Dict) => {
  const { theme } = props;

  return {
    bg: transparentize(getColor(theme, 'neutral.900'), 0.95)(theme),
  };
};

function baseStyleDialogContainer(props: Dict) {
  const { isCentered, scrollBehavior } = props;

  return {
    display: 'flex',
    zIndex: 'modal',
    justifyContent: 'center',
    alignItems: isCentered ? 'center' : 'flex-start',
    overflow: scrollBehavior === 'inside' ? 'hidden' : 'auto',
    height: '100vh',
    maxH: 'fill-available',
  };
}

function baseStyleDialog(props: Dict) {
  const { scrollBehavior } = props;

  return {
    borderRadius: [0, '0.75rem'],
    bg: 'white',
    my: [0, '2.5rem'],
    zIndex: 'modal',
    maxH:
      scrollBehavior === 'inside'
        ? ['fill-available', 'calc(fill-available - 5rem)']
        : undefined,
    boxShadow: 'none',
  };
}

const baseStyleHeader = {
  ...textStyles.h3,
  pl: [5, 8],
  pr: [20, 16],
  py: [4, 6],
  borderBottom: ['1px solid ', 'none'],
  borderColor: 'neutral.150',
};

const baseStyleBody = {
  pt: [5, 0],
  px: [5, 8],
  pb: 8,
  flex: 1,
};

const baseStyleFooter = {
  px: [5, 8],
  py: [5, 6],
  flexDirection: ['column-reverse', 'row'],
  alignItems: ['stretch', 'center'],
};

const baseStyle = (props: Dict) => ({
  overlay: baseStyleOverlay(props),
  dialogContainer: baseStyleDialogContainer(props),
  dialog: baseStyleDialog(props),
  header: baseStyleHeader,
  body: baseStyleBody,
  footer: baseStyleFooter,
});

const sizes = {
  custom: {
    dialog: { maxW: ['100vw', 'md', '40rem'], h: ['100vh', 'auto'] },
  },
};

const defaultProps = {
  size: 'custom',
  isCentered: true,
  scrollBehavior: ['inside', 'outside'],
};

export default {
  baseStyle,
  sizes,
  defaultProps,
};
