import { ColorSchemes } from '../foundations/colors';
import { textStyles } from '../foundations/typography';

const baseStyle = {
  textTransform: 'none',
  px: 2,
  py: '2px',
  borderRadius: 'full',
  ...textStyles['caption-highlight'],
};

function variantSolid(props: Record<string, any>) {
  const { colorScheme: c } = props;

  return {
    bg: c === ColorSchemes.NEUTRAL ? `${c}.100` : `${c}.200`,
    color: `${c}.900`,
  };
}

const variants = {
  solid: variantSolid,
};

const defaultProps = {
  variant: 'solid',
  colorScheme: ColorSchemes.NEUTRAL,
};

export default {
  baseStyle,
  variants,
  defaultProps,
};
