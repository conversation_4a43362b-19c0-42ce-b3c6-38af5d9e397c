const parts = ['popper', 'content', 'header', 'body', 'footer', 'arrow'];

const baseStyleContent = {
  width: 'auto',
  p: 2,
  bg: 'white',
  border: 'none',
  borderRadius: '4px',
  boxShadow:
    '0px 1px 8px rgba(0, 0, 0, 0.13), 0px 8px 16px rgba(0, 0, 0, 0.07)',
  zIndex: 'inherit',
  _focus: {
    boxShadow:
      '0px 1px 8px rgba(0, 0, 0, 0.13), 0px 8px 16px rgba(0, 0, 0, 0.07)',
  },
};

const baseStyle = {
  content: baseStyleContent,
};

const variantInfo = {
  popper: {
    maxW: 'initial',
    w: 'auto',
  },
  content: {
    borderRadius: '4px',
    boxShadow: 'none',
    _focus: {
      boxShadow: 'none',
    },
  },
};

const variants = {
  info: variantInfo,
};

export default {
  parts,
  baseStyle,
  variants,
};
