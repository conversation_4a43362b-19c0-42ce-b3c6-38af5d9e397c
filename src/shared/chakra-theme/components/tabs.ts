import type { Dict } from '../config';
import { textStyles } from '../foundations/typography';

const selectedBorderWidth = '3px';

const sizes = {
  sm: {
    tab: {
      ...textStyles['body2-highlight'],
      py: '0.875rem',
      pb: `calc(0.875rem - ${selectedBorderWidth})`,
      px: 0,
      mx: '0.625rem',
    },
  },
  md: {
    tab: {
      ...textStyles['body1-highlight'],
      pt: '1rem',
      pb: `calc(1rem - ${selectedBorderWidth})`,
      px: 0,
      mx: 3,
    },
  },
};

function variantLine(props: Dict): any {
  const { orientation } = props;
  const isVertical = orientation === 'vertical';
  const borderProp = orientation === 'vertical' ? 'borderLeft' : 'borderBottom';
  const marginProp = isVertical ? 'ml' : 'mb';

  return {
    tablist: {
      [borderProp]: '1px solid',
      borderColor: 'neutral.100',
    },
    tab: {
      color: 'neutral.800',
      [borderProp]: `${selectedBorderWidth} solid`,
      borderColor: 'transparent',
      [marginProp]: '-1px',
      _selected: {
        color: 'neutral.must',
        borderColor: 'primary.600',
      },
      _active: {
        color: 'neutral.must',
        bg: 'transparent',
        boxShadow: 'none',
      },
      _focus: {
        color: 'neutral.must',
        bg: 'transparent',
        boxShadow: 'none',
      },
      _disabled: {
        opacity: 0.4,
        cursor: 'not-allowed',
      },
    },
  };
}

const variants = {
  line: variantLine,
};

const defaultProps = {
  size: 'md',
  variant: 'line',
};

export default {
  sizes,
  variants,
  defaultProps,
};
