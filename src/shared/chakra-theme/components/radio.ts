import Checkbox from './checkbox';

const { control } = Checkbox.baseStyle;

const baseStyleControl = {
  ...control,
  borderRadius: 'full',
  _checked: {
    ...control._checked,
    _before: {
      content: `""`,
      display: 'inline-block',
      pos: 'relative',
      w: '50%',
      h: '50%',
      borderRadius: '50%',
      bg: 'currentColor',
    },
  },
};

const baseStyle = {
  label: Checkbox.baseStyle.label,
  control: baseStyleControl,
};

const sizes = {
  md: {
    control: Checkbox.sizes.md.control,
    label: Checkbox.sizes.md.label,
  },
};

const defaultProps = {
  size: 'md',
};

export default {
  baseStyle,
  sizes,
  defaultProps,
};
