import type { Dict } from '../config';
import { ColorSchemes } from '../foundations/colors';
import { textStyles } from '../foundations/typography';

const baseStyle = {
  ...textStyles['body1-highlight'],
  borderRadius: '4px',

  _focus: {
    boxShadow: 'none',
  },
  _disabled: {
    opacity: 0.4,
    cursor: 'not-allowed',
    boxShadow: 'none',
  },
  _loading: {
    opacity: 1,
  },
};

const ghostTextColor: Record<string, string> = {
  primary: 'primary.800',
  neutral: 'neutral.900',
  red: 'red.700',
};

function variantGhost(props: Dict) {
  const { colorScheme: c } = props;

  return {
    color: ghostTextColor[c],
    bg: 'transparent',
    _hover: {
      bg: 'neutral.50',
    },
    _focus: {
      bg: 'neutral.50',
    },
    _active: {
      bg: 'neutral.100',
    },
  };
}

type AccessibleColor = {
  bg?: string;
  color?: string;
  iconColor?: string;
  hoverBg?: string;
  activeBg?: string;
};

/** Accessible color overrides for less accessible colors. */
const accessibleColorMap: Record<string, AccessibleColor> = {
  primary: {
    bg: 'primary.800',
    color: 'white',
    iconColor: 'blue.500',
    hoverBg: 'primary.600',
    activeBg: 'primary.900',
  },
  neutral: {
    bg: 'neutral.900',
    color: 'white',
    hoverBg: 'neutral.800',
    activeBg: 'neutral.must',
  },
  secondary: {
    bg: 'neutral.50',
    color: 'black',
    hoverBg: 'neutral.100',
    activeBg: 'neutral.150',
  },
};

function variantSolid(props: Dict) {
  const { colorScheme: c } = props;

  const {
    bg = `${c}.500`,
    color = 'white',
    hoverBg = `${c}.200`,
    iconColor,
    activeBg = `${c}.700`,
  } = accessibleColorMap[c] || {};

  return {
    bg,
    color,
    '.chakra-button__icon > svg, .chakra-spinner': {
      color: iconColor || color,
    },
    _hover: {
      bg: hoverBg,
      _disabled: {
        bg,
      },
      _loading: {
        bg,
        opacity: 1,
      },
    },
    _focus: {
      bg: hoverBg,
      _disabled: {
        bg,
      },
      _loading: {
        bg,
        opacity: 1,
      },
    },
    _active: { bg: activeBg },
  };
}

const variants = {
  ghost: variantGhost,
  solid: variantSolid,
};

const sizes = {
  md: {
    h: 12,
    minW: 12,
    px: 5,

    '.chakra-button__icon > svg, .chakra-spinner': {
      height: '1.5rem',
      width: '1.5rem',
    },
  },
  sm: ({ variant }: Dict) => ({
    ...textStyles['body2-highlight'],

    h: 10,
    minW: 10,
    px: variant === 'ghost' ? 3 : 4,

    '.chakra-button__icon > svg, .chakra-spinner': {
      height: '1.25rem',
      width: '1.25rem',
    },
  }),
};

const defaultProps = {
  variant: 'solid',
  size: 'md',
  colorScheme: ColorSchemes.SECONDARY,
};

export default {
  baseStyle,
  variants,
  sizes,
  defaultProps,
};
