const baseStyleControl = {
  border: '2px solid',
  borderRadius: '4px',
  borderColor: 'neutral.200',
  bg: 'white',

  _checked: {
    bg: 'primary.600',
    borderColor: 'primary.600',

    _hover: {
      bg: 'primary.600',
      borderColor: 'primary.600',
    },

    _disabled: {
      borderColor: 'neutral.200',
      bg: 'neutral.200',
      color: 'white',
    },
  },

  _disabled: {
    bg: 'white',
    borderColor: 'neutral.100',
  },

  _hover: {
    borderColor: 'primary.600',
  },

  _focus: {
    boxShadow: 'none',
    borderColor: 'primary.600',
  },
};

const baseStyleLabel = {
  userSelect: 'none',
  fontFamily: 'karla',
  fontWeight: 'normal',

  _disabled: { opacity: 0.4 },
};

const baseStyle = {
  control: baseStyleControl,
  label: baseStyleLabel,
};

const sizes = {
  md: {
    control: { w: 5, h: 5 },
    label: {
      fontSize: ['0.875rem', '1rem'],
      letterSpacing: ['-0.01em', '-0.03em'],
      lineHeight: [1.43, 1.5],
    },
    icon: { fontSize: '0.625rem' },
  },
};

const defaultProps = {
  size: 'md',
};

export default {
  baseStyle,
  sizes,
  defaultProps,
};
