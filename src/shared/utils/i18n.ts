import type { AvailableLanguage } from 'shared/types';

export function getDecimalSeparator(locale: string): string {
  return (1.1).toLocaleString(locale)[1];
}

const abbrToLangMap: Record<string, string> = {
  'en-us': 'en',
  'et-ee': 'et',
  'lt-lt': 'lt',
  'ru-ru': 'ru',
  'lv-lv': 'lv',
};

const langToAbbrMap = (lang: AvailableLanguage): string => {
  return Object.entries(abbrToLangMap)
    .filter((entry) => entry[1] === lang)
    .flat()[0];
};

export function getLangFromAbbr(abbr: string): AvailableLanguage | null {
  if (!abbr) {
    return null;
  }
  const lang = abbrToLangMap[abbr];

  if (!lang) {
    throw new Error(
      `Unknown language abbreviation (${abbr}). Check it's value or add support for new locale.`,
    );
  }

  return lang as AvailableLanguage;
}

export function getAbbrFromLang(lang: AvailableLanguage): string {
  const abbr = langToAbbrMap(lang);

  if (!abbr) {
    throw new Error(
      `Unknown language (${lang}). Check it's value or add support for new locale.`,
    );
  }

  return abbr;
}

const getFallbackLanguageBasedOnEnvironment = (
  reactEnv: string | undefined,
): AvailableLanguage => {
  if (reactEnv === 'prod-et') {
    return 'et';
  }

  if (reactEnv === 'prod-lt') {
    return 'lt';
  }

  if (reactEnv === 'prod-lv') {
    return 'lv';
  }

  return 'en';
};

export const getFormLanguageWithFallback = (
  language: AvailableLanguage,
  reactEnv: string | undefined,
): AvailableLanguage => {
  if (language) return language;

  return getFallbackLanguageBasedOnEnvironment(reactEnv);
};
