import type { GraphQLError } from 'graphql';

// export enum AuthError {
//   MO<PERSON>LE_AUTHENTICATION_FAILURE = 1201,
//   MOBILE_SIGNING_FAILURE = 1202,
//   SSL_CLIENT_NOT_VERIFIED = 1203,
//   INVALID_SSL_CERTIFICATE = 1204,
//   INVALID_USERNAME_OR_PASSWORD = 1205,
//   ID_SIGNING_FAILURE = 1206,
//   INVALID_PERMISSIONS = 1207,
//   SMART_ID_AUTHENTICATION_FAILURE = 1208,
//   SMART_ID_SIGNING_FAILURE = 1209,
//   INVALID_AUTH_HASH = 1210,
//   AUTH_METHOD_NOT_SUPPORTED = 1211,
//   SIGNING_METHOD_NOT_SUPPORTED = 1212,
//   DOCUMENT_SEALING_FAILURE = 1213,
// }

function isObject(obj: unknown): obj is Record<string, any> {
  return typeof obj === 'object' && obj !== null;
}

function hasGraphQLErrors(
  error: unknown,
): error is { graphQLErrors: ReadonlyArray<any> } {
  return (
    isObject(error) &&
    Array.isArray(
      (error as { graphQLErrors: ReadonlyArray<any> }).graphQLErrors,
    )
  );
}

// Extracts errors, and returns error without e
export function extractGraphqlErrors(
  error: unknown,
): [ReadonlyArray<any>, unknown] {
  if (hasGraphQLErrors(error)) {
    const newError = { ...error };
    delete (newError as any).graphQLErrors;
    return [error.graphQLErrors, newError];
  }

  return [[], error];
}

export type ValidationErrors = Record<string, Array<string>>;

export function extractValidationErrors(
  error: unknown,
): [ValidationErrors, unknown] {
  const [graphqlErrors, newError] = extractGraphqlErrors(error);
  if (graphqlErrors.length > 0) {
    if (isObject(newError)) {
      newError.graphQLErrors = graphqlErrors.filter(
        (e) => e.message !== 'validation',
      );
    }

    const validationErrors =
      graphqlErrors.find((e) => e.message === 'validation')?.validation ?? {};
    return [validationErrors, newError];
  }

  return [{}, newError];
}

export function isUnauthorizedError(error: GraphQLError): boolean {
  return error.message === 'Unauthorized';
}
