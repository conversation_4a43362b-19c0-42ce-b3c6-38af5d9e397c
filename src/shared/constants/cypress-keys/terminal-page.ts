export const CypressTerminalKeys = {
  STORE_TYPE_SELECTOR_PHYSICAL_OPTION: 'store-type-selector.physical-option',
  STORE_TYPE_SELECTOR_ONLINE_OPTION: 'store-type-selector.online-option',
  PHY<PERSON>CAL_STORE_SELECTOR: 'physical-store-selector',
  PURCHASE_DETAILS_STEP: 'purchase-details-step',
  PURCHASE_DETAILS_AMOUNT_INPUT: 'purchase-details.amount-input',
  PURCHASE_DETAILS_REFERENCE_INPUT: 'purchase-details.reference-input',
  PURCHASE_DETAILS_DOWN_PAYMENT_INPUT: 'purchase-details.down-payment-input',
  PAYMENT_PLAN_SELECTION_STEP: 'payment-plan-selection-step',
  PAYMENT_PLAN_SELECTION_OPTION: 'payment-plan-selection.option',
  PERSONAL_INFO_STEP: 'personal-info-step',
  PER<PERSON><PERSON><PERSON>_INFO_FIRST_NAME_INPUT: 'personal-info.first-name-input',
  PER<PERSON><PERSON><PERSON>_INFO_LAST_NAME_INPUT: 'personal-info.last-name-input',
  PER<PERSON><PERSON>L_INFO_EMAIL_INPUT: 'personal-info.email-input',
  PERSONAL_INFO_PHONE_NUMBER_INPUT: 'personal-info.phone-number-input',
  PERSONAL_INFO_LANGUAGE_SELECTOR: 'personal-info.language-selector',
  PERSONAL_INFO_LANGUAGE_SELECTOR_OPTION:
    'personal-info.language-selector.option',
  DOCUMENT_IDENTIFICATION_STEP: 'document-identification-step',
  DOCUMENT_IDENTIFICATION_PIN_INPUT: 'document-identification.pin-input',
  DOCUMENT_IDENTIFICATION_DOCUMENT_TYPE_SELECTOR_OPTION:
    'document-identification.document-type-selector.option',
  DOCUMENT_IDENTIFICATION_DOCUMENT_NUMBER_INPUT:
    'document-identification.document-number-input',
  SEND_LINK_STEP: 'send-link-step',
  SEND_LINK_TYPE_SELECTOR_EMAIL_OPTION: 'send-link-type-selector.email-option',
  SEND_LINK_TYPE_SELECTOR_PHONE_NUMBER_OPTION:
    'send-link-type-selector.phone-number-option',
  SEND_LINK_EMAIL_INPUT: 'send-link.email-input',
  SEND_LINK_PHONE_NUMBER_INPUT: 'send-link.phone-number-input',
  SUBMIT_ACTION_BUTTON_SEND_LINK: 'submit-action-button.send-link',
  SUBMIT_ACTION_BUTTON_OPEN_IN_NEW_TAB: 'submit-action-button.open-in-new-tab',
  SUBMIT_ACTION_BUTTON_COPY_LINK: 'submit-action-button.copy-link',
  SUBMIT_ACTION_BUTTON_GENERATE_QR_CODE:
    'submit-action-button.generate-qr-code',
  RESET_BUTTON: 'reset-button',
  RESET_BUTTON_CONFIRM: 'reset-button-confirm',
  PRACTICE_MODE_TOGGLER: 'practice-mode-toggler',
  PRACTICE_MODE_DISCLAIMER: 'practice-mode-disclaimer',
  CALCULATOR_BUTTON: 'calculator-button',
  CALCULATOR_FORM: 'calculator-form',
  CALCULATOR_FORM_AMOUNT_INPUT: 'calculator-form.amount-input',
  CALCULATOR_FORM_PAYMENT_PLAN_OPTION: 'calculator-form.payment-plan-option',
  CALCULATOR_FORM_PAYMENT_PLAN_INFO: 'calculator-form.payment-plan-info',
  CALCULATOR_FORM_PAYMENT_PLAN_EMPTY_INFO:
    'calculator-form.payment-plan-empty-info',
  CALCULATOR_FORM_SUBMIT_BUTTON: 'calculator-form.submit-button',
  CALCULATOR_FORM_CANCEL_BUTTON: 'calculator-form.cancel-button',
} as const;
