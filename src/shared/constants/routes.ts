import queryString, { type ParsedQuery } from 'query-string';
import { useLocation } from 'react-router-dom';
import type {
  ApplicationLinkAction,
  DevToolsRouteTabs,
  LoginMethods,
  RouteConfig,
  SettingsRouteTabs,
} from 'shared/types';

export type LoginRouteParams = {
  method?: LoginMethods;
  redirectUrl?: string;
  sessionExpired?: boolean;
  inviteHash?: string;
};

export const LoginRoute: RouteConfig<LoginRouteParams> = {
  template: '/login',
  format({
    redirectUrl,
    method,
    sessionExpired,
    inviteHash,
  }: LoginRouteParams | undefined = {}) {
    const params = {
      method,
      redirectUrl: redirectUrl && redirectUrl !== '/' ? redirectUrl : undefined,
      sessionExpired,
      inviteHash,
    };

    const query = queryString.stringify(params);
    if (!query) {
      return '/login';
    }
    return `/login?${query}`;
  },
};

export type SignUpRouteParams = {
  inviteHash?: string;
};

export const SignUpRoute: RouteConfig<SignUpRouteParams> = {
  template: '/merchant-invite/:inviteHash',
  format({ inviteHash }: SignUpRouteParams | undefined = {}) {
    return `/merchant-invite/${inviteHash}`;
  },
};

export const ForgotPasswordRoute: RouteConfig<Record<string, never>> = {
  template: '/forgot-password',
  format() {
    return '/forgot-password';
  },
};

export type ResetPasswordRouteParams = {
  token: string;
};

export const ResetPasswordRoute: RouteConfig<ResetPasswordRouteParams> = {
  template: '/reset-password/:token',
  format(params?: ResetPasswordRouteParams) {
    const { token } = params || {};
    return `/reset-password/${token}`;
  },
};

export const TerminalRoute: RouteConfig<Record<string, never>> = {
  template: '/terminal',
  format() {
    return '/terminal';
  },
};

export type TerminalApplicationRouteParams = {
  action: ApplicationLinkAction;
  applicationId: number;
};

export const TerminalApplicationRoute: RouteConfig<TerminalApplicationRouteParams> =
  {
    template: '/terminal/:applicationId',
    format(params?: TerminalApplicationRouteParams) {
      const { applicationId, ...queryParams } = params || {};
      return `/terminal/${applicationId}?${queryString.stringify(queryParams)}`;
    },
  };

export const ApplicationListRoute: RouteConfig<Record<string, never>> = {
  template: '/applications',
  format() {
    return '/applications';
  },
};

export type ApplicationDetailsRouteParams = {
  applicationId: number;
};

export const ApplicationDetailsRoute: RouteConfig<ApplicationDetailsRouteParams> =
  {
    template: '/applications/:applicationId',
    format(params?: ApplicationDetailsRouteParams) {
      const { applicationId } = params || {};
      return `/applications/${applicationId}`;
    },
  };

export const FinanceRoute: RouteConfig<Record<string, never>> = {
  template: '/finances',
  format() {
    return '/finances';
  },
};

export const EmployeesRoute: RouteConfig<Record<string, never>> = {
  template: '/employees',
  format() {
    return '/employees';
  },
};

export const StoresRoute: RouteConfig<Record<string, never>> = {
  template: '/stores',
  format() {
    return '/stores';
  },
};

export const ForDeveloperRoute: RouteConfig<Record<string, never>> = {
  template: '/developer-tools',
  format() {
    return '/developer-tools';
  },
};

export const ForDeveloperSetupRoute: RouteConfig<Record<string, never>> = {
  template: '/developer-tools/setup',
  format() {
    return '/developer-tools/setup';
  },
};

export const ForDeveloperUrlsRoute: RouteConfig<Record<string, never>> = {
  template: '/developer-tools/urls',
  format() {
    return '/developer-tools/urls';
  },
};

export const ForDeveloperCalculatorRoute: RouteConfig<Record<string, never>> = {
  template: '/developer-tools/calculator',
  format() {
    return '/developer-tools/calculator';
  },
};

type DevToolsTabRouteParams = {
  tab: DevToolsRouteTabs;
};

export const DevToolsTabRoute: RouteConfig<DevToolsTabRouteParams> = {
  template: `${ForDeveloperRoute.template}/:tab`,
  format(params?: DevToolsTabRouteParams) {
    const { tab } = params || {};
    return `${ForDeveloperRoute.format()}/${tab}`;
  },
};

export const AccountRoute: RouteConfig<Record<string, never>> = {
  template: '/account',
  format() {
    return '/account';
  },
};

export const SettingsRoute: RouteConfig<Record<string, never>> = {
  template: '/settings',
  format() {
    return '/settings';
  },
};

export type SettingsTabRouteParams = {
  tab: SettingsRouteTabs;
};

export const SettingsTabRoute: RouteConfig<SettingsTabRouteParams> = {
  template: `${SettingsRoute.template}/:tab`,
  format: (params?: SettingsTabRouteParams) => {
    const { tab } = params || {};
    return `${SettingsRoute.format()}/${tab}`;
  },
};

export const useQueryParams = (): ParsedQuery<string | boolean | number> => {
  const location = useLocation();
  return queryString.parse(location.search);
};

export const BusinessLoanRoute: RouteConfig<Record<string, never>> = {
  template: '/business-loan',
  format() {
    return '/business-loan';
  },
};
