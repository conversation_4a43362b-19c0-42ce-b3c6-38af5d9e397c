import { toggleEmployeeInviteModal } from 'modules/settings/employees/invite-employee-modal';
import type { IconType } from 'react-icons';
import { FiUserPlus } from 'react-icons/fi';
import { useLocation } from 'react-router-dom';
import { useAvailableStores, useMerchantUsers } from 'shared/hooks/merchant';
import { SettingsRouteTabs } from 'shared/types';

const PAGE_DATA_BY_TAB: Record<
  SettingsRouteTabs,
  | {
      title?: string;
      actionIcon?: IconType;
      onAction?: () => void;
    }
  | undefined
> = {
  [SettingsRouteTabs.Stores]: {
    title: 'stores.title',
  },
  [SettingsRouteTabs.Employees]: {
    title: 'employees.title',
    actionIcon: FiUserPlus,
    onAction: () => {
      toggleEmployeeInviteModal(true);
    },
  },
  [SettingsRouteTabs.Merchant]: undefined,
  [SettingsRouteTabs.Payment]: undefined,
};

export const useMerchantSettingsTabDataByPathname = () => {
  const { pathname } = useLocation();
  const { users } = useMerchantUsers();
  const { availableStores } = useAvailableStores();

  const tab =
    (pathname.split('/')[2] as SettingsRouteTabs) || SettingsRouteTabs.Merchant;
  const getCount = () => {
    if (tab === SettingsRouteTabs.Employees) {
      return users?.length;
    }

    if (tab === SettingsRouteTabs.Stores) {
      return availableStores?.length;
    }
  };

  const pageData = PAGE_DATA_BY_TAB[tab];

  return {
    tab,
    count: getCount(),
    title: pageData?.title,
    actionIcon: pageData?.actionIcon,
    onAction: pageData?.onAction,
  };
};
