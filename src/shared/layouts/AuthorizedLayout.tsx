import { Box, SimpleGrid } from '@chakra-ui/react';
import { useUnit } from 'effector-react';
import { Suspense } from 'react';
import { Outlet } from 'react-router-dom';
import { Loader } from 'shared/components/Loader';
import { MobileSidebarOverlay, Sidebar } from 'shared/components/sidebar';
import { useIsMobileLayout } from 'shared/hooks/layout';
import { useCurrentUser } from 'shared/hooks/user';
import { $isSidebarOpen } from 'shared/models/sidebar';

export const AuthorizedLayout = () => {
  const { loading } = useCurrentUser();
  const isSidebarOpen = useUnit($isSidebarOpen);
  const isMobileLayout = useIsMobileLayout();

  return (
    <SimpleGrid
      gridTemplateColumns={{
        base: '1fr',
        md: '17rem 1fr',
      }}
      height="100%"
      overflow="hidden"
    >
      <Sidebar />
      <Box
        boxShadow="0px 2px 20px rgba(0, 0, 0, 0.1)"
        height="100%"
        overflow="auto"
        position="relative"
        transform={{
          base: `translateX(${isSidebarOpen ? '17rem' : '0'})`,
          md: 'unset',
        }}
        transition="0.2s all"
      >
        {!!isMobileLayout && <MobileSidebarOverlay />}
        <Suspense fallback={<Loader />}>
          {loading ? <Loader /> : <Outlet />}
        </Suspense>
      </Box>
    </SimpleGrid>
  );
};
