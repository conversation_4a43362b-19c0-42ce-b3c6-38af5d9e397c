query merchantApplication($applicationId: Int!) {
  merchant_application(application_id: $applicationId) {
    id
    schedule_type
    rejected_at
    signed_at
    is_test
    purchase_url
    from_retail
    credit_info {
      cashier_bonus_amount
    }
  }
}

query merchantApplications(
  $merchantId: Int!
  $createdByUserId: Int
  $limit: Int
  $page: Int
  $orderBy: MerchantApplicationsOrderBy
  $direction: Direction
  $merchantReference: String
  $merchantRefOrUserName: String
  $statuses: [admin_application_statuses]
  $dateRange: DateRange
  $balanceAtTimestamp: Int
  $userName: String
  $scheduleTypes: [ApplicationScheduleType]
) {
  applications: merchant_applications(
    merchant_id: $merchantId
    created_by_user_id: $createdByUserId
    limit: $limit
    page: $page
    orderBy: $orderBy
    direction: $direction
    merchant_reference: $merchantReference
    merchant_ref_or_user_name: $merchantRefOrUserName
    statuses: $statuses
    date_range: $dateRange
    balance_at_timestamp: $balanceAtTimestamp
    user_name: $userName
    schedule_types: $scheduleTypes
  ) {
    data {
      id
      schedule_type
      for_private_person
      rejected_at
      signed_at
      status
      created_at
      processed_at
      requested_amount
      invoice_reference_nr
      from_retail
      simple_eligibility_status
      user_id
      schedule_type
      user_info {
        first_name
        last_name
      }
      legal_person_info {
        name
      }
      application_reference {
        creator {
          profile {
            first_name
            last_name
          }
        }
      }
      merchant_data {
        reference
        store {
          name
        }
      }
    }
    total
    has_more_pages
  }
}

fragment ApplicationCreditInfoBasic on ApplicationCreditInfo {
  net_total
  total_expected
  period_months
  irr
  monthly_payment
  management_fee
  contract_fee
  merchant_financing_pct
  technology_cost_pct
  bonus_pct
  bonus_type
  cashier_bonus_amount
}

fragment MerchantDataBasic on ApplicationMerchantData {
  reference
  store {
    name
  }
}

fragment ApplicationBasic on Application {
  type
  id
  user_id
  for_private_person
  invoice_reference_nr
  requested_amount
  signed_at
  processed_at
  status
  created_at
  cancelled_at
  rejected_at
  bonus_paid_at
  from_retail
  schedule_type
  simple_eligibility_status
  is_refundable
  refunded_amount
  application_reference {
    short_reference
    creator {
      profile {
        first_name
        last_name
      }
    }
  }
  user_info {
    first_name
    last_name
  }
  credit_info {
    ...ApplicationCreditInfoBasic
  }
  merchant_data {
    ...MerchantDataBasic
  }
}

query merchantApplicationDetails($applicationId: Int!) {
  application: merchant_application(application_id: $applicationId) {
    ...ApplicationBasic
    purchase_url
    eligibility_state
    initial_paid_at
    merchant_id
    merchant {
      settings {
        net_total_min
        net_total_max
      }
    }
    klix_payments {
      payer_iban
    }
    legal_person_info {
      name
      legal_person_score_id
      legal_person_score {
        legal_person {
          registry_code
        }
      }
    }
    credit_info {
      ...ApplicationCreditInfoBasic
      credit_income
      merchant_monthly_payment
      merchant_credit_income
      technology_cost_amount
      merchant_irr
      down_payment
      merchant_initial_amount
      merchant_financing_amount
      merchant_bonus_amount
    }
    first_installment {
      application_id
      due_at
    }
    last_installment {
      due_at
      application_id
    }
    installments {
      id
      due_at
      amount
      paid
      type
    }
    buyback_settings {
      bought_back_at
      buyback_amount
      buyback_days
      buyback_discount_pct
    }
    cancelled_at
    cancellation_request {
      id
      application_id
      reason
      created_at
      creator {
        profile {
          first_name
          last_name
        }
      }
      merchant_creator {
        name
      }
    }
    latest_modification_request {
      id
      application_id
      new_requested_amount
      reason
      handled_at
      created_at
      creator {
        profile {
          first_name
          last_name
        }
      }
      merchant_creator {
        name
      }
    }
    direct_payment_refunds {
      id
      application_id
      amount
      status
      refunded_at
      cancelled_at
      created_at
    }
    modification_requests {
      id
      new_requested_amount
      creator {
        profile {
          first_name
          last_name
        }
      }
      merchant_creator {
        name
      }
      reason
      handled_at
    }
  }
}

query CashierApplicationSalesBonus($merchantId: Int!) {
  cashier_bonus: cashier_application_sales(merchant_id: $merchantId) {
    all_time_bonus_paid
    all_time_bonus_unpaid
    all_time_bonus_amount
  }
}

mutation UpdateMerchantApplication($applicationId: Int!, $reference: String!) {
  application: merchant_update_application(
    application_id: $applicationId
    reference: $reference
  ) {
    id
    merchant_data {
      ...MerchantDataBasic
    }
  }
}

mutation ModifyApplicationAmount(
  $applicationId: Int!
  $newRequestedAmount: Float!
  $reason: String!
) {
  request: store_application_modification_request(
    application_id: $applicationId
    new_requested_amount: $newRequestedAmount
    reason: $reason
  ) {
    id
    application_id
    new_requested_amount
    reason
    handled_at
  }
}

mutation CancelApplication($applicationId: Int!, $reason: String!) {
  request: store_application_cancellation_request(
    application_id: $applicationId
    reason: $reason
  ) {
    id
    application_id
    reason
  }
}

mutation RefundDirectPayment(
  $applicationId: Int!
  $refundAmount: Float!
  $paymentProvider: String!
) {
  request: create_direct_payment_refund_request(
    application_id: $applicationId
    amount: $refundAmount
    payment_provider: $paymentProvider
  ) {
    id
    amount
    status
    refunded_at
    cancelled_at
  }
}
