mutation cashierApplication(
  $merchantId: Int!
  $amount: Float!
  $merchantDownPayment: Float!
  $reference: String!
  $message: String
  $fromRetail: Boolean!
  $phone: String
  $specialSettings: cashier_application_special_settings!
  $pin: String
  $documentType: UserDocumentType
  $documentNr: String
  $storeId: Int
  $isTest: Boolean
  $signingMethod: signing_method
  $scheduleType: ApplicationScheduleType
  $firstName: String
  $lastName: String
  $email: String
  $languageAbbr: String
) {
  application: cashier_application(
    merchant_id: $merchantId
    amount: $amount
    merchant_down_payment: $merchantDownPayment
    reference: $reference
    message: $message
    from_retail: $fromRetail
    phone: $phone
    special_settings: $specialSettings
    pin: $pin
    document_type: $documentType
    document_nr: $documentNr
    store_id: $storeId
    is_test: $isTest
    signing_method: $signingMethod
    schedule_type: $scheduleType
    first_name: $firstName
    last_name: $lastName
    email: $email
    language_abbr: $languageAbbr
  ) {
    id
    purchase_url
  }
}

mutation sendPurchaseUrl(
  $applicationId: Int!
  $type: UserMessageSentType
  $phone: String
  $email: String
) {
  sent: send_purchase_url(
    application_id: $applicationId
    type: $type
    phone: $phone
    email: $email
  )
}
