fragment CalcSkinBasic on MerchantCalculatorSkin {
  merchant_id
  main
  secondary
  text
  period
  active_period
  period_text
  monthly_text
  button
  button_text
}

fragment CampaignBasic on MerchantCampaign {
  is_active
  regular_hp_enabled
  converting_schedule_enabled
  converting_schedule_name
  converting_schedule_logo_url
  converting_schedule_net_total_min
  converting_schedule_net_total_max
  converting_schedule_months
  converting_schedule_regular_months
  converting_schedule_reverse_kickback_pct
  pay_later_enabled
  pay_later_name
  pay_later_logo_url
  pay_later_net_total_min
  pay_later_net_total_max
  pay_later_reverse_kickback_pct
  esto_pay_enabled
  regular_hp_enabled
  esto_pay_enabled
  esto_pay_name
  esto_pay_logo_url
  esto_pay_net_total_min
  esto_pay_net_total_max
  fixed_annual_pct_rate
  fixed_management_fee
  fixed_contract_fee
  converting_schedule_fixed_contract_fee
  direct_payment_gateways {
    enabled
    provider
    fee_fixed
    fee_pct
    fee_total_min
    fee_total_max
  }
}

fragment MerchantBasic on Merchant {
  id
  shop_id
  name
  return_url
  notification_url
  cancel_url
  home_url
  logo_path
  registry_code
  phone
  address
  iban
  beneficiary_name
  cashier_loyalty_enabled
  email
  secret_key
  settings {
    merchant_financing_pct
    net_total_min
    net_total_max
    can_create_small_loan
    can_create_dynamic_loan
    can_enable_special_settings
    can_see_eligibility_status
    buyback_guarantee_days
    buyback_discount_pct
    bonus_pct
    bonus_type
    reverse_kickback_pct
    max_amount_id_verification_not_required
    min_months_period
    max_months_period
    cashier_bonus_pct
  }
  campaign {
    ...CampaignBasic
  }
  calculator_skin {
    ...CalcSkinBasic
  }
}

fragment MerchantStoreBasic on MerchantStore {
  id
  name
  cashiers {
    id
    email
    profile {
      first_name
      last_name
    }
  }
}

query Merchant($merchantId: Int!) {
  merchant(merchant_id: $merchantId) {
    ...MerchantBasic
    users {
      id
      merchant_permission_bits
      send_emails
      email
      profile {
        first_name
        last_name
      }
    }
    stores {
      ...MerchantStoreBasic
    }
  }
}

query MerchantInvite($inviteHash: String!) {
  merchant_invite(invite_hash: $inviteHash) {
    merchant_id
    hash
    permission_bits
    email
  }
}

query Pricing($keys: [String]!) {
  pricing(keys: $keys) {
    key
    value
    type
  }
}

mutation UpdateMerchant(
  $merchantId: Int!
  $name: String!
  $registry_code: String!
  $address: String!
  $phone: String!
  $email: String!
  $iban: String!
  $beneficiary_name: String!
  $return_url: String
  $notification_url: String
  $cancel_url: String
  $home_url: String
) {
  merchant: update_merchant(
    merchant_id: $merchantId
    name: $name
    registry_code: $registry_code
    address: $address
    phone: $phone
    email: $email
    iban: $iban
    beneficiary_name: $beneficiary_name
    return_url: $return_url
    notification_url: $notification_url
    cancel_url: $cancel_url
    home_url: $home_url
  ) {
    ...MerchantBasic
  }
}

mutation CreateMerchantStore($merchantId: Int!, $name: String!) {
  store: store_merchant_store(merchant_id: $merchantId, name: $name) {
    ...MerchantStoreBasic
  }
}

mutation UpdateMerchantStore($storeId: Int!, $name: String!) {
  store: update_merchant_store(store_id: $storeId, name: $name) {
    ...MerchantStoreBasic
  }
}

mutation UpdateMerchantUserPermissions(
  $merchantId: Int!
  $userId: Int!
  $permissionBits: Int!
) {
  update_merchant_user_permissions(
    merchant_id: $merchantId
    user_id: $userId
    permission_bits: $permissionBits
  )
}

mutation DetachUserMerchant($merchantId: Int!, $userId: Int!) {
  handle_user_merchant(
    merchant_id: $merchantId
    user_id: $userId
    attach: false
  )
}

mutation DeleteMerchantStore($storeId: Int!) {
  deleted: delete_merchant_store(store_id: $storeId)
}

mutation UpdateMerchantPaymentMethods(
  $merchantId: Int!
  $regularHpEnalbed: Boolean!
  $convertingScheduleEnabled: Boolean!
  $payLaterEnabled: Boolean!
  $estoPayEnabled: Boolean!
) {
  campaign: update_merchant_payment_methods(
    merchant_id: $merchantId
    regular_hp_enabled: $regularHpEnalbed
    converting_schedule_enabled: $convertingScheduleEnabled
    pay_later_enabled: $payLaterEnabled
    esto_pay_enabled: $estoPayEnabled
  ) {
    ...CampaignBasic
  }
}

mutation SendMerchantUserInvite(
  $merchantId: Int!
  $email: String!
  $permissionBits: Int!
) {
  send_merchant_invite(
    merchant_id: $merchantId
    email: $email
    permission_bits: $permissionBits
  )
}

mutation GenerateSecretKey($merchantId: Int!) {
  key: generate_secret_key(merchant_id: $merchantId)
}

mutation UpdateCalculatorSkin(
  $merchant_id: Int!
  $main: String!
  $secondary: String!
  $text: String!
  $period: String!
  $active_period: String!
  $period_text: String!
  $monthly_text: String!
  $button: String!
  $button_text: String!
) {
  skin: update_merchant_calculator_skin(
    merchant_id: $merchant_id
    main: $main
    secondary: $secondary
    text: $text
    period: $period
    active_period: $active_period
    period_text: $period_text
    monthly_text: $monthly_text
    button: $button
    button_text: $button_text
  ) {
    ...CalcSkinBasic
  }
}

mutation StoreUserFromInviteByPassword(
  $inviteHash: String!
  $email: String!
  $pin: String!
  $firstName: String!
  $lastName: String!
  $password: String!
  $confirmPassword: String!
) {
  success: store_user_from_merchant_invite(
    invite_hash: $inviteHash
    email: $email
    pin: $pin
    first_name: $firstName
    last_name: $lastName
    password: $password
    confirm_password: $confirmPassword
  )
}

mutation ForwardLoan(
  $applicationId: Int
  $creditAccountId: Int
  $merchantId: Int
) {
  forward_loan(
    application_id: $applicationId
    credit_account_id: $creditAccountId
    merchant_id: $merchantId
  )
}
