import { useCallback } from 'react';
import { useUpdateUserLanguageMutation } from 'shared/api';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { useCurrentUser } from 'shared/hooks/user/useCurrentUser';
import type { AvailableLanguage } from 'shared/types';
import { getAbbrFromLang } from 'shared/utils';

export const useUpdateUserLanguage = () => {
  const handleGenericError = useHandleGenericError();
  const [updateLanguageMutation] = useUpdateUserLanguageMutation({
    onError: (error) => {
      handleGenericError(error);
    },
  });
  const { user } = useCurrentUser();

  return useCallback(
    async (lang: AvailableLanguage) => {
      if (!user?.id) {
        throw new Error('Missing user ID');
      }

      await updateLanguageMutation({
        variables: {
          languageAbbr: getAbbrFromLang(lang),
          userId: user.id,
        },
      });
    },
    [updateLanguageMutation, user?.id],
  );
};
