import { useApolloClient } from '@apollo/client';
import { useNavigate } from 'react-router-dom';
import { useLogoutMutation } from 'shared/api';
import { LoginRoute } from 'shared/constants/routes';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { authModel } from 'shared/models/auth';

export const useLogout = () => {
  const apolloClient = useApolloClient();
  const handleGenericError = useHandleGenericError();
  const navigate = useNavigate();

  const [logOutMutation] = useLogoutMutation();

  return async () => {
    try {
      await logOutMutation();
      authModel.setUserEv(null);
      await apolloClient.clearStore();
      navigate(LoginRoute.format());
    } catch (e) {
      handleGenericError(e);
    }
  };
};
