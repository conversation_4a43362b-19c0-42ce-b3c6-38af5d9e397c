import { useContext, useMemo } from 'react';
import { useCurrentUser } from 'shared/hooks/user';
import { MerchantPermissions, UserPermissions } from 'shared/types';
import { hasPermissions } from 'shared/utils';

import { useAssignedMerchants } from '../merchant';
import { GlobalStateContext } from '../state';

export const useMerchantPermissionBits = () => {
  const { merchantId } = useContext(GlobalStateContext);
  const merchants = useAssignedMerchants();

  const selectedMerchant = useMemo(
    () => merchants?.find((merch) => merch.id === merchantId) || null,
    [merchantId, merchants],
  );

  return selectedMerchant?.merchant_permission_bits;
};

export const useIsSuperAdmin = () => {
  const { user } = useCurrentUser();

  const permissionBits = user?.permission_bits;

  return (
    !!permissionBits && hasPermissions(permissionBits, [UserPermissions.Admin])
  );
};

export const useIsCashier = () => {
  const permissionBits = useMerchantPermissionBits();

  return (
    !!permissionBits &&
    hasPermissions(permissionBits, [MerchantPermissions.Cashier])
  );
};

export const useCheckRolePermission = () => {
  const isUserSuperAdmin = useIsSuperAdmin();

  const merchantPermissionBits = useMerchantPermissionBits();

  return (permissions: Array<UserPermissions | MerchantPermissions>) => {
    if (isUserSuperAdmin) {
      return true;
    }

    if (merchantPermissionBits) {
      return hasPermissions(merchantPermissionBits, permissions);
    }

    return false;
  };
};

export const useIsAllowedPermission = (
  permissions: Array<MerchantPermissions>,
) => {
  const isUserSuperAdmin = useIsSuperAdmin();

  const merchantPermissionBits = useMerchantPermissionBits();

  if (isUserSuperAdmin) {
    return true;
  }

  if (merchantPermissionBits) {
    return hasPermissions(merchantPermissionBits, permissions);
  }

  return false;
};
