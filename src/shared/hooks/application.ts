import { NetworkStatus } from '@apollo/client';
import { useCallback, useContext, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  type CancelApplicationMutationVariables,
  MerchantApplicationDetailsDocument,
  type ModifyApplicationAmountMutationVariables,
  type RefundDirectPaymentMutationVariables,
  type UpdateMerchantApplicationMutationVariables,
  useCancelApplicationMutation,
  useCashierApplicationSalesBonusQuery,
  useMerchantApplicationDetailsQuery,
  useMerchantApplicationQuery,
  useModifyApplicationAmountMutation,
  useRefundDirectPaymentMutation,
  useUpdateMerchantApplicationMutation,
} from 'shared/api';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { useCheckRegion } from 'shared/hooks/app';
import { GlobalStateContext } from 'shared/hooks/state';
import {
  useCheckRolePermission,
  useCurrentUser,
  useMerchantPermissionBits,
} from 'shared/hooks/user';
import { getDefaultRoleRedirect } from 'shared/lib';
import type { MerchantPermissions, MutationConfig } from 'shared/types';
import { type AppRegions, extractValidationErrors } from 'shared/utils';

export function useApplicationId(): number {
  const params = useParams<{ applicationId: string }>();

  if (typeof params.applicationId === 'string') {
    return Number.parseInt(params.applicationId);
  }

  throw new Error('Failed to parse applicationId');
}

export function useMerchantApplication(applicationId: number) {
  const { loading, data, startPolling, stopPolling } =
    useMerchantApplicationQuery({
      variables: {
        applicationId,
      },
    });

  useEffect(() => {
    startPolling(5000);
    return () => {
      stopPolling();
    };
  }, [startPolling, stopPolling]);

  const isPending =
    (data?.merchant_application?.rejected_at ?? null) === null &&
    (data?.merchant_application?.signed_at ?? null) === null;

  useEffect(() => {
    if (!isPending) {
      stopPolling();
    }
  }, [isPending, stopPolling]);

  return { loading, data };
}

export function useMerchantApplicationDetails(applicationId: number) {
  const { loading, data } = useMerchantApplicationDetailsQuery({
    variables: {
      applicationId,
    },
  });

  return { loading, data };
}

export function useCurrentMerchantApplicationDetails() {
  const applicationId = useApplicationId();
  return useMerchantApplicationDetails(applicationId);
}

export function useUpdateMerchantApplication({
  onFieldValidationError,
}: MutationConfig = {}) {
  const [updateMerchantApplicationMutation, { loading, data }] =
    useUpdateMerchantApplicationMutation();
  const handleGenericError = useHandleGenericError();

  const updateMerchantApplication = useCallback(
    async (variables: UpdateMerchantApplicationMutationVariables) => {
      try {
        return await updateMerchantApplicationMutation({
          variables,
          update(cache) {
            cache.modify({
              id: cache.identify({
                id: variables.applicationId,
                __typename: 'MerchantApplicationIndex',
              }),
              fields: {
                merchant_data(existing) {
                  return (
                    existing && { ...existing, reference: variables.reference }
                  );
                },
              },
            });
          },
        });
      } catch (e) {
        const [validationErrors, newError] = extractValidationErrors(e);
        if (
          Object.keys(validationErrors).length > 0 &&
          onFieldValidationError
        ) {
          for (const field of Object.keys(validationErrors)) {
            onFieldValidationError(field);
          }
        } else {
          handleGenericError(newError);
        }
        return null;
      }
    },
    [
      handleGenericError,
      updateMerchantApplicationMutation,
      onFieldValidationError,
    ],
  );

  return { isLoading: loading, isCompleted: !!data, updateMerchantApplication };
}

export function useModifyApplicationAmount({
  onFieldValidationError,
}: MutationConfig = {}) {
  const [modifyApplicationAmountMutation, { loading }] =
    useModifyApplicationAmountMutation();
  const handleGenericError = useHandleGenericError();

  const modifyApplicationAmount = useCallback(
    async ({
      applicationId,
      newRequestedAmount,
      reason,
    }: ModifyApplicationAmountMutationVariables) => {
      try {
        return await modifyApplicationAmountMutation({
          variables: {
            applicationId,
            newRequestedAmount,
            reason,
          },
          refetchQueries: [
            {
              query: MerchantApplicationDetailsDocument,
              variables: { applicationId },
            },
          ],
          awaitRefetchQueries: true,
        });
      } catch (e) {
        const [validationErrors, newError] = extractValidationErrors(e);
        if (
          Object.keys(validationErrors).length > 0 &&
          onFieldValidationError
        ) {
          for (const field of Object.keys(validationErrors)) {
            onFieldValidationError(field);
          }
        } else {
          handleGenericError(newError);
        }
        return null;
      }
    },
    [
      handleGenericError,
      modifyApplicationAmountMutation,
      onFieldValidationError,
    ],
  );

  return { isLoading: loading, modifyApplicationAmount };
}

export function useCancelApplication({
  onFieldValidationError,
}: MutationConfig = {}) {
  const [cancelApplicationMutation, { loading }] =
    useCancelApplicationMutation();
  const handleGenericError = useHandleGenericError();

  const cancelApplication = useCallback(
    async (variables: CancelApplicationMutationVariables) => {
      try {
        return await cancelApplicationMutation({
          variables,
          refetchQueries: [
            {
              query: MerchantApplicationDetailsDocument,
              variables: { applicationId: variables.applicationId },
            },
          ],
          awaitRefetchQueries: true,
        });
      } catch (e) {
        const [validationErrors, newError] = extractValidationErrors(e);
        if (
          Object.keys(validationErrors).length > 0 &&
          onFieldValidationError
        ) {
          for (const field of Object.keys(validationErrors)) {
            onFieldValidationError(field);
          }
        } else {
          handleGenericError(newError);
        }
        return null;
      }
    },
    [handleGenericError, cancelApplicationMutation, onFieldValidationError],
  );

  return { isLoading: loading, cancelApplication };
}

export function useRequestRefundApplication({
  onFieldValidationError,
}: MutationConfig = {}) {
  const [RefundDirectPaymentMutation, { loading }] =
    useRefundDirectPaymentMutation();
  const handleGenericError = useHandleGenericError();

  const requestRefundApplication = useCallback(
    async (variables: RefundDirectPaymentMutationVariables) => {
      try {
        return await RefundDirectPaymentMutation({
          variables,
          refetchQueries: [
            {
              query: MerchantApplicationDetailsDocument,
              variables: { applicationId: variables.applicationId },
            },
          ],
          awaitRefetchQueries: true,
        });
      } catch (e) {
        const [validationErrors, newError] = extractValidationErrors(e);
        if (
          Object.keys(validationErrors).length > 0 &&
          onFieldValidationError
        ) {
          for (const field of Object.keys(validationErrors)) {
            onFieldValidationError(field);
          }
        } else {
          handleGenericError(newError);
        }
        return null;
      }
    },
    [handleGenericError, RefundDirectPaymentMutation, onFieldValidationError],
  );

  return { isLoading: loading, requestRefundApplication };
}

export const useCashierApplicationSalesBonus = () => {
  const { merchantId } = useContext(GlobalStateContext);

  return useCashierApplicationSalesBonusQuery({
    skip: !merchantId,
    variables: { merchantId: merchantId ?? -1 },
  });
};

export const useDefaultRedirectPathname = () => {
  const { user, networkStatus } = useCurrentUser({
    notifyOnNetworkStatusChange: true,
  });

  const merchantPermissionBits = useMerchantPermissionBits();

  if (networkStatus !== NetworkStatus.ready) {
    return null;
  }

  return getDefaultRoleRedirect(user?.permission_bits, merchantPermissionBits);
};

export const useRoleAndRegionCheck = () => {
  const checkRegion = useCheckRegion();
  const checkRole = useCheckRolePermission();

  return ({
    roles,
    regions,
  }: {
    roles?: Array<MerchantPermissions>;
    regions?: Array<AppRegions>;
  }) => {
    const isValidPermission = !roles || checkRole(roles);
    const isValidRegion = !regions || checkRegion(regions);

    return isValidPermission && isValidRegion;
  };
};
