import { useCurrentUser } from 'shared/hooks/user';
import { region } from 'shared/lib';
import type { AppRegions } from 'shared/utils';

export const useCheckRegion = () => {
  const user = useCurrentUser();

  return (allowedRegions: Array<AppRegions>) =>
    !!user && allowedRegions.includes(region as AppRegions);
};

export const useIsRegion = (allowedRegions: Array<AppRegions>) => {
  const user = useCurrentUser();

  return !!user && allowedRegions.includes(region as AppRegions);
};
