import type { ApolloError } from '@apollo/client';
import type { LoginHookParams } from 'modules/auth/login/types';
import { useState } from 'react';
import { useStoreUserFromInviteByPasswordMutation } from 'shared/api';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { extractGraphqlErrors } from 'shared/utils/graphql';

export function usePasswordSignUp({ onSuccess }: LoginHookParams) {
  const [error, setError] = useState<ApolloError | Error | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<
    string,
    string
  > | null>(null);
  const handleGenericError = useHandleGenericError();
  const [signUpWithPassword, { loading }] =
    useStoreUserFromInviteByPasswordMutation({
      onCompleted: (data) => {
        if (data.success) {
          onSuccess();
        } else {
          setError(new Error('Wrong data'));
        }
      },
      onError: (err) => {
        handleGenericError(err);
        const [graphqlerrors] = extractGraphqlErrors(err);
        if (graphqlerrors[0].validation) {
          setValidationErrors(graphqlerrors[0].validation);
        }
        setError(err);
      },
    });

  const resetValidationErrors = () => {
    setValidationErrors(null);
  };

  return {
    isLoading: loading,
    error,
    validationErrors,
    resetValidationErrors,
    signUpWithPassword,
  };
}
