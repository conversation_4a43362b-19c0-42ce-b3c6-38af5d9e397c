import { createContext } from 'react';
import type { GlobalStateType } from 'shared/types/state';

type GlobalStateContextType = {
  setMerchantId: (id: number | null) => void;
  setIsVisitedInvitePage: (visitInvitePage: string) => void;
} & GlobalStateType;

export const GlobalStateContext = createContext<GlobalStateContextType>({
  merchantId: null,
  setMerchantId: () => {},
  // uses only if user visit merchant-invite page
  isVisitedInvitePage: null,
  setIsVisitedInvitePage: () => {},
});
