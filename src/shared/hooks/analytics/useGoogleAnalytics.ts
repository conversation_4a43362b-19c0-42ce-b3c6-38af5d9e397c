import { useCallback, useEffect, useState } from 'react';
import ReactGA from 'react-ga4';
import { useCurrentUser } from 'shared/hooks/user';
import { googleAnalyticsId, isProd } from 'shared/lib/env';

export const isGoogleAnalyticsEnabled = Boolean(googleAnalyticsId);

export const useGoogleAnalytics = () => {
  const [isInitialized, setIsInitialized] = useState(false);

  const { user } = useCurrentUser();

  const initialize = useCallback(() => {
    if (!googleAnalyticsId || isInitialized) return;

    ReactGA.initialize(googleAnalyticsId, {
      testMode: !isProd,
      gtagOptions: {
        debug_mode: !isProd,
      },
    });

    setIsInitialized(true);
  }, [isInitialized]);

  const identifyUser = useCallback(
    (userData: typeof user) => {
      if (!isInitialized || !userData) return;

      ReactGA.set({
        user_id: userData.id.toString(),
        custom_map: {
          dimension1: userData.email ?? 'No User Email',
          dimension2: !(
            !userData.profile?.first_name && !userData.profile?.last_name
          )
            ? `${userData.profile?.first_name ?? ''} ${userData.profile?.last_name ?? ''}`.trimStart()
            : 'No User Name',
        },
      });
    },
    [isInitialized],
  );

  useEffect(() => {
    initialize();
  }, []);

  useEffect(() => {
    if (!user) return;
    identifyUser(user);
  }, [user, identifyUser]);

  return {
    isInitialized,
    identifyUser,
  };
};
