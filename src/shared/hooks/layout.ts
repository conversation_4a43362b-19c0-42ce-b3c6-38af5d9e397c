import { useMediaQuery, useToken } from '@chakra-ui/react';
import { type FunctionComponent, useMemo } from 'react';

export const useIsMobileLayout = (): boolean => {
  const mdBreakpoint = useToken('breakpoints', 'md');
  const [isDesktop] = useMediaQuery(`(min-width: ${mdBreakpoint})`);
  return !isDesktop;
};

export const useLayoutAwareElement = <Props>(
  mobileEl: FunctionComponent<Props>,
  desktopEl: FunctionComponent<Props>,
  breakpoint = 'sm',
): FunctionComponent<Props> => {
  const breakpointValue = useToken('breakpoints', breakpoint);
  const [isLargerThanBreakpoint] = useMediaQuery(
    `(min-width: ${breakpointValue})`,
  );

  return useMemo(
    () => (isLargerThanBreakpoint ? desktopEl : mobileEl),
    [isLargerThanBreakpoint, mobileEl, desktopEl],
  );
};
