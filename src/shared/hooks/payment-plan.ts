import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationScheduleType } from 'shared/api';

import { useMerchantDetails } from './merchant';

export type PaymentMethodInfo = {
  name?: string;
  logoUrl?: string;
  isEnabled: boolean;
  period: {
    from: number;
    to: number;
    text: string;
  } | null;
};

export function usePaymentMethodInfo(
  method: ApplicationScheduleType,
): PaymentMethodInfo {
  const { t } = useTranslation('terminal');
  const { data: merchantData } = useMerchantDetails();
  const settings = merchantData?.merchant?.settings;
  const campaign = merchantData?.merchant?.campaign;

  const name = useMemo(() => {
    switch (method) {
      case ApplicationScheduleType.REGULAR:
        return t('payment-plan.regular');
      case ApplicationScheduleType.ESTO_X:
        return campaign?.converting_schedule_name || '';
      case ApplicationScheduleType.PAY_LATER:
        return campaign?.pay_later_name || '';
      case ApplicationScheduleType.ESTO_PAY:
        return campaign?.esto_pay_name || '';
      default:
    }
  }, [t, campaign, method]);

  const logoUrl = useMemo(() => {
    switch (method) {
      case ApplicationScheduleType.REGULAR:
        return merchantData?.merchant?.logo_path ?? '';
      case ApplicationScheduleType.ESTO_X:
        return (
          merchantData?.merchant?.campaign?.converting_schedule_logo_url ?? ''
        );
      case ApplicationScheduleType.PAY_LATER:
        return merchantData?.merchant?.campaign?.pay_later_logo_url ?? '';
      case ApplicationScheduleType.ESTO_PAY:
        return merchantData?.merchant?.campaign?.esto_pay_logo_url ?? '';
      default:
    }
  }, [merchantData, method]);

  const period = useMemo(() => {
    if (method === ApplicationScheduleType.ESTO_PAY) {
      return null;
    }

    let from = 0;
    let to = 0;

    switch (method) {
      case ApplicationScheduleType.REGULAR:
        from = settings?.min_months_period ?? 0;
        to = settings?.max_months_period ?? 0;
        break;
      case ApplicationScheduleType.ESTO_X:
        from = to = campaign?.converting_schedule_months ?? 0;
        break;
      case ApplicationScheduleType.PAY_LATER:
        from = to = 1;
        break;
      default:
    }

    return {
      from,
      to,
      text: t('payment-plan.period', {
        period: from === to ? from : `${from}-${to}`,
        count: to,
      }),
    };
  }, [method, t, campaign, settings]);

  const isEnabled = useMemo(() => {
    switch (method) {
      case ApplicationScheduleType.REGULAR:
        return campaign?.regular_hp_enabled ?? false;
      case ApplicationScheduleType.ESTO_X:
        return campaign?.converting_schedule_enabled ?? false;
      case ApplicationScheduleType.PAY_LATER:
        return campaign?.pay_later_enabled ?? false;
      case ApplicationScheduleType.ESTO_PAY:
        return campaign?.esto_pay_enabled ?? false;
      default:
        return false;
    }
  }, [method, campaign]);

  return {
    name,
    logoUrl,
    isEnabled,
    period,
  };
}
