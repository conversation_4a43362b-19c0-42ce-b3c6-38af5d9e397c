import {
  Box,
  Center,
  HStack,
  SimpleGrid,
  Spinner,
  Text,
} from '@chakra-ui/react';
import {
  type FunctionComponent,
  type ReactNode,
  Suspense,
  useMemo,
} from 'react';
import { useTranslation } from 'react-i18next';
import { useIsMobileLayout } from 'shared/hooks/layout';
import { affiliateContacts, customerContacts } from 'shared/lib';

const contacts = [
  {
    type: 'affiliate',
    ...affiliateContacts,
  },
  {
    type: 'customer',
    ...customerContacts,
  },
];

type LoggedOutLayoutProps = {
  isLoading?: boolean;
  children?: ReactNode;
  mobileHeader: FunctionComponent<any>;
  desktopHeader: FunctionComponent<any>;
};

const LoadingIndicator = (): JSX.Element => (
  <Center height="100%" width="100%">
    <Spinner
      boxSize="2rem"
      color="primary.800"
      emptyColor="neutral.100"
      thickness="3px"
    />
  </Center>
);

export const LoggedOutLayout = ({
  isLoading,
  children,
  mobileHeader,
  desktopHeader,
}: LoggedOutLayoutProps) => {
  const isMobileLayout = useIsMobileLayout();
  const Header = useMemo(
    () => (isMobileLayout ? mobileHeader : desktopHeader),
    [isMobileLayout, mobileHeader, desktopHeader],
  );
  const { t } = useTranslation(['login', 'common']);

  return (
    <SimpleGrid
      gridTemplateAreas="'header' 'body' 'footer'"
      gridTemplateRows="auto 1fr auto"
      h="100%"
      overflow="auto"
      position="relative"
    >
      <Box
        as="header"
        bg="white"
        flexShrink={0}
        gridArea="header"
        left={0}
        position="sticky"
        top={0}
        zIndex={2}
      >
        <Header />
      </Box>
      <Box gridArea="body" px={5}>
        <Suspense fallback={<LoadingIndicator />}>
          {isLoading ? (
            <LoadingIndicator />
          ) : (
            <Box flexGrow={1} height="100%" mx="auto" width={['100%', '400px']}>
              {children}
            </Box>
          )}
        </Suspense>
      </Box>
      <Box
        as="footer"
        flexShrink={0}
        gridArea="footer"
        pb={[5, 8]}
        pt={0}
        px={5}
      >
        <HStack
          alignItems="flex-start"
          mx="auto"
          spacing={3}
          width={['100%', '400px']}
        >
          {contacts.map(({ type, email, phone }) => (
            <Box
              bg="neutral.50"
              borderRadius={4}
              flexBasis={1}
              flexGrow={1}
              key={type}
              px={5}
              py={4}
            >
              <Text mb={1} textStyle="body2-highlight">
                {t(`common:support.${type}`)}
              </Text>
              <Text mb={1} textStyle="body2">
                {email}
              </Text>
              <Text mb={1} textStyle="body2">
                {phone}
              </Text>
            </Box>
          ))}
        </HStack>
      </Box>
    </SimpleGrid>
  );
};
