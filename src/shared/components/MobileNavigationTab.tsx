import { Button, Icon } from '@chakra-ui/react';
import { FiChevronRight } from 'react-icons/fi';
import { Link } from 'react-router-dom';

type Props = {
  to: string;
  label: string;
};

export const MobileNavigationTab = ({ to, label }: Props) => {
  return (
    <Link to={to}>
      <Button
        _active={{ bg: 'neutral.100' }}
        _focus={{ bg: 'neutral.50' }}
        _hover={{ bg: 'neutral.50' }}
        bg="white"
        border="none"
        borderRadius={0}
        boxShadow="inset 0px -1px 0px #E7EBEE;"
        color="neutral.must"
        height="auto"
        justifyContent="space-between"
        pl={5}
        pr={3}
        py={5}
        textAlign="initial"
        textStyle="body1-highlight"
        width="full"
      >
        {label}
        <Icon as={FiChevronRight} boxSize={6} color="primary.800" />
      </Button>
    </Link>
  );
};
