import { useLayoutAwareElement } from 'shared/hooks/layout';

import type { ConfirmationDisplayElementProps } from './common';
import { ConfirmationModal } from './ConfirmationModal';
import { ConfirmationPopover } from './ConfirmationPopover';

export const Confirmation = (props: ConfirmationDisplayElementProps) => {
  const ConfirmationElement =
    useLayoutAwareElement<ConfirmationDisplayElementProps>(
      ConfirmationModal,
      ConfirmationPopover,
    );

  return <ConfirmationElement {...props} />;
};
