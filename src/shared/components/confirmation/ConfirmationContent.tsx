import { Button, HStack, Text, VStack } from '@chakra-ui/react';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ColorSchemes } from '../../chakra-theme/foundations/colors';
import { ButtonWithLoader } from '../ButtonWithLoader';
import type { ConfirmationProps } from './common';

export type ConfirmationContentProps = {
  onClose: () => void;
} & ConfirmationProps;

export const ConfirmationContent = ({
  title,
  actionText,
  onClose,
  onAction,
  cyPrefix,
}: ConfirmationContentProps) => {
  const { t } = useTranslation('common');
  const [isLoading, setIsLoading] = useState(false);
  const onActionClick = useCallback(async () => {
    setIsLoading(true);
    await onAction();
    setIsLoading(false);
    onClose();
  }, [onAction, onClose]);

  return (
    <VStack alignItems="stretch" spacing={3}>
      <Text align="left" flexGrow={1} textStyle="h4">
        {title}
      </Text>
      <HStack justifyContent="stretch" spacing={3}>
        <Button
          colorScheme={ColorSchemes.SECONDARY}
          data-cy={[cyPrefix, 'confirmation-close-button'].join('-')}
          flexGrow={1}
          isDisabled={isLoading}
          onClick={onClose}
          size="sm"
        >
          {t('forms.cancel')}
        </Button>
        <ButtonWithLoader
          data-cy={[cyPrefix, 'confirmation-action-button'].join('-')}
          flexGrow={1}
          isLoading={isLoading}
          onClick={onActionClick}
          size="sm"
        >
          {actionText}
        </ButtonWithLoader>
      </HStack>
    </VStack>
  );
};
