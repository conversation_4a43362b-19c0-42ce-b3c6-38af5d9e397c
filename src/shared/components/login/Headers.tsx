import { HStack, Icon, IconButton } from '@chakra-ui/react';
import { type FC, useContext } from 'react';
import { HiArrowLeft } from 'react-icons/hi';
import { Link, useLocation } from 'react-router-dom';
import { EstoLogo, LanguageSelect } from 'shared/components';
import { SignUpRoute } from 'shared/constants/routes';
import { GlobalStateContext } from 'shared/hooks/state';

export const MobileHeader: FC = () => {
  const { isVisitedInvitePage } = useContext(GlobalStateContext);
  const { pathname } = useLocation();

  const isMerchantInvitePage = pathname.includes('merchant-invite');

  return (
    <HStack
      alignItems="center"
      borderBottom="1px solid"
      borderColor="neutral.150"
      boxSizing="content-box"
      height={14}
      justifyContent="space-between"
      pl={isVisitedInvitePage && !isMerchantInvitePage ? 0 : 5}
      pr="0.875rem"
    >
      <HStack alignItems="center">
        {!!isVisitedInvitePage && !isMerchantInvitePage && (
          <IconButton
            aria-label="back"
            as={Link}
            icon={<Icon as={HiArrowLeft} boxSize={6} color="primary.800" />}
            isRound
            left={0}
            position="relative"
            to={SignUpRoute.format({ inviteHash: isVisitedInvitePage })}
            top={1}
            variant="ghost"
          />
        )}

        <EstoLogo height={5} width="auto" />
      </HStack>
      <LanguageSelect />
    </HStack>
  );
};

export const DesktopHeader: FC = () => (
  <HStack
    alignItems="center"
    borderColor="neutral.150"
    justifyContent="space-between"
    mt={7}
    px={9}
  >
    <EstoLogo height={5} width="auto" />
    <LanguageSelect />
  </HStack>
);
