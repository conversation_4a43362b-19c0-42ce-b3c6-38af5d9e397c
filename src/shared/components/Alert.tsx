import { Box, Icon, Text } from '@chakra-ui/react';
import type { ElementType } from 'react';

export enum AlertType {
  ERROR = 'error',
  MESSAGE = 'message',
}

export type AlertProps = {
  id: string;
  message: string;
  icon: ElementType;
  type: AlertType;
  onCallToAction?: () => void;
  callToActionLabel?: string;
};

export const Alert = ({
  id,
  message,
  icon,
  type,
  onCallToAction,
  callToActionLabel,
}: AlertProps) => {
  const toastBgColor = {
    [AlertType.ERROR]: 'red.700',
    [AlertType.MESSAGE]: 'neutral.900',
  }[type];
  const iconColor = {
    [AlertType.ERROR]: 'white',
    [AlertType.MESSAGE]: 'neutral.500',
  }[type];
  const textStyle = {
    [AlertType.ERROR]: 'body2-highlight',
    [AlertType.MESSAGE]: 'body2',
  }[type];

  return (
    <Box
      alignItems="center"
      bgColor={toastBgColor}
      borderRadius="4px"
      display="flex"
      id={id}
      maxW="25rem"
      px={5}
      py={4}
      w="100%"
    >
      <Icon as={icon} boxSize={5} color={iconColor} />
      <Text color="white" ml={2} textStyle={textStyle}>
        {message}
      </Text>
      {!!onCallToAction && (
        <Text
          color="primary.500"
          cursor="pointer"
          fontWeight="700"
          ml={5}
          onClick={onCallToAction}
          textStyle="body2"
        >
          {callToActionLabel}
        </Text>
      )}
    </Box>
  );
};
