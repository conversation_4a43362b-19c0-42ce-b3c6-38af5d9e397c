import { Box, Text } from '@chakra-ui/react';
import type { ReactNode } from 'react';
import { HeaderToggleButton } from 'shared/components/header/HeaderToggleButton';

type Props = {
  title?: string;
  before?: ReactNode;
  after?: ReactNode;
  isSidebarButtonEnabled?: boolean;
};

// Default header
export const Header = ({
  title,
  before,
  after,
  isSidebarButtonEnabled = true,
}: Props) => {
  return (
    <Box
      backgroundColor="white"
      borderBottom="1px solid"
      borderColor="neutral.150"
      display={{ md: 'none' }}
      left="0px"
      position="sticky"
      top="0px"
      zIndex={2}
    >
      <Box alignItems="center" display="flex">
        {before}
        {!!isSidebarButtonEnabled && <HeaderToggleButton />}
        {!!title && (
          <Text color="neutral.900" textStyle="h3">
            {title}
          </Text>
        )}
        {after}
      </Box>
    </Box>
  );
};
