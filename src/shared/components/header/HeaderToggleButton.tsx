import { Icon, IconButton } from '@chakra-ui/react';
import { useUnit } from 'effector-react';
import { FiMenu, FiX } from 'react-icons/fi';
import { $isSidebarOpen, toggleSidebarEv } from 'shared/models/sidebar';

export const HeaderToggleButton = () => {
  const isSidebarOpen = useUnit($isSidebarOpen);

  const handleSidebarToggle = () => {
    toggleSidebarEv();
  };

  return (
    <IconButton
      aria-label="toggle-sidebar-button"
      color="primary.800"
      icon={<Icon as={isSidebarOpen ? FiX : FiMenu} boxSize={6} />}
      id="header-toggle-sidebar-button"
      isRound
      mx={2}
      my={1}
      onClick={handleSidebarToggle}
      variant="ghost"
    />
  );
};
