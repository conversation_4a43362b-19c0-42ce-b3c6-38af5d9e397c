import { Box, type ChakraProps, Text } from '@chakra-ui/react';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { MIN_PASSWORD_LENGTH } from 'shared/utils';

type PasswordStrengthWidgetProps = {
  password?: string;
} & ChakraProps;
type PasswordStrength = 'none' | 'weak' | 'strong' | 'very-strong';

const veryStrongPassword = new RegExp(
  `(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[^A-Za-z0-9])(?=.{${MIN_PASSWORD_LENGTH},})`,
);
const strongPassword = new RegExp(
  `(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.{${MIN_PASSWORD_LENGTH},})|(?=.*[a-z])(?=.*[^A-Za-z0-9])(?=.{${MIN_PASSWORD_LENGTH},})`,
);
const weakPassword = new RegExp(`(?=.{${MIN_PASSWORD_LENGTH},})`);

function getPasswordStrength(password: string): PasswordStrength {
  if (veryStrongPassword.test(password)) {
    return 'very-strong';
  }

  if (strongPassword.test(password)) {
    return 'strong';
  }

  if (weakPassword.test(password)) {
    return 'weak';
  }

  return 'none';
}
type ProgressStyles = {
  w: string;
  bg: string;
};
function getProgressStyles(passwordStrength: PasswordStrength): ProgressStyles {
  switch (passwordStrength) {
    case 'very-strong':
      return {
        w: '100%',
        bg: 'primary.800',
      };
    case 'strong':
      return {
        w: '67%',
        bg: 'primary.600',
      };
    case 'weak':
      return {
        w: '34%',
        bg: 'primary.400',
      };
    case 'none':
      return {
        w: '0',
        bg: 'transparent',
      };
  }
}

export const PasswordStrengthWidget = memo(
  ({
    password = '',
    ...chakraProps
  }: PasswordStrengthWidgetProps): JSX.Element => {
    const { t } = useTranslation('common');

    const passwordStrength = getPasswordStrength(password);

    return (
      <Box
        bg="neutral.50"
        borderRadius="4px"
        height={16}
        pb={4}
        pt={3}
        px={4}
        {...chakraProps}
      >
        <Text mb={2} textStyle="body2">
          {t('password-widget.title')}
          {passwordStrength !== 'none' && (
            <>
              {': '}
              <Text as="span" textStyle="body2-highlight">
                {t(`password-widget.${passwordStrength}`)}
              </Text>
            </>
          )}
        </Text>
        <Box
          bg="neutral.150"
          borderRadius="100px"
          h={2}
          position="relative"
          w="100%"
        >
          <Box
            {...getProgressStyles(passwordStrength)}
            borderRadius="100px"
            h="100%"
            left="0"
            position="absolute"
            top="0"
            transition="all 0.2s ease"
          />
        </Box>
      </Box>
    );
  },
);
PasswordStrengthWidget.displayName = 'PasswordStrengthWidget';
