import { Center, type ChakraProps, Spinner } from '@chakra-ui/react';
import type { FC } from 'react';

type LoaderProps = {
  fullScreen?: boolean;
} & ChakraProps;

export const Loader: FC<LoaderProps> = ({
  fullScreen = false,
  height = '10rem',
  position = 'relative',
}) => (
  <Center
    height={fullScreen ? '100vh' : height}
    position={position}
    width={fullScreen ? '100vw' : '100%'}
  >
    <Spinner
      boxSize="2rem"
      color="primary.800"
      emptyColor="neutral.100"
      thickness="3px"
    />
  </Center>
);
