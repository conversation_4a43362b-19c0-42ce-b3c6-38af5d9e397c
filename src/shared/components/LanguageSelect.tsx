import { Button, HStack, Text } from '@chakra-ui/react';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { availableLanguages } from 'shared/lib';
import type { AvailableLanguage } from 'shared/types';

const languageLanguages = {
  en: 'EN',
  et: 'ET',
  lt: 'LT',
  ru: 'RU',
  lv: 'LV',
};

type LanguageSelectProps = {
  onLanguageChange?: (lang: AvailableLanguage) => void;
};

export const LanguageSelect: FC<LanguageSelectProps> = ({
  onLanguageChange,
}) => {
  const { i18n } = useTranslation();

  const handleLanguageChange = (lang: AvailableLanguage) => () => {
    i18n.changeLanguage(lang);

    onLanguageChange?.(lang);
  };

  return (
    <HStack spacing={0}>
      {availableLanguages.map((lang) => (
        <Button
          _selected={{
            bg: 'primary.100',
            borderColor: 'primary.600',
            color: 'neutral.must',
          }}
          aria-selected={i18n.language === lang}
          bg="transparent"
          borderColor="transparent"
          borderRadius="2px"
          borderWidth="1px"
          color="primary.800"
          height={7}
          key={lang}
          onClick={handleLanguageChange(lang)}
          p={0}
          w="2.75rem"
        >
          <Text textStyle="body2">{languageLanguages[lang]}</Text>
        </Button>
      ))}
    </HStack>
  );
};
