import { Button, type ButtonProps } from '@chakra-ui/react';

import { ColorSchemes } from '../chakra-theme/foundations/colors';
import { Loader } from './Loader';

type ButtonWithLoaderProps = {
  btnText?: string;
  onClick?: () => void;
  textColor?: string;
  dataCy?: string;
  noOpacity?: boolean;
} & ButtonProps;

export const ButtonWithLoader = ({
  isLoading = false,
  isDisabled,
  onClick,
  textColor = 'white',
  children,
  dataCy = '',
  type = 'button',
  colorScheme = ColorSchemes.PRIMARY,
  size = 'md',
  noOpacity = true,
  ...rest
}: ButtonWithLoaderProps) => (
  <Button
    colorScheme={colorScheme}
    data-cy={dataCy}
    isDisabled={isDisabled}
    onClick={onClick}
    size={size}
    style={{
      color: isLoading ? 'transparent' : textColor,
      opacity: noOpacity ? 1 : 0.4,
    }}
    type={type}
    {...rest}
  >
    <>
      {children}
      {!!isLoading && <Loader height="100%" position="absolute" />}
    </>
  </Button>
);
