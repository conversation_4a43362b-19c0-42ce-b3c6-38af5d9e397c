import 'react-day-picker/dist/style.css';

import {
  Box,
  type SystemStyleObject,
  useMediaQuery,
  useToken,
} from '@chakra-ui/react';
import type React from 'react';
import { DayPicker } from 'react-day-picker';

import { type DateRangePickerProps, useDateFnsLocale } from './common';

const styles: SystemStyleObject = {
  '.rdp': {
    '--rdp-cell-size': '36px',
    m: 1,
  },
  '.rdp-caption': {
    mb: 5,
  },
  '.rdp-caption_label': {
    textStyle: 'body2-highlight',
    color: 'netural.must',
  },
  '.rdp-nav_icon': {
    color: 'primary.800',
  },
  '.rdp-nav_button': {
    bg: 'transparent',
    border: 'none !important',
    _hover: {
      bg: 'neutral.50',
    },
    _focus: {
      bg: 'neutral.50',
    },
    _active: {
      bg: 'neutral.100',
    },
    _disabled: {
      bg: 'transparent',
    },
  },
  '.rdp-head_cell': {
    textStyle: 'body2-highlight',
    color: 'neutral.500',
    textTransform: 'capitalize',
  },
  '.rdp-day': {
    textStyle: 'body1',
    color: 'neutral.900',
    border: 'none !important',
    _hover: {
      bg: 'neutral.50',
    },
  },
  '.rdp-day_selected': {
    bg: 'primary.100',
    _hover: { bg: 'primary.200', color: 'neutral.900' },
  },
  '.rdp-day_range_start, .rdp-day_range_end': {
    color: 'white !important',
    bg: 'primary.800',
    borderRadius: '4px',
    _active: { bg: 'primary.800' },
    _hover: { bg: 'primary.800' },
    _focus: { bg: 'primary.800' },
  },
  '.rdp-day_range_start:not(.rdp-day_range_end)': {
    borderRadius: '4px 0px 0px 4px',
  },
  '.rdp-day_range_end:not(.rdp-day_range_start)': {
    borderRadius: '0px 4px 4px 0px',
  },
};

export const DateRangePickerDesktop: React.FC<DateRangePickerProps> = ({
  value,
  onChange,
}) => {
  const locale = useDateFnsLocale();
  const breakpointValue = useToken('breakpoints', 'md');
  const [isLargerThanBreakpoint] = useMediaQuery(
    `(min-width: ${breakpointValue})`,
  );

  return (
    <Box sx={styles}>
      <DayPicker
        defaultMonth={value.from}
        locale={locale}
        mode="range"
        numberOfMonths={isLargerThanBreakpoint ? 2 : 1}
        onSelect={(range) => {
          if (range) {
            onChange(range);
          }
        }}
        onDayClick={(
          day,
          { selected, range_start, range_middle, range_end },
        ) => {
          if (selected && range_start && !range_middle && !range_end) {
            onChange({ from: day, to: day });
          }
        }}
        selected={value}
        toDate={new Date()}
      />
    </Box>
  );
};

export const DateRangePickerMobile: React.FC<DateRangePickerProps> = ({
  value,
  onChange,
}) => {
  const locale = useDateFnsLocale();
  return (
    <Box
      sx={{
        ...styles,
        '.rdp': {
          '--rdp-cell-size': 'calc((100vw - 2.5rem) / 7)',
          m: 0,
        },
      }}
    >
      <DayPicker
        defaultMonth={value.from}
        locale={locale}
        mode="range"
        numberOfMonths={1}
        onSelect={(range) => {
          if (range) {
            onChange(range);
          }
        }}
        onDayClick={(
          day,
          { selected, range_start, range_middle, range_end },
        ) => {
          if (selected && range_start && !range_middle && !range_end) {
            onChange({ from: day, to: day });
          }
        }}
        selected={value}
        toDate={new Date()}
      />
    </Box>
  );
};
