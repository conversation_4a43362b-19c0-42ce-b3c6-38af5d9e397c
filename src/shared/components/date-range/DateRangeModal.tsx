import {
  <PERSON>ton,
  <PERSON>dal,
  ModalBody,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  ModalOverlay,
  useDisclosure,
} from '@chakra-ui/react';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ModalCloseButton } from '../ModalCloseButton';
import {
  type DateRangeElementProps,
  type DateRangeType,
  useFormattedDateRange,
} from './common';
import { DateRangeInput } from './DateRangeInput';
import { DateRangePickerMobile } from './DateRangePicker';

export const DateRangeModal: React.FC<DateRangeElementProps> = ({
  value,
  onChange,
  ...chakraProps
}) => {
  const { isOpen, onClose, onOpen } = useDisclosure();
  const inputRef = useRef<HTMLInputElement>(null);
  const { formattedDate } = useFormattedDateRang<PERSON>(value);

  useEffect(() => {
    const inputEl = inputRef.current;
    inputEl?.addEventListener('click', onOpen);
    return () => inputEl?.removeEventListener('click', onOpen);
  }, [onOpen]);

  return (
    <>
      <DateRangeInput ref={inputRef} value={formattedDate} {...chakraProps} />

      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <DateRangeModalContent
          onSubmit={(range) => {
            onChange(range);
            onClose();
          }}
          value={value}
        />
      </Modal>
    </>
  );
};

type DateRangeModalContentProps = {
  value: DateRangeType;
  onSubmit: (range: DateRangeType) => void;
};

const DateRangeModalContent: React.FC<DateRangeModalContentProps> = ({
  value,
  onSubmit,
}) => {
  const { t } = useTranslation('common');
  const [savedValue, setSavedValue] = useState<DateRangeType | undefined>(
    value,
  );
  const { formattedDate } = useFormattedDateRange(savedValue || value);

  return (
    <ModalContent>
      <ModalHeader>{t('date-range.title')}</ModalHeader>
      <ModalCloseButton />
      <ModalBody>
        <DateRangePickerMobile
          onChange={setSavedValue}
          value={savedValue || value}
        />
      </ModalBody>

      <ModalFooter>
        <Button
          colorScheme="primary"
          onClick={() => {
            onSubmit(savedValue || value);
          }}
        >
          {t('date-range.submit')} {formattedDate}
        </Button>
      </ModalFooter>
    </ModalContent>
  );
};
