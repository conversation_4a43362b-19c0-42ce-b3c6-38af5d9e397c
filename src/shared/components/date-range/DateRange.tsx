import { useLayoutAwareElement } from 'shared/hooks/layout';

import type { DateRangeElementProps } from './common';
import { DateRangeModal } from './DateRangeModal';
import { DateRangePopover } from './DateRangePopover';

export const DateRange: React.FC<DateRangeElementProps> = (props) => {
  const DateRangeElement = useLayoutAwareElement<DateRangeElementProps>(
    DateRangeModal,
    DateRangePopover,
  );

  return <DateRangeElement {...props} />;
};
