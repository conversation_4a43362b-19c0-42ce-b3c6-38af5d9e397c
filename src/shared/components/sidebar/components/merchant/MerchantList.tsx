import { Button, Text, VStack } from '@chakra-ui/react';
import type { RefObject } from 'react';
import { MerchantLogo } from 'shared/components/sidebar/components/merchant/MerchantLogo';
import type { MerchantListItem } from 'shared/types';

type MerchantListProps = {
  size?: 'small' | 'default';
  selectedMerchant: MerchantListItem | null;
  merchants: Array<MerchantListItem>;
  focusRef: RefObject<HTMLElement>;
  onSelect: (merchant: MerchantListItem) => void;
};

export const MerchantList = ({
  size = 'default',
  selectedMerchant,
  merchants,
  focusRef,
  onSelect,
}: MerchantListProps) => {
  return (
    <VStack
      alignItems="stretch"
      data-cy="merchant-switch-list"
      spacing={0}
      w="100%"
    >
      {merchants.map((merchant) => (
        <Button
          _focus={{
            bg: 'neutral.50',
            borderColor: 'neutral.50',
          }}
          _hover={{
            bg: 'neutral.50',
            borderColor: 'neutral.50',
          }}
          _selected={{
            bg: 'primary.100',
            borderColor: 'primary.600',
          }}
          aria-selected={selectedMerchant?.id === merchant.id}
          bg="transparent"
          border="1px solid"
          borderColor="transparent"
          borderRadius={size === 'default' ? '4px' : '2px'}
          h={size === 'default' ? 12 : 10}
          key={merchant.id}
          onClick={() => {
            onSelect(merchant);
          }}
          px={3}
          ref={
            selectedMerchant?.id === merchant.id
              ? (focusRef as RefObject<HTMLButtonElement>)
              : undefined
          }
        >
          <MerchantLogo
            isLight={selectedMerchant?.id === merchant.id}
            merchant={merchant}
          />
          <Text
            data-cy="merchant-switch-merchant-name"
            flexGrow={1}
            isTruncated
            ml={2}
            textAlign="left"
            textStyle={
              selectedMerchant?.id === merchant.id ? 'body1-highlight' : 'body1'
            }
          >
            {merchant.name}
          </Text>
        </Button>
      ))}
    </VStack>
  );
};
