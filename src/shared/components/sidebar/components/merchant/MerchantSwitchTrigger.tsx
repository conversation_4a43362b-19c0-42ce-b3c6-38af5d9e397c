import { type ButtonProps, Icon, Text } from '@chakra-ui/react';
import { forwardRef } from 'react';
import { FiChevronRight } from 'react-icons/fi';
import { MerchantLogo } from 'shared/components/sidebar/components/merchant/MerchantLogo';
import { SidebarButton } from 'shared/components/sidebar/components/SidebarButton';
import type { MerchantListItem } from 'shared/types';

type MerchantSwitchTriggerProps = {
  selectedMerchant: MerchantListItem | null;
} & ButtonProps;

export const MerchantSwitchTrigger = forwardRef<
  HTMLButtonElement,
  MerchantSwitchTriggerProps
>(({ selectedMerchant, ...chakraProps }, ref): JSX.Element => {
  return (
    <SidebarButton ref={ref} {...chakraProps}>
      {!!selectedMerchant && (
        <>
          <MerchantLogo isLight merchant={selectedMerchant} />
          <Text
            data-cy="merchant-switch-merchant-name"
            flexGrow={1}
            isTruncated
            ml={3}
            textAlign="left"
            textStyle="body2"
          >
            {selectedMerchant.name}
          </Text>
          <Icon as={FiChevronRight} boxSize={6} />
        </>
      )}
    </SidebarButton>
  );
});
MerchantSwitchTrigger.displayName = 'MerchantSwitchTrigger';
