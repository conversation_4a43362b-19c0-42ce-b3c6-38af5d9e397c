import { Icon, Text } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FiLogOut } from 'react-icons/fi';
import { Confirmation } from 'shared/components/confirmation';
import { SidebarButton } from 'shared/components/sidebar/components/SidebarButton';
import { useCurrentUser, useLogout } from 'shared/hooks/user';
import { getFullName } from 'shared/utils/name';

export const LogoutButton = () => {
  const { t } = useTranslation('common');

  const { user } = useCurrentUser();
  const handleLogOut = useLogout();

  return (
    <Confirmation
      actionText={t('sidebar.sign-out')}
      onAction={handleLogOut}
      popoverPlacement="right-start"
      title={t('sidebar.sign-out-question')}
      trigger={
        <SidebarButton data-cy="sidebar-logout-button" mb={2}>
          <Text
            data-cy="merchant-switch-merchant-name"
            flexGrow={1}
            isTruncated
            textAlign="left"
            textStyle="body2"
          >
            {getFullName({
              firstName: user?.profile?.first_name,
              lastName: user?.profile?.last_name,
              defaultValue: 'No name',
            })}
          </Text>
          <Icon as={FiLogOut} boxSize={6} />
        </SidebarButton>
      }
    />
  );
};
