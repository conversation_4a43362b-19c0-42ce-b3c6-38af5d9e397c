import { Box } from '@chakra-ui/react';
import { useUnit } from 'effector-react';
import { $isSidebarOpen, toggleSidebarEv } from 'shared/models/sidebar';

export const MobileSidebarOverlay = () => {
  const isSidebarOpen = useUnit($isSidebarOpen);

  const handleSidebarClose = () => {
    toggleSidebarEv(false);
  };

  if (!isSidebarOpen) {
    return null;
  }

  return (
    <Box
      bg="white"
      bottom={0}
      height="calc(100% - 57px)"
      left={0}
      onClick={handleSidebarClose}
      opacity={0.9}
      position="absolute"
      width="100%"
      zIndex={1}
    />
  );
};
