import { Box, Icon, Text } from '@chakra-ui/react';
import type { ElementType } from 'react';
import { Link, useMatch } from 'react-router-dom';
import { SidebarButton } from 'shared/components/sidebar/components/SidebarButton';
import { toggleSidebarEv } from 'shared/models/sidebar';

type SidebarLinkProps = {
  cyPrefix?: string;
  icon: ElementType;
  title: string;
  path: string;
  isShowDot?: boolean;
};

export const SidebarLink = ({
  icon,
  path,
  title,
  cyPrefix,
  isShowDot = false,
}: SidebarLinkProps) => {
  const match = useMatch(`${path}/*`);

  const onClick = () => {
    toggleSidebarEv(false);
  };

  return (
    <Box as={Link} onClick={onClick} tabIndex={-1} to={path} w="100%">
      <SidebarButton
        _focus={{
          bg: 'rgba(195, 204, 213, 0.3)',
        }}
        _hover={{
          bg: 'rgba(195, 204, 213, 0.2)',
          color: 'neutral.900',
        }}
        bg={match ? 'rgba(195, 204, 213, 0.3)' : 'transparent'}
        border="none"
        color={match ? 'neutral.must' : 'neutral.800'}
        data-cy={!!cyPrefix && `${cyPrefix}-sidebar-link`}
        m={0}
      >
        <Icon as={icon} boxSize={6} mr={3} />
        <Text flexGrow={1} textAlign="left" textStyle="body1-highlight">
          {title}
        </Text>

        {!!isShowDot && (
          <Box backgroundColor="primary.500" borderRadius="50%" boxSize={2} />
        )}
      </SidebarButton>
    </Box>
  );
};
