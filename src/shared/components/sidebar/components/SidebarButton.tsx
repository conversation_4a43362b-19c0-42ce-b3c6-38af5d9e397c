import { Button, type ButtonProps } from '@chakra-ui/react';
import { forwardRef } from 'react';

export const SidebarButton = forwardRef<HTMLButtonElement, ButtonProps>(
  (chakraProps, ref): JSX.Element => {
    return (
      <Button
        _focus={{
          bg: 'rgba(171, 184, 196, 0.2)',
        }}
        _hover={{
          bg: 'rgba(171, 184, 196, 0.1)',
        }}
        bg="transparent"
        border="1px solid rgba(195, 204, 213, 0.5)"
        color="neutral.900"
        h={12}
        pl={4}
        pr={3}
        ref={ref}
        width="full"
        {...chakraProps}
      >
        {chakraProps.children}
      </Button>
    );
  },
);

SidebarButton.displayName = 'SidebarButton';
