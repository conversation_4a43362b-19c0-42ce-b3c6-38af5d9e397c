import { Box, Flex, Text, VStack } from '@chakra-ui/react';
import { useUnit } from 'effector-react';
import { type ElementType, memo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FiFileText,
  FiPieChart,
  FiSettings,
  FiShoppingCart,
  FiTool,
  FiUser,
} from 'react-icons/fi';
import { LanguageSelect } from 'shared/components';
import { EstoLogo } from 'shared/components/icons';
import { LogoutButton } from 'shared/components/sidebar/components/LogoutButton';
import {
  AdminMerchantSwitch,
  MerchantSwitch,
} from 'shared/components/sidebar/components/merchant';
import { SidebarLink } from 'shared/components/sidebar/components/SidebarLink';
import {
  LocizeCommonKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import {
  AccountRoute,
  ApplicationListRoute,
  FinanceRoute,
  ForDeveloperRoute,
  SettingsRoute,
  TerminalRoute,
} from 'shared/constants/routes';
import { useShowError } from 'shared/hooks/alerts';
import { useIsRegion } from 'shared/hooks/app';
import { useRoleAndRegionCheck } from 'shared/hooks/application';
import { useIsSuperAdmin, useUpdateUserLanguage } from 'shared/hooks/user';
import { useReadLocalStorage } from 'shared/hooks/utility';
import { affiliateContacts } from 'shared/lib';
import { $isSidebarOpen } from 'shared/models/sidebar';
import { type AvailableLanguage, MerchantPermissions } from 'shared/types';
import { AppRegions } from 'shared/utils';

type SidebarOption = {
  icon: ElementType;
  title: string;
  path: string;
  allowedRoles?: Array<MerchantPermissions>;
  allowedRegions?: Array<AppRegions>;
  isShowDot?: boolean;
};

type GetSidebarPropsParams = {
  isFinancesNewViewOpened?: boolean;
};

export const getSidebarOptions = ({
  isFinancesNewViewOpened,
}: GetSidebarPropsParams): Array<SidebarOption> => [
  {
    title: LocizeCommonKeys.SIDEBAR_TERMINAL_OPTION,
    icon: FiShoppingCart,
    path: TerminalRoute.format(),
    allowedRoles: [MerchantPermissions.Cashier],
  },
  {
    title: LocizeCommonKeys.SIDEBAR_APPLICATIONS_OPTION,
    icon: FiFileText,
    path: ApplicationListRoute.format(),
    allowedRoles: [
      MerchantPermissions.Cashier,
      MerchantPermissions.Admin,
      MerchantPermissions.Accountant,
    ],
  },
  {
    title: LocizeCommonKeys.SIDEBAR_FINANCES_OPTION,
    icon: FiPieChart,
    path: FinanceRoute.format(),
    allowedRoles: [MerchantPermissions.Admin, MerchantPermissions.Accountant],
    isShowDot: !isFinancesNewViewOpened,
  },
  {
    title: LocizeCommonKeys.SIDEBAR_FOR_DEVELOPERS_OPTION,
    icon: FiTool,
    path: ForDeveloperRoute.format(),
    allowedRoles: [MerchantPermissions.Developer],
  },
  // {
  // 	title: LocizeCommonKeys.SIDEBAR_BUSINESS_LOAN_OPTION,
  // 	icon: FiBriefcase,
  // 	path: BusinessLoanRoute.format(),
  // 	allowedRegions: [AppRegions.ET],
  // 	allowedRoles: [MerchantPermissions.Admin],
  // },
  {
    title: LocizeCommonKeys.SIDEBAR_SETTINGS_OPTION,
    icon: FiSettings,
    path: SettingsRoute.format(),
    allowedRoles: [MerchantPermissions.Admin],
  },
  {
    title: LocizeCommonKeys.SIDEBAR_ACCOUNT_OPTION,
    icon: FiUser,
    path: AccountRoute.format(),
  },
];

const Sidebar = () => {
  const isSidebarOpen = useUnit($isSidebarOpen);
  const sidebarRef = useRef<HTMLDivElement>(null);

  const isFinancesNewViewOpened = useReadLocalStorage<boolean>(
    'isFinancesNewViewOpened',
  );

  const { t } = useTranslation(LocizeNamespaces.COMMON);
  const isSuperAdmin = useIsSuperAdmin();
  const isLvUserLoggedIn = useIsRegion([AppRegions.LV]);
  const checkRoleAndRegion = useRoleAndRegionCheck();
  const updateLanguage = useUpdateUserLanguage();
  const showError = useShowError();

  const handleLanguageChange = async (
    lang: AvailableLanguage,
  ): Promise<void> => {
    try {
      await updateLanguage(lang);
    } catch {
      showError("Can't perform a language change! Try again later.");
    }
  };

  const sidebarOptions = getSidebarOptions({
    isFinancesNewViewOpened: !!isFinancesNewViewOpened,
  });

  return (
    <Flex
      bg="linear-gradient(186.59deg, rgba(153, 153, 255, 0.1) 0%, rgba(102, 212, 237, 0.1) 100%), #FFFFFF"
      flexDir="column"
      height="100%"
      justifyContent="space-between"
      left={{
        base: isSidebarOpen ? '0' : '-17em',
        md: 'unset',
      }}
      overflow="auto"
      position={{
        base: 'fixed',
        md: 'relative',
      }}
      px={5}
      py={8}
      ref={sidebarRef}
      top="0px"
      transitionDuration="0.2s"
      transitionProperty="left"
      w="17rem"
    >
      <Box>
        <EstoLogo color="neutral.must" h="1.25rem" mb={8} ml={4} w="3.6rem" />
        <LogoutButton />
        {isSuperAdmin ? (
          <AdminMerchantSwitch mb={8} />
        ) : (
          <MerchantSwitch mb={8} />
        )}
        <VStack spacing={0}>
          {sidebarOptions.map(
            ({ icon, path, allowedRoles, title, allowedRegions, isShowDot }) =>
              checkRoleAndRegion({
                roles: allowedRoles,
                regions: allowedRegions,
              }) && (
                <SidebarLink
                  icon={icon}
                  isShowDot={isShowDot}
                  key={path}
                  path={path}
                  title={t(title)}
                />
              ),
          )}
        </VStack>
      </Box>

      <VStack align="start" mt={10} spacing={4}>
        <LanguageSelect onLanguageChange={handleLanguageChange} />
        <Box color="neutral.800" w="100%">
          <Text textStyle="body2-highlight">{t('support.need-help')}</Text>
          <Text textStyle="body2">{affiliateContacts.email}</Text>
          <Text textStyle="body2">
            {isLvUserLoggedIn
              ? affiliateContacts.phoneLoggedIn
              : affiliateContacts.phone}
          </Text>
        </Box>
      </VStack>
    </Flex>
  );
};

export const MemoizedSidebar = memo(Sidebar);
