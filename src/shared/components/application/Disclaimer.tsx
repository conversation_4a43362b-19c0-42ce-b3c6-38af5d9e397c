import { HStack, Icon, Text } from '@chakra-ui/react';
import { format } from 'date-fns';
import type { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { FiAlertCircle } from 'react-icons/fi';
import type { ApplicationRequestType } from 'shared/types';

export type ApplicationDetailsDisclaimerProps = {
  date: number;
  requestCreatorFullName?: string;
  type: ApplicationRequestType;
  after?: ReactNode;
};

export const ApplicationDetailsDisclaimer = ({
  type,
  date,
  after,
  requestCreatorFullName,
}: ApplicationDetailsDisclaimerProps) => {
  const { t } = useTranslation('applications');

  return (
    <HStack
      alignItems="center"
      bg="primary.100"
      borderRadius="4px"
      data-cy={`application-${type}-disclaimer`}
      justifyContent="space-between"
      mb={5}
      mt={2}
      px={3}
      py={2}
      spacing={2}
    >
      <HStack alignItems="center" spacing={2}>
        <Icon as={FiAlertCircle} boxSize={5} color="primary.700" />
        <Text color="primary.900" textStyle="body2">
          {t(`details.${type}-disclaimer`, {
            date: format(date, 'dd.MM.yyyy'),
          })}{' '}
          {!!requestCreatorFullName && `(${requestCreatorFullName})`}
        </Text>
      </HStack>
      {after}
    </HStack>
  );
};
