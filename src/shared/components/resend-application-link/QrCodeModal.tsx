import {
  Button,
  Center,
  Modal,
  ModalBody,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalOverlay,
  Text,
} from '@chakra-ui/react';
import QRCode from 'qrcode.react';
import { useTranslation } from 'react-i18next';

import { ColorSchemes } from '../../chakra-theme/foundations/colors';
import { ModalCloseButton } from '../ModalCloseButton';

type QrCodeModalProps = {
  isOpen: boolean;
  purchaseUrl: string;
  onClose: () => void;
};

export const QrCodeModal = ({ isOpen, ...rest }: QrCodeModalProps) => {
  return (
    <Modal isOpen={isOpen} onClose={rest.onClose} scrollBehavior="inside">
      <ModalOverlay display={['none', 'flex']} />
      <QrCodeModalContent {...rest} />
    </Modal>
  );
};

type QrCodeModalContentProps = {
  purchaseUrl: string;
  onClose: () => void;
};

const QrCodeModalContent = ({
  purchaseUrl,
  onClose,
}: QrCodeModalContentProps) => {
  const { t } = useTranslation(['terminal', 'common']);

  return (
    <ModalContent data-cy="qr-code-modal-content" maxW="29rem">
      <ModalHeader>{t('qr-code-modal.title')}</ModalHeader>
      <ModalCloseButton />
      <ModalBody display="flex" flexDirection="column">
        <Text flexShrink={0} mb={[0, 6]} textStyle="body2">
          {t('qr-code-modal.description')}
        </Text>
        <Center flexGrow={1}>
          <QRCode size={142} value={purchaseUrl} />
        </Center>
      </ModalBody>

      <ModalFooter>
        <Button
          colorScheme={ColorSchemes.SECONDARY}
          mr={[0, 3]}
          mt={[3, 0]}
          onClick={onClose}
        >
          {t('common:modal.close')}
        </Button>
      </ModalFooter>
    </ModalContent>
  );
};
