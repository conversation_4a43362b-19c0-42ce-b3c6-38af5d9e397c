import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalOverlay,
} from '@chakra-ui/react';
import { useCallback, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  type SendPurchaseUrlMutationVariables,
  UserMessageSentType,
  useSendPurchaseUrlMutation,
} from 'shared/api';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { phonePrefix } from 'shared/lib';
import { EMAIL_REGEXP, PHONE_FORMAT } from 'shared/utils';
import { z } from 'zod';

import { ColorSchemes } from '../../chakra-theme/foundations/colors';
import { ButtonWithLoader } from '../ButtonWithLoader';
import {
  ControlledToggle,
  PatternNumberInput,
  TextInput,
  ToggleItem,
} from '../controls';
import { ModalCloseButton } from '../ModalCloseButton';

export function useSendPurchaseUrl() {
  const [sendPurchaseUrlMutation, { loading }] = useSendPurchaseUrlMutation();
  const handleGenericError = useHandleGenericError();

  const sendPurchaseUrl = useCallback(
    async (variables: SendPurchaseUrlMutationVariables) => {
      try {
        return await sendPurchaseUrlMutation({ variables });
      } catch (e) {
        handleGenericError(e);
        return null;
      }
    },
    [handleGenericError, sendPurchaseUrlMutation],
  );

  return { isLoading: loading, sendPurchaseUrl };
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const SendLinkSchema = z.object({
  phone: z.string().min(1, 'required'),
  email: z.string().email('invalid-format'),
  sendLinkType: z.nativeEnum(UserMessageSentType),
});

type SendLinkSchemaType = z.infer<typeof SendLinkSchema>;

type Props = {
  isOpen: boolean;
  applicationId: number;
  isResend: boolean;
  onSuccess: (destination: string) => void;
  onClose: () => void;
};

export const SendLinkModal = ({
  isOpen,
  applicationId,
  isResend,
  onClose,
  onSuccess,
}: Props) => {
  const inputRef = useRef(null);
  const { t } = useTranslation('terminal');
  const { sendPurchaseUrl } = useSendPurchaseUrl();
  const {
    register,
    formState,
    control,
    formState: { errors },
    watch,
    handleSubmit,
  } = useForm<SendLinkSchemaType>({
    defaultValues: {
      email: '',
      phone: '',
      sendLinkType: UserMessageSentType.SMS,
    },
  });
  const isSubmitting = formState.isSubmitting;
  const sendLinkType = watch('sendLinkType');
  const translationKeyPart = isResend ? 'resend' : 'send';
  const onSubmit = useCallback(
    async ({ phone, email, sendLinkType }: SendLinkSchemaType) => {
      const result = await sendPurchaseUrl({
        applicationId,
        type: sendLinkType,
        email,
        phone,
      });
      if (result !== null) {
        if (email) {
          onSuccess(email);
        } else {
          const formatted = `${phonePrefix} ${phone.substring(
            0,
            3,
          )} ${phone.substring(3)}`;
          onSuccess(formatted);
        }
      }
    },
    [onSuccess, sendPurchaseUrl, applicationId],
  );

  return (
    <Modal
      initialFocusRef={inputRef}
      isOpen={isOpen}
      onClose={onClose}
      scrollBehavior="inside"
    >
      <ModalOverlay display={['none', 'flex']} />
      <ModalContent
        as="form"
        data-cy="send-link-modal-content"
        maxW="29rem"
        onSubmit={handleSubmit(onSubmit)}
      >
        <ModalHeader>
          {t(`send-link-modal.${translationKeyPart}.title`)}
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <ControlledToggle
            control={control}
            isDisabled={isSubmitting}
            mb={4}
            name="sendLinkType"
            w="100%"
          >
            <ToggleItem
              dataCy="send-link-modal-type-phone"
              value={UserMessageSentType.SMS}
            >
              {t('send-link.phone.toggle')}
            </ToggleItem>
            <ToggleItem
              dataCy="send-link-modal-type-email"
              value={UserMessageSentType.EMAIL}
            >
              {t('send-link.email.toggle')}
            </ToggleItem>
          </ControlledToggle>
          {sendLinkType === UserMessageSentType.SMS ? (
            <PatternNumberInput
              data-cy="send-link-modal-phone"
              type="tel"
              {...register('phone', {
                required: true,
              })}
              disabled={isSubmitting}
              error={
                errors.phone?.message
                  ? t(`common:forms.${errors.phone.message}`)
                  : undefined
              }
              format={PHONE_FORMAT}
              label={t('send-link.phone.label')}
            />
          ) : (
            <TextInput
              data-cy="send-link-modal-email"
              inputMode="email"
              label={t('send-link.email.label')}
              {...register('email', {
                required: true,
                pattern: { value: EMAIL_REGEXP, message: 'invalid-format' },
              })}
              error={
                errors.email?.message
                  ? t(`common:forms.${errors.email.message}`)
                  : undefined
              }
              isDisabled={isSubmitting}
            />
          )}
        </ModalBody>

        <ModalFooter>
          <Button
            colorScheme={ColorSchemes.SECONDARY}
            isDisabled={isSubmitting}
            mr={[0, 3]}
            mt={[3, 0]}
            onClick={onClose}
          >
            {t('send-link-modal.cancel')}
          </Button>

          <ButtonWithLoader
            data-cy="send-link-modal-submit"
            isLoading={isSubmitting}
            type="submit"
          >
            {t(`send-link-modal.${translationKeyPart}.submit`)}
          </ButtonWithLoader>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
