import { createIcon } from '@chakra-ui/react';

export const E<PERSON><PERSON><PERSON> = createIcon({
  displayName: 'EstoLogo',
  viewBox: '0 0 276.9 97.2',
  path: (
    <>
      <g transform="translate(-287.000000, -522.000000)">
        <path
          clipRule="evenodd"
          d="M525.7,540.3c10.2,0,19.7,4,27,11.4c7.2,7.5,11.2,17.3,11.2,27.9c0,10.6-3.9,20.6-11.2,28.1c-7.3,7.5-16.7,11.4-27,11.4c-10.2,0-19.5-4-26.8-11.4      c-7.4-7.6-11.3-17.5-11.3-28.1c0-10.6,3.9-20.3,11.3-27.9C506.2,544.3,515.5,540.3,525.7,540.3z M525.7,554.7      c-6.3,0-11.9,2.7-16.1,7.2c-4.1,4.5-6.6,10.6-6.6,17.6c0,7,2.5,13.2,6.6,17.7c4.1,4.5,9.8,7.3,16,7.3c6.2,0,11.9-2.8,16-7.3      c4.1-4.5,6.6-10.7,6.6-17.7c0-7-2.5-13.2-6.6-17.6C537.7,557.4,532,554.7,525.7,554.7z M324,540.2c6.4,0,12.2,1.4,17.2,4.2      c5.1,2.8,9.1,6.7,12.1,11.7c3,5,4.5,10.7,4.5,17.2c0,1.1-0.1,4.9-0.2,11.3l0,0.4l-54.5,0c1.2,5.7,3.6,9.8,7.5,12.9      c4.1,3.2,9.6,4.9,16.6,5.2l0.6,0l1.7,0h24.2v14.6h-24.3c-13.3,0-23.6-3.5-31-10.4c-7.5-6.9-11.3-15.7-11.3-29      c0-6.7,1.5-12.9,4.6-18.7c3.1-5.8,7.3-10.5,12.8-14.1C309.9,542.1,316.5,540.2,324,540.2z M324.7,555.3c-6,0-10.9,1.6-15.5,5.6      c-2.8,2.7-4.6,6.1-5.7,10.6l0,0l40.1-0.3l0-0.5l0-0.4c-0.2-2.3-1-4.5-2.2-6.5c-1.5-2.6-3.7-4.6-6.5-6.2      C332.1,556.1,328.7,555.3,324.7,555.3z M420.5,541.7v14.2h-31.7c-2.3,0-4.1,0.5-5.3,1.5c-1.2,0.9-1.8,2.3-1.8,4      c0,1.3,0.5,2.4,1.5,3.4c1.2,1.2,3.1,2.3,5.7,3.4l0,0l13.6,5.6c7.4,3.1,13.1,5.9,16.7,9.7c3.2,3.4,4.9,7.5,4.9,13.2      c0,7.2-2.6,12.5-7.8,16c-5.1,3.4-12.9,5.2-23.4,5.2l0,0l-0.6,0h-24.7v-14.5h31.7c3,0,5.4-0.6,7.1-1.7c1.5-1.1,2.4-2.6,2.4-4.5      c0-1.7-0.6-3-1.8-4.1c-1.5-1.4-3.9-2.6-7.1-4l0,0l-13-5.3c-7.1-2.9-12.2-5.7-15.5-9.3c-3.1-3.4-4.6-7.5-4.6-12.9      c0-6.2,2.3-11.2,6.6-14.5c4.2-3.2,10.4-5.1,18.5-5.1l0,0l0.5,0H420.5z M435,522h15v19.7h29.2v14.4H450V587c0,5.6,1.2,9.7,4,12.4      c2.8,2.7,7,3.9,12.9,4l0,0l12.3,0v14.5h-12.1c-10.6,0-18.5-2.4-23.9-7c-5.4-4.7-8.2-11.6-8.3-21l0,0l0-0.6V522z"
          fill="currentColor"
          fillRule="evenodd"
        />
      </g>
      <defs>
        <clipPath id="clip0">
          <path d="M0 0h446.422v155H0z" fill="currentColor" />
        </clipPath>
      </defs>
    </>
  ),
});

export const QrCodeIcon = createIcon({
  displayName: 'QrCodeIcon',
  viewBox: '0 0 20 20',
  path: (
    <g fill="none">
      <rect
        height="5.5"
        rx="1"
        stroke="currentColor"
        strokeWidth="2"
        width="5.5"
        x="2.66699"
        y="2.66663"
      />
      <rect
        height="5.5"
        rx="1"
        stroke="currentColor"
        strokeWidth="2"
        width="5.5"
        x="2.66699"
        y="11.8333"
      />
      <rect
        height="1.25"
        stroke="currentColor"
        strokeWidth="1.25"
        width="1.25"
        x="11.459"
        y="11.4583"
      />
      <rect
        height="1.25"
        stroke="currentColor"
        strokeWidth="1.25"
        width="1.25"
        x="13.959"
        y="13.9583"
      />
      <rect
        height="1.25"
        stroke="currentColor"
        strokeWidth="1.25"
        width="1.25"
        x="16.459"
        y="16.4583"
      />
      <rect
        height="1.25"
        stroke="currentColor"
        strokeWidth="1.25"
        width="1.25"
        x="16.459"
        y="11.4583"
      />
      <rect
        height="1.25"
        stroke="currentColor"
        strokeWidth="1.25"
        width="1.25"
        x="11.459"
        y="16.4583"
      />
      <rect
        height="5.5"
        rx="1"
        stroke="currentColor"
        strokeWidth="2"
        width="5.5"
        x="11.834"
        y="2.66663"
      />
    </g>
  ),
});

export const CustomCheckCircle = createIcon({
  displayName: 'CustomCheckIcon',
  viewBox: '0 0 24 24',
  path: (
    <>
      <path
        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
        fill="var(--fill-color)"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      />
      <path
        d="M16 10L10.8125 15L8 12.2727"
        fill="var(--fill-color)"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      />
    </>
  ),
});

export const CustomCrossCircle = createIcon({
  displayName: 'CustomCrossCircle',
  viewBox: '0 0 24 24',
  path: (
    <>
      <path
        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
        fill="var(--fill-color)"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      />
      <path
        d="M15 9L9 15"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      />
      <path
        d="M9 9L15 15"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      />
    </>
  ),
});

export const PracticeOnIcon = createIcon({
  displayName: 'PracticeOnIcon',
  viewBox: '0 0 24 24',
  path: (
    <g fill="none">
      <path
        d="M22.4315 8.5C22.8002 9.59944 23 10.7764 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C13.2236 1 14.4006 1.19979 15.5 1.56852"
        stroke="currentColor"
        strokeLinecap="round"
        strokeWidth="2"
      />
      <path
        d="M13 6.08296C12.6748 6.0284 12.3407 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18C15.3137 18 18 15.3137 18 12C18 11.6593 17.9716 11.3252 17.917 11"
        stroke="currentColor"
        strokeLinecap="round"
        strokeWidth="2"
      />
      <circle cx="12" cy="12" r="1" stroke="currentColor" strokeWidth="2" />
      <path
        d="M12 12L18 6M21 3L18 6M21 3V1L18 4V6M21 3H23L20 6H18"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      />
    </g>
  ),
});

export const PracticeOffIcon = createIcon({
  displayName: 'PracticeOffIcon',
  viewBox: '0 0 24 24',
  path: (
    <g fill="none">
      <circle cx="12" cy="12" r="11" stroke="currentColor" strokeWidth="2" />
      <circle cx="12" cy="12" r="6" stroke="currentColor" strokeWidth="2" />
      <circle cx="12" cy="12" r="1" stroke="currentColor" strokeWidth="2" />
    </g>
  ),
});

export const CalculatorIcon = createIcon({
  displayName: 'CalculatorIcon',
  viewBox: '0 0 24 24',
  path: (
    <path
      clipRule="evenodd"
      d="M3.58579 1.58579C3.21071 1.96086 3 2.46957 3 3V21C3 21.5304 3.21071 22.0391 3.58579 22.4142C3.96086 22.7893 4.46957 23 5 23H19C19.5304 23 20.0391 22.7893 20.4142 22.4142C20.7893 22.0391 21 21.5304 21 21V3C21 2.46957 20.7893 1.96086 20.4142 1.58579C20.0391 1.21071 19.5304 1 19 1H5C4.46957 1 3.96086 1.21071 3.58579 1.58579ZM19 3H5V21H19V3ZM17 6C17 5.44772 16.5523 5 16 5H8C7.44772 5 7 5.44772 7 6C7 6.55228 7.44772 7 8 7H16C16.5523 7 17 6.55228 17 6ZM7 10C7 9.44771 7.44772 9 8 9C8.55228 9 9 9.44771 9 10C9 10.5523 8.55228 11 8 11C7.44772 11 7 10.5523 7 10ZM8 13C8.55228 13 9 13.4477 9 14C9 14.5523 8.55228 15 8 15C7.44772 15 7 14.5523 7 14C7 13.4477 7.44772 13 8 13ZM7 18C7 17.4477 7.44772 17 8 17C8.55228 17 9 17.4477 9 18C9 18.5523 8.55228 19 8 19C7.44772 19 7 18.5523 7 18ZM12 9C12.5523 9 13 9.44771 13 10C13 10.5523 12.5523 11 12 11C11.4477 11 11 10.5523 11 10C11 9.44771 11.4477 9 12 9ZM11 14C11 13.4477 11.4477 13 12 13C12.5523 13 13 13.4477 13 14C13 14.5523 12.5523 15 12 15C11.4477 15 11 14.5523 11 14ZM16 13C15.4477 13 15 13.4477 15 14C15 14.5523 15.4477 15 16 15C16.5523 15 17 14.5523 17 14C17 13.4477 16.5523 13 16 13ZM13 18C13 17.4477 12.5523 17 12 17C11.4477 17 11 17.4477 11 18C11 18.5523 11.4477 19 12 19C12.5523 19 13 18.5523 13 18ZM16 17C16.5523 17 17 17.4477 17 18C17 18.5523 16.5523 19 16 19C15.4477 19 15 18.5523 15 18C15 17.4477 15.4477 17 16 17ZM15 10C15 9.44771 15.4477 9 16 9C16.5523 9 17 9.44771 17 10C17 10.5523 16.5523 11 16 11C15.4477 11 15 10.5523 15 10Z"
      fill="currentColor"
      fillRule="evenodd"
    />
  ),
});

export const StarIcon = createIcon({
  displayName: 'StarIcon',
  viewBox: '0 0 14 14',
  path: (
    <>
      <g clipPath="url(#clip0_828_753)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.6961 6.37199C13.9717 6.10343 14.0689 5.70928 13.9501 5.34288C13.8309 4.97648 13.5206 4.71492 13.1393 4.65942L9.74952 4.16687C9.60514 4.14584 9.4804 4.05529 9.41592 3.92437L7.90042 0.853009C7.73025 0.507914 7.38488 0.293457 6.99998 0.293457C6.61536 0.293457 6.26998 0.507914 6.09982 0.853009L4.58404 3.92465C4.51956 4.05557 4.39453 4.14612 4.25016 4.16715L0.86033 4.6597C0.479353 4.71492 0.168741 4.97676 0.0495976 5.34316C-0.0692651 5.70956 0.0280116 6.10371 0.303582 6.37227L2.75625 8.76298C2.86081 8.86502 2.90875 9.01192 2.88408 9.15545L2.30546 12.5313C2.25416 12.8284 2.3321 13.1174 2.52441 13.3454C2.82325 13.7005 3.34495 13.8088 3.76209 13.5895L6.79365 11.9955C6.92036 11.9291 7.07987 11.9297 7.20631 11.9955L10.2381 13.5895C10.3856 13.6672 10.5429 13.7064 10.7052 13.7064C11.0015 13.7064 11.2824 13.5747 11.4755 13.3454C11.6681 13.1174 11.7458 12.8279 11.6945 12.5313L11.1156 9.15545C11.0909 9.01164 11.1389 8.86502 11.2434 8.76298L13.6961 6.37199ZM9.63718 9.40898C9.63717 9.40895 9.63718 9.409 9.63718 9.40898C9.52862 8.77595 9.74027 8.13399 10.1958 7.68945L11.9295 5.99937L9.53383 5.65128C8.90219 5.55929 8.35415 5.1627 8.07076 4.58811C8.0706 4.58778 8.07043 4.58745 8.07027 4.58711L7.0001 2.41827L5.92969 4.58739C5.92952 4.58774 5.92934 4.58809 5.92917 4.58844C5.64519 5.16418 5.09664 5.55969 4.46632 5.65149L2.07028 5.99964L3.80325 7.68884C4.25875 8.13334 4.47104 8.77613 4.36252 9.40886C4.36248 9.40908 4.36256 9.40863 4.36252 9.40886L3.95369 11.7941L6.09709 10.6671C6.66332 10.3702 7.34197 10.3748 7.89945 10.6653L7.90434 10.6678L10.0462 11.7939L9.63718 9.40898ZM12.6487 5.29826C12.6485 5.29843 12.6488 5.29809 12.6487 5.29826V5.29826Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_828_753">
          <rect width="14" height="14" fill="white" />
        </clipPath>
      </defs>
    </>
  ),
});
