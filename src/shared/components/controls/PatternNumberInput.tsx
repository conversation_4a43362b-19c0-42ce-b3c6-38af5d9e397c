import {
  FormErrorIcon,
  FormErrorMessage,
  Input,
  InputGroup,
  InputRightElement,
  useMultiStyleConfig,
} from '@chakra-ui/react';
import { forwardRef } from 'react';
import { FiAlertCircle } from 'react-icons/fi';
import { PatternFormat, type PatternFormatProps } from 'react-number-format';

import { InputWrapper } from './common';

type Props = {
  label?: string;
  hint?: string;
  error?: string;
} & Omit<PatternFormatProps, 'customInput' | 'size'>;

export const PatternNumberInput = forwardRef<HTMLInputElement, Props>(
  ({ label, name, error, hint, disabled, ...rest }, ref) => {
    const styles = useMultiStyleConfig('Input', {});
    return (
      <InputWrapper
        error={error}
        hint={hint}
        isDisabled={disabled}
        label={label}
        name={name}
      >
        <InputGroup>
          <PatternFormat
            _focus={{
              ...(styles.field as any)._focus,
              pr: error ? 'calc(3rem - 1px)' : undefined,
            }}
            customInput={Input}
            disabled={disabled}
            getInputRef={ref}
            name={name}
            style={{
              paddingRight: error ? 12 : undefined,
            }}
            {...rest}
          />

          <InputRightElement width={12}>
            <FormErrorMessage>
              <FormErrorIcon as={FiAlertCircle} boxSize={6} color="red.700" />
            </FormErrorMessage>
          </InputRightElement>
        </InputGroup>
      </InputWrapper>
    );
  },
);

PatternNumberInput.displayName = 'PatternNumberInput';
