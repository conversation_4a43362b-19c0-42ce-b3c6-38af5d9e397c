import {
  type ChakraProps,
  FormControl,
  FormErrorMessage,
  FormHelperText,
  FormLabel,
  Text,
} from '@chakra-ui/react';
import type { PropsWithChildren } from 'react';

export type SelectItemType<VType = any> = {
  value: VType;
  label: string;
};
export type BaseInputProps = {
  label?: React.ReactNode;
  subText?: string;
  name?: string;
  error?: string;
  hint?: React.ReactNode;
  extraState?: React.ReactNode;
  isDisabled?: boolean;
} & ChakraProps;

export const InputWrapper = ({
  name,
  label,
  subText,
  isDisabled,
  error,
  hint,
  extraState,
  children,
  ...chakraProps
}: PropsWithChildren<BaseInputProps>): JSX.Element => (
  <FormControl
    id={`${name}-input`}
    {...chakraProps}
    isDisabled={isDisabled}
    isInvalid={!!error}
    pb={hint || error || extraState ? 0 : '19px'}
  >
    {!!label &&
      (subText ? (
        <FormLabel fontSize="16px" fontWeight="700" mb={[1]}>
          {label}
        </FormLabel>
      ) : (
        <FormLabel>{label}</FormLabel>
      ))}
    {!!subText && (
      <Text fontSize="14px" fontWeight="400" mb={[2]}>
        {subText}
      </Text>
    )}
    {children}
    {!!hint && !error && !extraState && <FormHelperText>{hint}</FormHelperText>}
    {!!extraState && !error && (
      <Text
        color="primary.700"
        mt={1}
        textAlign="end"
        textStyle="caption-highlight"
      >
        {extraState}
      </Text>
    )}
    <FormErrorMessage data-cy="error-message">{error}</FormErrorMessage>
  </FormControl>
);
