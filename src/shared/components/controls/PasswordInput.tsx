import { Button, Input, InputGroup, InputRightElement } from '@chakra-ui/react';
import { forwardRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { InputWrapper } from './common';
import type { TextInputProps } from './TextInput';

export type PasswordInputProps = Omit<
  TextInputProps,
  'startElement' | 'endElement' | 'type'
>;

export const PasswordInput = forwardRef<HTMLInputElement, PasswordInputProps>(
  (
    {
      label,
      name,
      hint,
      value,
      isDisabled,
      autoComplete,
      error,
      onChange,
      ...chackraProps
    },
    ref,
  ) => {
    const { t } = useTranslation();
    const [isShown, setIsShown] = useState(false);
    return (
      <InputWrapper
        error={error}
        hint={hint}
        isDisabled={isDisabled}
        label={label}
        name={name}
        {...chackraProps}
      >
        <InputGroup>
          <Input
            autoComplete={autoComplete}
            name={name}
            onChange={onChange}
            pr="5.5rem"
            ref={ref}
            type={isShown ? 'text' : 'password'}
            value={value}
          />

          <InputRightElement justifyContent="flex-end" width="5.5rem">
            <Button
              height="1.25rem"
              isDisabled={isDisabled}
              mr="0.5rem"
              onClick={() => {
                setIsShown((prev) => !prev);
              }}
              px="0.5rem"
              size="sm"
              variant="ghost"
            >
              {t(`forms.${isShown ? 'password-hide' : 'password-show'}`)}
            </Button>
          </InputRightElement>
        </InputGroup>
      </InputWrapper>
    );
  },
);
PasswordInput.displayName = 'PasswordInput';
