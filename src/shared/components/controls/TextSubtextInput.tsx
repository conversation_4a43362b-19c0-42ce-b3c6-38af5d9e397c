import { FormControl, Input, InputGroup, Text } from '@chakra-ui/react';
import { forwardRef } from 'react';

import type { BaseInputProps } from './common';

export type TextSubtextInputProps = {
  type?: string;
  value?: string;
  inputMode?: React.HTMLAttributes<HTMLInputElement>['inputMode'];
  placeholder?: string;
  subText?: string;
} & BaseInputProps;

export const TextSubtextInput = forwardRef<
  HTMLInputElement,
  TextSubtextInputProps
>(
  (
    {
      type = 'text',
      label,
      subText,
      placeholder,
      name,
      value,
      isDisabled,
      inputMode,
    },
    ref,
  ) => {
    return (
      <FormControl>
        {!!subText && (
          <>
            <Text mb={[1]} textStyle="h4">
              {label}
            </Text>

            <Text fontSize="14px" fontWeight="400" mb={[2]}>
              {subText}
            </Text>
          </>
        )}
        <InputGroup>
          <Input
            bg="neutral.100"
            inputMode={inputMode}
            isReadOnly={isDisabled}
            name={name}
            placeholder={placeholder}
            ref={ref}
            type={type}
            value={value}
          />
        </InputGroup>
      </FormControl>
    );
  },
);
TextSubtextInput.displayName = 'TextSubtextInput';
