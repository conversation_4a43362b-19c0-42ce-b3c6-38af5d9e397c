import type { PropsWithChildren } from 'react';
import { useCurrentUser } from 'shared/hooks/user';

import { Loader } from '../index';
import { DefaultRedirect } from './DefaultRedirect';

/**
 * Guest guard for routes having no auth required
 * @param {PropTypes.node} children children element/node
 */

const GuestGuard = ({ children }: PropsWithChildren) => {
  const { loading, user } = useCurrentUser();

  if (loading) {
    return <Loader fullScreen />;
  }

  if (user) {
    return <DefaultRedirect />;
  }

  return <>{children}</>;
};

export { GuestGuard };
