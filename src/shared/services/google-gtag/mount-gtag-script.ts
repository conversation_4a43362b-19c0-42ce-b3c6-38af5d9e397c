let isMounted = false;

/**
 * Mounts the Google Global Site Tag script programmatically only once.
 *
 * This function covers the same steps as the official Google Global Site
 * Tag documentation but in a programmatic way instead of hardcoding elements
 * in the html.
 */
export function mountGoogleGtagScriptOnlyOnce(measurementId: string): void {
  if (isMounted) {
    // console.warn('Attempted to mount the Google Global Site Tag script but it is already mounted.');
    return;
  }

  mountGoogleGtagScript(measurementId);
  isMounted = true;
}

/**
 * Mounts the Google Global Site Tag script programmatically.
 *
 * This function covers the same steps as the official Google Global Site
 * Tag documentation but in a programmatic way instead of hardcoding elements
 * in the html.
 *
 * @see https://developers.google.com/analytics/devguides/collection/gtagjs
 */
function mountGoogleGtagScript(measurementId: string): void {
  const gtmScript = document.createElement('script');
  gtmScript.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
  gtmScript.async = true;

  const firstScriptInHead = document.getElementsByTagName('script')[0];
  const parentElement = firstScriptInHead.parentNode;
  if (parentElement) parentElement.insertBefore(gtmScript, firstScriptInHead);
}
