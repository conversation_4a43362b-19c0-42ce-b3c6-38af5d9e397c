import { config } from 'shared/services/google-gtag/config';
import { mountGoogleGtagScriptOnlyOnce } from 'shared/services/google-gtag/mount-gtag-script';

let isInitialized = false;

function init(): void {
  if (!config.isEnabled || isInitialized) {
    return;
  }

  mountGoogleGtagScriptOnlyOnce(config.containerId);
  // Create the gtag global function
  window.gtag = standardGtag;
  isInitialized = true;

  // Initialize the main container id, then the extra ones.
  gtag('js', new Date());
  gtag('config', config.containerId);

  for (const id of config.extraContainerIds) {
    gtag('config', id);
  }
}

/**
 * Default implementation of the gtag function as per documentation.
 */
function standardGtag(): void {
  // eslint-disable-next-line prefer-rest-params
  window.dataLayer.push(arguments);
}

function gtag(...args: any[]): void {
  if (config.isEnabled && isInitialized) {
    window.gtag(...args);
  }
}

export const googleGtag = {
  init,
  gtag,
};
