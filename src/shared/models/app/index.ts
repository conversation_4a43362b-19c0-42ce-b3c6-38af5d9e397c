import { createEffect, createEvent, createStore, sample } from 'effector';
import i18n from 'shared/lib/i18n';
import { authModel } from 'shared/models/auth';
import { getLangFromAbbr } from 'shared/utils';

const initAppEv = createEvent('triggered on app init');
const $isAppInitialized = createStore(false).on(initAppEv, () => true);

// change app language on user language change
sample({
  clock: authModel.$userLanguageAbbr,
  filter: Boolean,
  target: createEffect((languageAbbr: string) => {
    const lang = getLangFromAbbr(languageAbbr);

    if (!lang) {
      console.debug(`Language ${languageAbbr} is not supported`);
      return;
    }

    if (lang !== i18n.language) {
      i18n.changeLanguage(lang);
    }
  }),
});

export const appModel = {
  $isAppInitialized,
  initAppEv,
};
