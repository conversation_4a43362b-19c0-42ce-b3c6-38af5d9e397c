import { createEvent, restore } from 'effector';

type UserData = {
  id: number;
  firstName?: string | null;
  lastName?: string | null;
  email?: string | null;
  languageAbbr?: string;
};

const setUserEv = createEvent<UserData | null>(
  'triggered when user authorized',
);
const $user = restore(setUserEv, null);

const $isUserAuthorized = $user.map((user) => !!user);
const $userLanguageAbbr = $user.map((user) => user?.languageAbbr);

export const authModel = {
  $isUserAuthorized,
  $user,
  setUserEv,
  $userLanguageAbbr,
};
