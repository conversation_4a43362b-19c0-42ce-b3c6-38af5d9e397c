import { ResetPasswordSecondStep } from 'modules/resetPassword';
import { useTranslation } from 'react-i18next';
import { LoggedOutLayout } from 'shared/components';
import { DesktopHeader, MobileHeader } from 'shared/components/login/Headers';
import { usePageTitle } from 'shared/hooks/app';

const ResetPasswordPage = () => {
  const { t } = useTranslation('reset-password');

  usePageTitle(t('second-step.page-title'));

  return (
    <LoggedOutLayout desktopHeader={DesktopHeader} mobileHeader={MobileHeader}>
      <ResetPasswordSecondStep />
    </LoggedOutLayout>
  );
};

export default ResetPasswordPage;
