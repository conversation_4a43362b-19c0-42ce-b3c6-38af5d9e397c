import { Box } from '@chakra-ui/react';
import { MerchantUrls } from 'modules/developer-tools';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';
import {
  LocizeCommonKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { usePageTitle } from 'shared/hooks/app';

const DevToolsUrlsPage: FC = () => {
  const { t } = useTranslation(LocizeNamespaces.COMMON);

  usePageTitle(t(LocizeCommonKeys.SIDEBAR_FOR_DEVELOPERS_OPTION));

  return (
    <Box maxW="400px" mx="auto" pb={['48px', null, '100px']} pt={[6, null, 14]}>
      <MerchantUrls />
    </Box>
  );
};

export default DevToolsUrlsPage;
