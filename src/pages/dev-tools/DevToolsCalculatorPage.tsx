import { Box } from '@chakra-ui/react';
import { CalculatorColorsList } from 'modules/developer-tools/calculator/CalculatorColorsList';
import { CalculatorDescription } from 'modules/developer-tools/calculator/CalculatorDescription';
import { CalculatorIFrameLink } from 'modules/developer-tools/calculator/CalculatorIFrameLink';
import { CalculatorParameters } from 'modules/developer-tools/calculator/CalculatorParameters';
import { CalculatorPreview } from 'modules/developer-tools/calculator/CalculatorPreview';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  LocizeCommonKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { usePageTitle } from 'shared/hooks/app';
import { useMerchantDetails } from 'shared/hooks/merchant';
import { availableLanguages, calculatorIFrameUrl } from 'shared/lib';
import {
  type CalculatorForm,
  CalculatorTypes,
  ExtendLanguageAliases,
} from 'shared/types/calculator';

const DevToolsCalculatorPage = () => {
  const { t } = useTranslation(LocizeNamespaces.COMMON);
  const innerRef = useRef(null);
  const { data: merchantData } = useMerchantDetails();

  const defaultForm = useMemo(
    () => ({
      type: CalculatorTypes.ViewOnly,
      language: ExtendLanguageAliases[availableLanguages[0]],
      amount: 0,
      width: 400,
      height: 320,
    }),
    [],
  );
  const [calculatorForm, setCalculatorForm] =
    useState<CalculatorForm>(defaultForm);
  const [changedValues, setChangedValues] = useState<Array<string>>([]);
  const [isReload, setReload] = useState(false);
  const { type, language, amount, width, height } = calculatorForm;

  const composeIFrameLink = (): string => {
    const baseUrl = `${calculatorIFrameUrl}calculator?`;
    return `${baseUrl}shop_id=${
      merchantData?.merchant?.shop_id
    }&type=${type}&lang=${language}${amount ? `&amount=${amount}` : ''}${
      isReload ? ' ' : ''
    }`;
  };

  const composeHtml = (): string => {
    const iFrameUrl = composeIFrameLink();
    return `<iframe style="border: 0;" width="${width}" height="${height}" src="${iFrameUrl}"></iframe>`;
  };

  useEffect(() => {
    setReload(false);
  }, [isReload]);

  useEffect(() => {
    for (const item of Object.keys(calculatorForm)) {
      if (
        calculatorForm[item as keyof CalculatorForm] !==
          defaultForm[item as keyof CalculatorForm] &&
        !changedValues.includes(`calculator.parameters-${item}`)
      )
        setChangedValues([
          ...changedValues,
          t(`calculator.parameters-${item}`),
        ]);
    }
  }, [calculatorForm, changedValues, defaultForm, t]);

  usePageTitle(t(LocizeCommonKeys.SIDEBAR_FOR_DEVELOPERS_OPTION));

  return (
    <>
      <Box
        maxW="680px"
        mx="auto"
        pb={['48px', null, '100px']}
        pt={[5, null, 14]}
        w="100%"
      >
        <CalculatorDescription />
        <Box
          display="flex"
          flexDirection={['column', 'row']}
          justifyContent="space-between"
          mb="56px"
        >
          <CalculatorParameters
            calculatorForm={calculatorForm}
            onCalculatorFormSet={setCalculatorForm}
          />
          <CalculatorColorsList innerRef={innerRef} onReloadSet={setReload} />
        </Box>
        <CalculatorPreview
          height={height}
          iFrameUrl={composeIFrameLink()}
          width={width}
        />
        <CalculatorIFrameLink
          changedValues={changedValues}
          htmlString={composeHtml()}
          iFrameUrl={composeIFrameLink()}
        />
      </Box>
      <Box ref={innerRef} />
    </>
  );
};

export default DevToolsCalculatorPage;
