import { Box } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { Navigate } from 'react-router-dom';
import { Header, MobileNavigationTab } from 'shared/components';
import {
  LocizeCommonKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { DevToolsTabRoute } from 'shared/constants/routes';
import { usePageTitle } from 'shared/hooks/app';
import { useIsMobileLayout } from 'shared/hooks/layout';
import { DevToolsRouteTabs } from 'shared/types';

const DEV_TOOLS_NAV_TABS = [
  DevToolsRouteTabs.Setup,
  DevToolsRouteTabs.Urls,
  DevToolsRouteTabs.Calculator,
] as const;

const DevToolsPage = () => {
  const isMobileLayout = useIsMobileLayout();
  const { t } = useTranslation('dev-tools');
  const { t: tc } = useTranslation(LocizeNamespaces.COMMON);

  usePageTitle(tc(LocizeCommonKeys.SIDEBAR_FOR_DEVELOPERS_OPTION));

  // on desktop layout we redirect directly to setup page
  if (!isMobileLayout) {
    return (
      <Navigate
        replace={true}
        to={DevToolsTabRoute.format({ tab: DevToolsRouteTabs.Setup })}
      />
    );
  }

  return (
    <>
      <Header title={tc(LocizeCommonKeys.SIDEBAR_FOR_DEVELOPERS_OPTION)} />
      <Box>
        {DEV_TOOLS_NAV_TABS.map((tab) => (
          <MobileNavigationTab
            key={tab}
            label={t(`tabs.${tab}`)}
            to={DevToolsTabRoute.format({ tab })}
          />
        ))}
      </Box>
    </>
  );
};

export default DevToolsPage;
