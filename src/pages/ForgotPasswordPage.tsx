import {
  FirstStepMobileHeader,
  ResetPasswordFirstStep,
} from 'modules/resetPassword';
import { useTranslation } from 'react-i18next';
import { LoggedOutLayout } from 'shared/components';
import { DesktopHeader } from 'shared/components/login/Headers';
import { usePageTitle } from 'shared/hooks/app';

const ForgotPasswordPage = () => {
  const { t } = useTranslation('reset-password');

  usePageTitle(t('first-step.page-title'));

  return (
    <LoggedOutLayout
      desktopHeader={DesktopHeader}
      mobileHeader={FirstStepMobileHeader}
    >
      <ResetPasswordFirstStep />
    </LoggedOutLayout>
  );
};

export default ForgotPasswordPage;
