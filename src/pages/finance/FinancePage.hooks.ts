import { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

import { DashboardTabs } from './FinancePage.types';

export const useFinanceTabs = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const selectedDashboardTab = searchParams.get('tab') as DashboardTabs;

  const handleDashboardTabChange = (tab: DashboardTabs) => {
    setSearchParams(
      { tab },
      {
        replace: true,
      },
    );
  };

  useEffect(() => {
    if (Object.values(DashboardTabs).includes(selectedDashboardTab)) {
      return;
    }

    setSearchParams(
      { tab: DashboardTabs.TRANSACTIONS },
      {
        replace: true,
      },
    );
  }, [selectedDashboardTab, setSearchParams]);

  return { selectedDashboardTab, handleDashboardTabChange };
};
