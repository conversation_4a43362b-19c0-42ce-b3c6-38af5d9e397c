import { VStack } from '@chakra-ui/react';
import { FinanceNewViewInfo } from 'modules/finance/components';
import { InvoicesTable } from 'modules/finance/invoices-table';
import { MerchantBalanceInfo } from 'modules/finance/merchant-balance-info';
import { TransactionsTable } from 'modules/finance/transactions-table';
import { useTranslation } from 'react-i18next';
import { Header, Loader, Toggle, ToggleItem } from 'shared/components';
import {
  LocizeCommonKeys,
  LocizeFinanceKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { usePageTitle } from 'shared/hooks/app';
import { useMerchantDetails } from 'shared/hooks/merchant';
import { useEffectOnce, useLocalStorage } from 'shared/hooks/utility';

import { useFinanceTabs } from './FinancePage.hooks';
import { DashboardTabs } from './FinancePage.types';

const TABS = [
  {
    label: LocizeFinanceKeys.TAB_TRANSACTIONS_OPTION,
    value: DashboardTabs.TRANSACTIONS,
  },
  {
    label: LocizeFinanceKeys.TAB_INVOICES_OPTION,
    value: DashboardTabs.INVOICES,
  },
] as const;

export const FinancePage = () => {
  const { t } = useTranslation(LocizeNamespaces.FINANCE);
  const { t: tc } = useTranslation(LocizeNamespaces.COMMON);

  const { loading } = useMerchantDetails();

  const [isFinanceNewViewInfoClosed, setFinanceNewViewInfoClosed] =
    useLocalStorage('isFinanceNewViewInfoClosed', false);
  const [isFinancesNewViewOpened, setFinancesNewViewOpened] =
    useLocalStorage<boolean>('isFinancesNewViewOpened', false);

  const { selectedDashboardTab, handleDashboardTabChange } = useFinanceTabs();

  const handleFinanceNewViewInfoClose = () => {
    setFinanceNewViewInfoClosed(true);
  };

  useEffectOnce(() => {
    if (isFinancesNewViewOpened) {
      return;
    }

    setFinancesNewViewOpened(true);
  });

  usePageTitle(tc(LocizeCommonKeys.SIDEBAR_FINANCES_OPTION));

  if (loading) {
    return <Loader />;
  }

  return (
    <>
      <Header title={tc(LocizeCommonKeys.SIDEBAR_FINANCES_OPTION)} />
      <VStack
        align="stretch"
        height={{ md: '100%' }}
        p={{ base: '32px 20px 28px', md: '40px 20px 28px' }}
        position="relative"
        spacing={6}
      >
        {!isFinanceNewViewInfoClosed && (
          <FinanceNewViewInfo onCloseClick={handleFinanceNewViewInfoClose} />
        )}
        <MerchantBalanceInfo />
        <VStack
          align="stretch"
          height="100%"
          minHeight={400}
          mt="16px"
          overflow="hidden"
          spacing={6}
        >
          <Toggle
            name="finance-dashboard-view"
            onChange={handleDashboardTabChange}
            value={selectedDashboardTab}
            w="100%"
          >
            {TABS.map(({ label, value }) => (
              <ToggleItem key={label} value={value}>
                {t(label)}
              </ToggleItem>
            ))}
          </Toggle>
          {selectedDashboardTab === DashboardTabs.TRANSACTIONS && (
            <TransactionsTable />
          )}
          {selectedDashboardTab === DashboardTabs.INVOICES && <InvoicesTable />}
        </VStack>
      </VStack>
    </>
  );
};
