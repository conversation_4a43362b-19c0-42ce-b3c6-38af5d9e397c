import { Box } from '@chakra-ui/react';
import { StoresSettings } from 'modules/settings';
import { useTranslation } from 'react-i18next';
import { usePageTitle } from 'shared/hooks/app';

const StoresSettingsPage = () => {
  const { t } = useTranslation('common');

  usePageTitle(t('sidebar.settings'));

  return (
    <Box
      maxW="400px"
      mx="auto"
      pb={['48px', null, '100px']}
      pt={[5, null, 14]}
      w="100%"
    >
      <StoresSettings />
    </Box>
  );
};

export default StoresSettingsPage;
