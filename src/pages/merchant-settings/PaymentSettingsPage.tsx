import { Box } from '@chakra-ui/react';
import { PaymentSettings } from 'modules/settings/payment';
import { useTranslation } from 'react-i18next';
import { usePageTitle } from 'shared/hooks/app';

const PaymentSettingsPage = () => {
  const { t } = useTranslation('common');

  usePageTitle(t('sidebar.settings'));

  return (
    <Box maxW="400px" mx="auto" pt={[5, null, 14]} w="100%">
      <PaymentSettings />
    </Box>
  );
};

export default PaymentSettingsPage;
