import { Box } from '@chakra-ui/react';
import { MerchantSettings } from 'modules/settings';
import { useTranslation } from 'react-i18next';
import { usePageTitle } from 'shared/hooks/app';

const MerchantSettingsPage = () => {
  const { t } = useTranslation('common');

  usePageTitle(t('sidebar.settings'));

  return (
    <Box
      maxW="400px"
      mx="auto"
      pb={['48px', null, '100px']}
      pt={[5, null, 14]}
      w="100%"
    >
      <MerchantSettings />
    </Box>
  );
};

export default MerchantSettingsPage;
