import { Box } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { Navigate } from 'react-router-dom';
import { Header, MobileNavigationTab } from 'shared/components';
import {
  LocizeCommonKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { SettingsTabRoute } from 'shared/constants/routes';
import { usePageTitle } from 'shared/hooks/app';
import { useIsMobileLayout } from 'shared/hooks/layout';
import { SettingsRouteTabs } from 'shared/types';

const MERCHANT_SETTINGS_NAV_TABS = [
  SettingsRouteTabs.Merchant,
  SettingsRouteTabs.Payment,
  SettingsRouteTabs.Stores,
  SettingsRouteTabs.Employees,
] as const;

const SettingsPage = () => {
  const isMobileLayout = useIsMobileLayout();
  const { t: tc } = useTranslation(LocizeNamespaces.COMMON);
  const { t } = useTranslation('settings');

  usePageTitle(tc(LocizeCommonKeys.SIDEBAR_SETTINGS_OPTION));

  // on desktop layout we redirect directly to merchant settings page
  if (!isMobileLayout) {
    return (
      <Navigate
        replace={true}
        to={SettingsTabRoute.format({ tab: SettingsRouteTabs.Merchant })}
      />
    );
  }

  return (
    <>
      <Header title={tc(LocizeCommonKeys.SIDEBAR_SETTINGS_OPTION)} />
      <Box>
        {MERCHANT_SETTINGS_NAV_TABS.map((tab) => (
          <MobileNavigationTab
            key={tab}
            label={t(`tabs.${tab}`)}
            to={SettingsTabRoute.format({ tab })}
          />
        ))}
      </Box>
    </>
  );
};

export default SettingsPage;
