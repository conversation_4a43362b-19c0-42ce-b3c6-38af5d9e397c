import { Box } from '@chakra-ui/react';
import {
  ApplicationsTable,
  ApplicationsTableDesktopControls,
  ApplicationTableHeaderControls,
} from 'modules/applications/list';
import { useTranslation } from 'react-i18next';
import { Loader } from 'shared/components';
import { usePageTitle } from 'shared/hooks/app';
import { useIsMobileLayout } from 'shared/hooks/layout';
import { useMerchantDetails } from 'shared/hooks/merchant';

const ApplicationsTablePage = () => {
  const { t } = useTranslation('common');
  const { loading } = useMerchantDetails();
  const isMobileLayout = useIsMobileLayout();

  usePageTitle(t('sidebar.applications'));

  if (loading) {
    return <Loader />;
  }

  return (
    <>
      {isMobileLayout ? (
        <ApplicationTableHeaderControls />
      ) : (
        <ApplicationsTableDesktopControls />
      )}
      <Box
        mt={{
          base: 5,
          md: 0,
        }}
        pb={['48px', null, '100px']}
        px="20px"
      >
        <ApplicationsTable />
      </Box>
    </>
  );
};

export default ApplicationsTablePage;
