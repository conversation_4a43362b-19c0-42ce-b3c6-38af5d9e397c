import { Box, Divider, Icon, Icon<PERSON>utton, VStack } from '@chakra-ui/react';
import {
  ApplicationInfo,
  ApplicationModifications,
  ApplicationRefund,
  DesktopHeader,
  RefundHistory,
  ResendLink,
} from 'modules/applications/details';
import { ApplicationRequestHistory } from 'modules/applications/details/requests-history';
import { useTranslation } from 'react-i18next';
import { FiArrowLeft } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import { ApplicationScheduleType, ApplicationStatus } from 'shared/api';
import { Header, Loader } from 'shared/components';
import {
  LocizeCommonKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { ApplicationListRoute } from 'shared/constants/routes';
import { usePageTitle } from 'shared/hooks/app';
import { useCurrentMerchantApplicationDetails } from 'shared/hooks/application';
import { useIsMobileLayout } from 'shared/hooks/layout';
import { MIN_AMOUNT_REFUND_REQUEST } from 'shared/types';

const ApplicationDetailsPage = () => {
  const { t: tc } = useTranslation(LocizeNamespaces.COMMON);
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);
  const isMobileLayout = useIsMobileLayout();
  const { loading, data } = useCurrentMerchantApplicationDetails();
  const application = data?.application;

  const isShowRefundActions =
    !!application &&
    application.is_refundable &&
    application.refunded_amount <=
      application.requested_amount - MIN_AMOUNT_REFUND_REQUEST;

  const isModificationCancellationHistoryExist =
    !!application?.modification_requests?.some(
      (request) => !!request?.handled_at,
    ) || !!(application?.cancelled_at && application?.cancellation_request);

  usePageTitle(tc(LocizeCommonKeys.SIDEBAR_APPLICATIONS_OPTION));

  const minMaxModificationAmount = {
    min: application?.credit_info?.down_payment ?? 0,
    max: application?.requested_amount ?? 0,
  };

  if (loading) {
    return <Loader />;
  }

  return (
    <>
      <Header
        before={
          <IconButton
            aria-label="back"
            as={Link}
            icon={<Icon as={FiArrowLeft} boxSize={6} color="primary.800" />}
            isRound
            mx={2}
            my={1}
            to={ApplicationListRoute.format()}
            variant="ghost"
          />
        }
        isSidebarButtonEnabled={false}
        title={t('details.title')}
      />
      {!isMobileLayout && <DesktopHeader />}
      <Box
        maxW="800px"
        mx="auto"
        pb={['48px', null, '100px']}
        pt="20px"
        px="20px"
        w="100%"
      >
        {!!application && (
          <>
            <ApplicationInfo {...application} />
            {![
              ApplicationScheduleType.ESTO_PAY,
              ApplicationScheduleType.SMALL_LOAN,
            ].includes(application.schedule_type) && (
              <>
                {application.status === ApplicationStatus.UNSIGNED && (
                  <ResendLink
                    id={application.id}
                    purchase_url={application.purchase_url}
                  />
                )}

                {!!(
                  [
                    ApplicationStatus.ACTIVE,
                    ApplicationStatus.CANCELLED,
                  ].includes(application.status) ||
                  isModificationCancellationHistoryExist
                ) && (
                  <VStack
                    alignItems="stretch"
                    border="1px solid"
                    borderColor="neutral.150"
                    borderRadius="4px"
                    overflow={{ base: 'hidden', xl: 'unset' }}
                    p={4}
                    spacing={4}
                  >
                    {!application.cancellation_request &&
                      application.status === ApplicationStatus.ACTIVE && (
                        <ApplicationModifications
                          id={application.id}
                          latest_modification_request={
                            application.latest_modification_request
                          }
                          requested_amount={application.requested_amount}
                          schedule_type={application.schedule_type}
                          minMaxModificationAmount={minMaxModificationAmount}
                        />
                      )}
                    {!application.cancellation_request &&
                      application.status === ApplicationStatus.ACTIVE &&
                      !!isModificationCancellationHistoryExist && <Divider />}
                    <ApplicationRequestHistory
                      cancellation_request={application?.cancellation_request}
                      cancelled_at={application.cancelled_at}
                      modification_requests={application.modification_requests}
                    />
                  </VStack>
                )}
              </>
            )}
            {!!application.direct_payment_refunds?.length && (
              <RefundHistory refunds={application.direct_payment_refunds} />
            )}
            {!!isShowRefundActions && (
              <ApplicationRefund
                direct_payment_refunds={application.direct_payment_refunds}
                id={application.id}
                refunded_amount={application.refunded_amount}
                requested_amount={application.requested_amount}
              />
            )}
          </>
        )}
      </Box>
    </>
  );
};

export default ApplicationDetailsPage;
