import { faker } from '@faker-js/faker';
import get from 'lodash/get';
import { terminalFormDefaultValue } from 'modules/terminal/Terminal.constants';
import { serializeTerminalApplicationData } from 'modules/terminal/Terminal.utils';
import {
  ApplicationScheduleType,
  UserDocumentType,
  UserMessageSentType,
} from 'shared/api';
import { CypressTerminalKeys } from 'shared/constants/cypress-keys/terminal-page';
import { phonePrefix } from 'shared/lib';

import {
  adminMerchantsMock,
  CreateApplicationMutationMocks,
  CreditSettingsMock,
  merchantMock,
  pricingMock,
  SendPurchaseUrlMocks,
  userMock,
} from '../../../cypress/mocks';
import TerminalPage from './TerminalPage';

const mockData = {
  amount: faker.number.int({ min: 50, max: 2000 }),
  amountWithDownPaymentSmall: faker.number.int({ min: 50, max: 1400 }),
  amountWithDownPaymentBig: faker.number.int({ min: 1400, max: 2000 }),
  largeAmount: faker.number.int({ min: 2000, max: 10000 }),
  reference: faker.lorem.words({ min: 1, max: 3 }),
  email: faker.internet.email(),
  phone: faker.string.numeric('######'),
  pin: faker.string.numeric('####'),
  documentType: UserDocumentType.ID_CARD,
  documentNumber: faker.string.numeric('##########'),
} as const;

describe('<TerminalPage /> without down payment', () => {
  beforeEach(() => {
    cy.viewport(1920, 1080);

    cy.mountWithProvider(<TerminalPage />, {
      apolloMocks: [
        merchantMock,
        userMock,
        pricingMock.noDownPayment,
        adminMerchantsMock,
        CreditSettingsMock[ApplicationScheduleType.REGULAR](mockData.amount),
        CreditSettingsMock[ApplicationScheduleType.ESTO_X](mockData.amount),
        SendPurchaseUrlMocks.successFlow({
          email: mockData.email,
          userMessageType: UserMessageSentType.EMAIL,
        }),
        SendPurchaseUrlMocks.successFlow({
          phone: mockData.phone,
          userMessageType: UserMessageSentType.SMS,
        }),
        CreateApplicationMutationMocks.errorFlow(
          serializeTerminalApplicationData({
            ...terminalFormDefaultValue,
            merchantId: 1,
            scheduleType: ApplicationScheduleType.REGULAR,
            isRequiredMerchantDownPayment: false,
            amount: mockData.largeAmount,
            reference: mockData.reference,
            personalInfoPhone: mockData.phone,
            userMessageSentType: UserMessageSentType.EMAIL,
            storeId: 491,
            documentType: mockData.documentType,
            documentNr: mockData.documentNumber,
            pin: mockData.pin,
          }),
        ),
        CreateApplicationMutationMocks.successFlow(
          serializeTerminalApplicationData({
            ...terminalFormDefaultValue,
            merchantId: 1,
            scheduleType: ApplicationScheduleType.REGULAR,
            isRequiredMerchantDownPayment: false,
            amount: mockData.amount,
            reference: mockData.reference,
            personalInfoEmail: mockData.email,
            userMessageSentType: UserMessageSentType.EMAIL,
            storeId: 491,
          }),
        ),
      ],
    });
  });

  it('first render -> store type is physical + auto select first store + purchase details step should be visible', () => {
    cy.dataCy(CypressTerminalKeys.STORE_TYPE_SELECTOR_PHYSICAL_OPTION).should(
      'have.attr',
      'aria-selected',
      'true',
    );
    cy.dataCy(CypressTerminalKeys.PHYSICAL_STORE_SELECTOR)
      .dataCy('select-value')
      .contains(get(merchantMock, 'result.data.merchant.stores.0.name') || '');

    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_STEP).should('be.visible');
  });

  it('store selection step -> physical store select should not be visible if the the store type is only', () => {
    cy.dataCy(CypressTerminalKeys.STORE_TYPE_SELECTOR_ONLINE_OPTION).click();
    cy.dataCy(CypressTerminalKeys.PHYSICAL_STORE_SELECTOR).should('not.exist');
  });

  it('purchase details step -> validation check + not display payment selection step if not valid', () => {
    cy.dataCy(CypressTerminalKeys.PAYMENT_PLAN_SELECTION_STEP).should(
      'not.exist',
    );

    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT)
      .type('1{backspace}')
      .dataCy('error-message')
      .should('exist');
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT)
      .click()
      .type(mockData.amount.toString())
      .dataCy('error-message')
      .should('not.exist');

    cy.dataCy(CypressTerminalKeys.PAYMENT_PLAN_SELECTION_STEP).should(
      'not.exist',
    );

    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_REFERENCE_INPUT)
      .find('input')
      .first()
      .type(mockData.amount.toString())
      .clear()
      .dataCy('error-message')
      .should('exist');
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_REFERENCE_INPUT)
      .type(mockData.reference.toString())
      .dataCy('error-message')
      .should('not.exist');

    cy.dataCy(CypressTerminalKeys.PAYMENT_PLAN_SELECTION_STEP).should('exist');
  });

  it('payment plan selection step -> first item auto select + selected item popup display + display personal info step if valid', () => {
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT).type(
      mockData.amount.toString(),
    );
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_REFERENCE_INPUT).type(
      mockData.reference.toString(),
    );

    cy.dataCy(`${CypressTerminalKeys.PAYMENT_PLAN_SELECTION_OPTION}-0`).should(
      'have.attr',
      'aria-selected',
      'true',
    );
    cy.dataCy(
      `${CypressTerminalKeys.PAYMENT_PLAN_SELECTION_OPTION}-0-popover`,
    ).should('be.visible');

    cy.dataCy(`${CypressTerminalKeys.PAYMENT_PLAN_SELECTION_OPTION}-1`)
      .click()
      .should('have.attr', 'aria-selected', 'true');
    cy.dataCy(
      `${CypressTerminalKeys.PAYMENT_PLAN_SELECTION_OPTION}-1-popover`,
    ).should('exist');
    cy.dataCy(`${CypressTerminalKeys.PAYMENT_PLAN_SELECTION_OPTION}-0`).should(
      'have.attr',
      'aria-selected',
      'false',
    );
    cy.dataCy(
      `${CypressTerminalKeys.PAYMENT_PLAN_SELECTION_OPTION}-0-popover`,
    ).should('not.be.visible');

    cy.dataCy(CypressTerminalKeys.PERSONAL_INFO_STEP).should('exist');
  });

  it('personal info step -> validation check + display test by store type', () => {
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT).type(
      mockData.amount.toString(),
    );
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_REFERENCE_INPUT).type(
      mockData.reference.toString(),
    );

    cy.dataCy(CypressTerminalKeys.PERSONAL_INFO_FIRST_NAME_INPUT).should(
      'not.exist',
    );
    cy.dataCy(CypressTerminalKeys.PERSONAL_INFO_LAST_NAME_INPUT).should(
      'not.exist',
    );
    cy.dataCy(CypressTerminalKeys.PERSONAL_INFO_EMAIL_INPUT).should(
      'not.exist',
    );
    cy.dataCy(CypressTerminalKeys.PERSONAL_INFO_PHONE_NUMBER_INPUT).should(
      'not.exist',
    );
    cy.dataCy(CypressTerminalKeys.PERSONAL_INFO_LANGUAGE_SELECTOR).should(
      'exist',
    );

    cy.dataCy(CypressTerminalKeys.SEND_LINK_STEP).should('exist');

    cy.dataCy(CypressTerminalKeys.STORE_TYPE_SELECTOR_ONLINE_OPTION).click();

    cy.dataCy(CypressTerminalKeys.PERSONAL_INFO_FIRST_NAME_INPUT).should(
      'exist',
    );
    cy.dataCy(CypressTerminalKeys.PERSONAL_INFO_LAST_NAME_INPUT).should(
      'exist',
    );
    cy.dataCy(CypressTerminalKeys.PERSONAL_INFO_PHONE_NUMBER_INPUT).should(
      'exist',
    );
    cy.dataCy(CypressTerminalKeys.PERSONAL_INFO_EMAIL_INPUT)
      .should('exist')
      .find('input')
      .type('1')
      .dataCy('error-message')
      .should('exist');

    cy.dataCy(CypressTerminalKeys.PERSONAL_INFO_EMAIL_INPUT)
      .find('input')
      .clear()
      .type(mockData.email)
      .dataCy('error-message')
      .should('not.exist');

    cy.dataCy(CypressTerminalKeys.SEND_LINK_STEP).should('exist');

    cy.dataCy(
      `${CypressTerminalKeys.PERSONAL_INFO_LANGUAGE_SELECTOR_OPTION}-et`,
    ).should('have.attr', 'aria-selected', 'true');

    cy.dataCy(
      `${CypressTerminalKeys.PERSONAL_INFO_LANGUAGE_SELECTOR_OPTION}-en`,
    )
      .click()
      .should('have.attr', 'aria-selected', 'true');
  });

  it('document identification step -> show if amount is greater then merchant settings limit + validation check', () => {
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT).type(
      mockData.largeAmount.toString(),
    );
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_REFERENCE_INPUT).type(
      mockData.reference.toString(),
    );

    cy.dataCy(CypressTerminalKeys.DOCUMENT_IDENTIFICATION_STEP).should('exist');
    cy.dataCy(CypressTerminalKeys.SEND_LINK_STEP).should('not.exist');

    cy.dataCy(CypressTerminalKeys.DOCUMENT_IDENTIFICATION_PIN_INPUT)
      .find('input')
      .should('be.empty')
      .type('1{backspace}')
      .dataCy('error-message')
      .should('exist');
    cy.dataCy(CypressTerminalKeys.DOCUMENT_IDENTIFICATION_PIN_INPUT)
      .click()
      .type('1')
      .dataCy('error-message')
      .should('not.exist');

    cy.dataCy(
      `${CypressTerminalKeys.DOCUMENT_IDENTIFICATION_DOCUMENT_TYPE_SELECTOR_OPTION}-0`,
    ).should('have.attr', 'aria-selected', 'true');
    cy.dataCy(
      `${CypressTerminalKeys.DOCUMENT_IDENTIFICATION_DOCUMENT_TYPE_SELECTOR_OPTION}-1`,
    )
      .click()
      .should('have.attr', 'aria-selected', 'true');

    cy.dataCy(CypressTerminalKeys.DOCUMENT_IDENTIFICATION_DOCUMENT_NUMBER_INPUT)
      .find('input')
      .should('be.empty')
      .type('1{backspace}')
      .dataCy('error-message')
      .should('exist');
    cy.dataCy(CypressTerminalKeys.DOCUMENT_IDENTIFICATION_DOCUMENT_NUMBER_INPUT)
      .click()
      .type('1')
      .dataCy('error-message')
      .should('not.exist');

    cy.dataCy(CypressTerminalKeys.SEND_LINK_STEP).should('exist');
  });

  it('send link step -> validation check + check sync with online store personal info', () => {
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT).type(
      mockData.amount.toString(),
    );
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_REFERENCE_INPUT).type(
      mockData.reference.toString(),
    );

    cy.dataCy(CypressTerminalKeys.SUBMIT_ACTION_BUTTON_SEND_LINK).should(
      'be.disabled',
    );
    cy.dataCy(CypressTerminalKeys.SUBMIT_ACTION_BUTTON_GENERATE_QR_CODE).should(
      'be.enabled',
    );
    cy.dataCy(CypressTerminalKeys.SUBMIT_ACTION_BUTTON_COPY_LINK).should(
      'be.enabled',
    );
    cy.dataCy(CypressTerminalKeys.SUBMIT_ACTION_BUTTON_OPEN_IN_NEW_TAB).should(
      'be.enabled',
    );

    cy.dataCy(CypressTerminalKeys.SEND_LINK_PHONE_NUMBER_INPUT).type(
      mockData.phone,
    );
    cy.dataCy(CypressTerminalKeys.SUBMIT_ACTION_BUTTON_SEND_LINK).should(
      'not.be.disabled',
    );

    cy.dataCy(CypressTerminalKeys.SEND_LINK_TYPE_SELECTOR_EMAIL_OPTION).click();
    cy.dataCy(CypressTerminalKeys.SUBMIT_ACTION_BUTTON_SEND_LINK).should(
      'be.disabled',
    );
    cy.dataCy(CypressTerminalKeys.SEND_LINK_EMAIL_INPUT).type(mockData.email);
    cy.dataCy(CypressTerminalKeys.SUBMIT_ACTION_BUTTON_SEND_LINK).should(
      'not.be.disabled',
    );

    cy.dataCy(CypressTerminalKeys.STORE_TYPE_SELECTOR_ONLINE_OPTION).click();

    cy.dataCy(CypressTerminalKeys.PERSONAL_INFO_EMAIL_INPUT)
      .find('input')
      .should('have.value', mockData.email);
    cy.dataCy(CypressTerminalKeys.PERSONAL_INFO_PHONE_NUMBER_INPUT)
      .invoke('val')
      .then((value) =>
        cy
          .wrap((value as string).replaceAll(' ', ''))
          .should('eq', phonePrefix + mockData.phone),
      );
  });

  it('success flow by sending a link with email', () => {
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT).type(
      mockData.amount.toString(),
    );
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_REFERENCE_INPUT).type(
      mockData.reference.toString(),
    );

    cy.dataCy(CypressTerminalKeys.SEND_LINK_TYPE_SELECTOR_EMAIL_OPTION).click();
    cy.dataCy(CypressTerminalKeys.SEND_LINK_EMAIL_INPUT).type(mockData.email);

    cy.dataCy(CypressTerminalKeys.SUBMIT_ACTION_BUTTON_SEND_LINK).click();
  });

  it('success flow by sending a link with phone', () => {
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT).type(
      mockData.amount.toString(),
    );
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_REFERENCE_INPUT).type(
      mockData.reference.toString(),
    );

    cy.dataCy(CypressTerminalKeys.SEND_LINK_PHONE_NUMBER_INPUT).type(
      mockData.phone,
    );

    cy.dataCy(CypressTerminalKeys.SUBMIT_ACTION_BUTTON_SEND_LINK).click();
  });

  it('error flow by incorrect document info', () => {
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT).type(
      mockData.largeAmount.toString(),
    );
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_REFERENCE_INPUT).type(
      mockData.reference.toString(),
    );

    cy.dataCy(CypressTerminalKeys.DOCUMENT_IDENTIFICATION_PIN_INPUT).type(
      mockData.pin,
    );
    cy.dataCy(
      CypressTerminalKeys.DOCUMENT_IDENTIFICATION_DOCUMENT_NUMBER_INPUT,
    ).type(mockData.documentNumber);

    cy.dataCy(CypressTerminalKeys.SEND_LINK_PHONE_NUMBER_INPUT).type(
      mockData.phone,
    );

    cy.dataCy(CypressTerminalKeys.SUBMIT_ACTION_BUTTON_SEND_LINK).click();

    cy.get('[id^="alert-"][id$="-error"]').should('exist');
  });

  it('reset form button', () => {
    cy.dataCy(CypressTerminalKeys.STORE_TYPE_SELECTOR_ONLINE_OPTION).click();

    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT).type(
      mockData.amount.toString(),
    );
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_REFERENCE_INPUT).type(
      mockData.reference.toString(),
    );

    cy.dataCy(CypressTerminalKeys.PAYMENT_PLAN_SELECTION_STEP).should('exist');
    cy.dataCy(CypressTerminalKeys.PERSONAL_INFO_STEP).should('exist');
    cy.dataCy(CypressTerminalKeys.SEND_LINK_STEP).should('exist');

    cy.dataCy(CypressTerminalKeys.RESET_BUTTON).click();
    cy.dataCy(
      `${CypressTerminalKeys.RESET_BUTTON_CONFIRM}-confirmation-popover-content`,
    )
      .dataCy(
        `${CypressTerminalKeys.RESET_BUTTON_CONFIRM}-confirmation-action-button`,
      )
      .click();

    cy.dataCy(CypressTerminalKeys.STORE_TYPE_SELECTOR_PHYSICAL_OPTION).should(
      'have.attr',
      'aria-selected',
      'true',
    );
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT).should(
      'have.value',
      '0 €',
    );
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_REFERENCE_INPUT)
      .find('input')
      .should('have.value', '');

    cy.dataCy(CypressTerminalKeys.PAYMENT_PLAN_SELECTION_STEP).should(
      'not.exist',
    );
  });

  it('practice mode toggler + practice mode disclaimer', () => {
    cy.dataCy(CypressTerminalKeys.PRACTICE_MODE_TOGGLER).should(
      'not.have.attr',
      'data-checked',
    );
    cy.dataCy(CypressTerminalKeys.PRACTICE_MODE_TOGGLER).click();
    cy.dataCy(CypressTerminalKeys.PRACTICE_MODE_DISCLAIMER).should('exist');
    cy.dataCy(CypressTerminalKeys.PRACTICE_MODE_TOGGLER).click();
    cy.dataCy(CypressTerminalKeys.PRACTICE_MODE_TOGGLER).should(
      'not.have.attr',
      'data-checked',
    );
    cy.dataCy(CypressTerminalKeys.PRACTICE_MODE_DISCLAIMER).should('not.exist');
  });

  it('amount calculator', () => {
    cy.dataCy(CypressTerminalKeys.CALCULATOR_BUTTON).click();
    cy.dataCy(CypressTerminalKeys.CALCULATOR_FORM).should('exist');

    cy.dataCy(CypressTerminalKeys.CALCULATOR_FORM_AMOUNT_INPUT)
      .should('be.empty')
      .type(mockData.amount.toString());

    cy.dataCy(CypressTerminalKeys.CALCULATOR_FORM_CANCEL_BUTTON).click();
    cy.dataCy(CypressTerminalKeys.CALCULATOR_FORM).should('not.exist');

    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT).should(
      'be.empty',
    );

    cy.dataCy(CypressTerminalKeys.CALCULATOR_BUTTON).click();
    cy.dataCy(
      CypressTerminalKeys.CALCULATOR_FORM_PAYMENT_PLAN_EMPTY_INFO,
    ).should('exist');
    cy.dataCy(CypressTerminalKeys.CALCULATOR_FORM_AMOUNT_INPUT).type(
      mockData.amount.toString(),
    );

    cy.dataCy(
      `${CypressTerminalKeys.CALCULATOR_FORM_PAYMENT_PLAN_OPTION}-0`,
    ).should('have.attr', 'aria-selected', 'true');
    cy.dataCy(CypressTerminalKeys.CALCULATOR_FORM_PAYMENT_PLAN_INFO).should(
      'exist',
    );

    cy.dataCy(CypressTerminalKeys.CALCULATOR_FORM_SUBMIT_BUTTON).click();

    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT).should(
      'have.value',
      `${mockData.amount} €`,
    );

    cy.dataCy(CypressTerminalKeys.CALCULATOR_BUTTON).click();

    cy.dataCy(CypressTerminalKeys.CALCULATOR_FORM_AMOUNT_INPUT).should(
      'have.value',
      `${mockData.amount} €`,
    );
  });

  // TODO: mock navigate
  // TODO: check send link bottom action
});

describe('<TerminalPage /> with down payment', () => {
  beforeEach(() => {
    cy.viewport(1920, 1080);

    cy.mountWithProvider(<TerminalPage />, {
      apolloMocks: [
        merchantMock,
        userMock,
        pricingMock.downPayment,
        adminMerchantsMock,
        CreditSettingsMock[ApplicationScheduleType.REGULAR](
          mockData.amountWithDownPaymentSmall,
        ),
        CreditSettingsMock[ApplicationScheduleType.ESTO_X](
          mockData.amountWithDownPaymentSmall,
        ),
        CreditSettingsMock[ApplicationScheduleType.REGULAR](
          mockData.amountWithDownPaymentBig -
            mockData.amountWithDownPaymentBig / 10,
        ),
        CreditSettingsMock[ApplicationScheduleType.ESTO_X](
          mockData.amountWithDownPaymentBig -
            mockData.amountWithDownPaymentBig / 10,
        ),
      ],
    });
  });

  it('down payment', () => {
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_DOWN_PAYMENT_INPUT).should(
      'exist',
    );

    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT).type(
      mockData.amountWithDownPaymentSmall.toString(),
    );
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_REFERENCE_INPUT).type(
      mockData.reference.toString(),
    );

    cy.dataCy(CypressTerminalKeys.PAYMENT_PLAN_SELECTION_STEP).should('exist');

    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_AMOUNT_INPUT)
      .clear()
      .click()
      .type(mockData.amountWithDownPaymentBig.toString());

    cy.dataCy(CypressTerminalKeys.PAYMENT_PLAN_SELECTION_STEP).should(
      'not.exist',
    );

    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_DOWN_PAYMENT_INPUT)
      .type('1{backspace}')
      .dataCy('error-message')
      .should('exist');
    cy.dataCy(CypressTerminalKeys.PURCHASE_DETAILS_DOWN_PAYMENT_INPUT)
      .click()
      .type((mockData.amountWithDownPaymentBig / 10).toString())
      .dataCy('error-message')
      .should('not.exist');

    cy.dataCy(CypressTerminalKeys.PAYMENT_PLAN_SELECTION_STEP).should('exist');
  });
});
