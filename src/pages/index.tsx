import * as Sentry from '@sentry/react';
import AccountPage from 'pages/AccountPage';
import ApplicationDetailsPage from 'pages/ApplicationDetailsPage';
import ApplicationListPage from 'pages/ApplicationsTablePage';
import DevToolsCalculatorPage from 'pages/dev-tools/DevToolsCalculatorPage';
import DevToolsSetupPage from 'pages/dev-tools/DevToolsSetupPage';
import DevToolsUrlsPage from 'pages/dev-tools/DevToolsUrlsPage';
import ForgotPasswordPage from 'pages/ForgotPasswordPage';
import EmployeesSettingsPage from 'pages/merchant-settings/EmployeesSettingsPage';
import MerchantSettingsPage from 'pages/merchant-settings/MerchantSettingsPage';
import PaymentSettingsPage from 'pages/merchant-settings/PaymentSettingsPage';
import ResetPasswordPage from 'pages/ResetPasswordPage';
import SignUpPage from 'pages/SignUpPage';
import { createBrowserRouter } from 'react-router-dom';
import {
  DefaultRedirect,
  GuestGuard,
  PermissionGuard,
} from 'shared/components/router';
import {
  AccountRoute,
  ApplicationDetailsRoute,
  ApplicationListRoute,
  FinanceRoute,
  ForDeveloperCalculatorRoute,
  ForDeveloperRoute,
  ForDeveloperSetupRoute,
  ForDeveloperUrlsRoute,
  ForgotPasswordRoute,
  LoginRoute,
  ResetPasswordRoute,
  SettingsRoute,
  SettingsTabRoute,
  SignUpRoute,
  TerminalApplicationRoute,
  TerminalRoute,
} from 'shared/constants/routes';
import { AuthorizedLayout } from 'shared/layouts/AuthorizedLayout';
import { DevToolsLayout } from 'shared/layouts/dev-tools';
import { MerchantSettingsLayout } from 'shared/layouts/merchant-settings';
import { partnerV1Url } from 'shared/lib';
import { MerchantPermissions, SettingsRouteTabs } from 'shared/types';

import DevToolsPage from './dev-tools';
import FinancePage from './finance';
import LoginPage from './login';
import SettingsPage from './merchant-settings';
import StoresSettingsPage from './merchant-settings/stores';
import TerminalPage from './terminal';
import TerminalApplicationPage from './terminal-application';

const sentryCreateBrowserRouter =
  Sentry.wrapCreateBrowserRouter(createBrowserRouter);

const router = sentryCreateBrowserRouter([
  {
    path: '*',
    element: <DefaultRedirect />,
  },
  {
    path: SignUpRoute.template,
    element: (
      <GuestGuard>
        <SignUpPage />
      </GuestGuard>
    ),
  },
  {
    path: LoginRoute.template,
    element: (
      <GuestGuard>
        <LoginPage />
      </GuestGuard>
    ),
  },
  {
    path: ForgotPasswordRoute.template,
    element: (
      <GuestGuard>
        <ForgotPasswordPage />
      </GuestGuard>
    ),
  },
  {
    path: ResetPasswordRoute.template,
    element: (
      <GuestGuard>
        <ResetPasswordPage />
      </GuestGuard>
    ),
  },
  {
    element: <AuthorizedLayout />,
    children: [
      {
        path: TerminalRoute.template,
        element: (
          <PermissionGuard roles={[MerchantPermissions.Cashier]}>
            <TerminalPage />
          </PermissionGuard>
        ),
      },
      {
        path: TerminalApplicationRoute.template,
        element: (
          <PermissionGuard roles={[MerchantPermissions.Cashier]}>
            <TerminalApplicationPage />
          </PermissionGuard>
        ),
      },
      {
        path: ApplicationListRoute.template,
        element: (
          <PermissionGuard
            roles={[
              MerchantPermissions.Admin,
              MerchantPermissions.Accountant,
              MerchantPermissions.Cashier,
            ]}
          >
            <ApplicationListPage />
          </PermissionGuard>
        ),
      },
      {
        path: ApplicationDetailsRoute.template,
        element: (
          <PermissionGuard
            roles={[
              MerchantPermissions.Admin,
              MerchantPermissions.Accountant,
              MerchantPermissions.Cashier,
            ]}
          >
            <ApplicationDetailsPage />
          </PermissionGuard>
        ),
      },
      {
        path: '/applications-old',
        element: (
          <PermissionGuard
            notImplementedRedirect={`${partnerV1Url}/applications-old`}
            roles={[
              MerchantPermissions.Admin,
              MerchantPermissions.Accountant,
              MerchantPermissions.Cashier,
            ]}
          />
        ),
      },
      {
        path: FinanceRoute.template,
        element: (
          <PermissionGuard
            roles={[MerchantPermissions.Admin, MerchantPermissions.Accountant]}
          >
            <FinancePage />
          </PermissionGuard>
        ),
      },
      {
        path: ForDeveloperRoute.template,
        element: (
          <PermissionGuard
            roles={[MerchantPermissions.Developer, MerchantPermissions.Admin]}
          >
            <DevToolsPage />
          </PermissionGuard>
        ),
      },
      {
        element: <DevToolsLayout />,
        children: [
          {
            path: ForDeveloperSetupRoute.template,
            element: (
              <PermissionGuard
                roles={[
                  MerchantPermissions.Developer,
                  MerchantPermissions.Admin,
                ]}
              >
                <DevToolsSetupPage />
              </PermissionGuard>
            ),
          },
          {
            path: ForDeveloperUrlsRoute.template,
            element: (
              <PermissionGuard
                roles={[
                  MerchantPermissions.Developer,
                  MerchantPermissions.Admin,
                ]}
              >
                <DevToolsUrlsPage />
              </PermissionGuard>
            ),
          },
          {
            path: ForDeveloperCalculatorRoute.template,
            element: (
              <PermissionGuard
                roles={[
                  MerchantPermissions.Developer,
                  MerchantPermissions.Admin,
                ]}
              >
                <DevToolsCalculatorPage />
              </PermissionGuard>
            ),
          },
        ],
      },
      {
        path: SettingsRoute.template,
        element: (
          <PermissionGuard roles={[MerchantPermissions.Admin]}>
            <SettingsPage />
          </PermissionGuard>
        ),
      },
      {
        element: <MerchantSettingsLayout />,
        children: [
          {
            path: SettingsTabRoute.format({ tab: SettingsRouteTabs.Merchant }),
            element: (
              <PermissionGuard roles={[MerchantPermissions.Admin]}>
                <MerchantSettingsPage />
              </PermissionGuard>
            ),
          },
          {
            path: SettingsTabRoute.format({ tab: SettingsRouteTabs.Payment }),
            element: (
              <PermissionGuard roles={[MerchantPermissions.Admin]}>
                <PaymentSettingsPage />
              </PermissionGuard>
            ),
          },
          {
            path: SettingsTabRoute.format({ tab: SettingsRouteTabs.Stores }),
            element: (
              <PermissionGuard roles={[MerchantPermissions.Admin]}>
                <StoresSettingsPage />
              </PermissionGuard>
            ),
          },
          {
            path: SettingsTabRoute.format({ tab: SettingsRouteTabs.Employees }),
            element: (
              <PermissionGuard roles={[MerchantPermissions.Admin]}>
                <EmployeesSettingsPage />
              </PermissionGuard>
            ),
          },
        ],
      },

      {
        path: AccountRoute.template,
        element: (
          <PermissionGuard
            roles={[
              MerchantPermissions.Admin,
              MerchantPermissions.Developer,
              MerchantPermissions.Cashier,
              MerchantPermissions.Accountant,
            ]}
          >
            <AccountPage />
          </PermissionGuard>
        ),
      },
      // {
      // 	path: BusinessLoanRoute.template,
      // 	element: (
      // 		<PermissionGuard
      // 			regions={[AppRegions.ET]}
      // 			roles={[MerchantPermissions.Admin]}
      // 		>
      // 			<BusinessLoanPage />
      // 		</PermissionGuard>
      // 	),
      // },
    ],
  },
]);

export default router;
