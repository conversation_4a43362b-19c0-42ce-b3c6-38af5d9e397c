import {
  ButtonVariants,
  IconNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_PENDING_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import {
  AppButton,
  AppIcon,
  AppLoader,
  AppLocalizationComponent,
} from 'components';
import { usePendingPageLogic } from 'hooks/page-logic/hire-purchase';
import { useTranslation } from 'react-i18next';

import styles from './Pending.module.scss';

const Page = () => {
  const { t } = useTranslation(LocizeNamespaces.pending);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const {
    processingPendingPage,
    visiblePageAttributes,
    pageUrlAndNavigationProcessing,
    debtAmount,
    isRedirecting,
    onEstoAccountButtonClick,
    onCheckIncomeButtonClick,
    onPayDebtButtonClick,
    onCreditLineOnboardingButtonClick,
    onCreditLineWithdrawalOnboardingButtonClick,
    availableCreditLimit,
    creditLimit,
  } = usePendingPageLogic();

  if (processingPendingPage) {
    return <AppLoader isRelative />;
  }

  return (
    <div className={styles.container}>
      <AppIcon className={styles['clock-icon']} name={IconNames.clock} />

      {visiblePageAttributes[PageAttributeNames.overdueDisclaimer] ? (
        <p className={styles.heading}>
          {t(LOCIZE_PENDING_TRANSLATION_KEYS.debtHeading)}
        </p>
      ) : (
        <p className={styles.heading}>
          {t(LOCIZE_PENDING_TRANSLATION_KEYS.pendingHeading)}
        </p>
      )}

      {visiblePageAttributes[PageAttributeNames.defaultPendingDisclaimer] ? (
        <p className={styles.paragraph}>
          {t(LOCIZE_PENDING_TRANSLATION_KEYS.pendingDisclaimer)}
        </p>
      ) : null}

      {visiblePageAttributes[PageAttributeNames.cawAvailableUpToDisclaimer] ? (
        <AppLocalizationComponent
          className={styles.paragraph}
          locizeKey={
            LOCIZE_PENDING_TRANSLATION_KEYS.creditLineWithdrawalOnboardingDisclaimer
          }
          t={t}
          values={{
            availableCreditLimit,
          }}
        />
      ) : null}

      {visiblePageAttributes[
        PageAttributeNames.unsignedCaWithLimitDisclaimer
      ] ? (
        <AppLocalizationComponent
          className={styles.paragraph}
          locizeKey={
            LOCIZE_PENDING_TRANSLATION_KEYS.creditLineOnboardingDisclaimer
          }
          t={t}
          values={{
            creditLimit,
          }}
        />
      ) : null}

      {visiblePageAttributes[PageAttributeNames.checkIncomeDisclaimer] ? (
        <p className={styles.paragraph}>
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.checkIncomeDisclaimer)}
        </p>
      ) : null}

      {visiblePageAttributes[
        PageAttributeNames.waitingSpouseConsentPendingDisclaimer
      ] ? (
        <p className={styles.paragraph}>
          {t(LOCIZE_PENDING_TRANSLATION_KEYS.spouseConsentPendingDisclaimer)}
        </p>
      ) : null}

      {visiblePageAttributes[
        PageAttributeNames.manualScoringNeededDisclaimer
      ] ? (
        <p className={styles.paragraph}>
          {t(LOCIZE_PENDING_TRANSLATION_KEYS.manualScoringNeededDisclaimer)}
        </p>
      ) : null}

      {visiblePageAttributes[
        PageAttributeNames.outsideWorkingHoursPendingDisclaimer
      ] ? (
        <p className={styles.paragraph}>
          {t(
            LOCIZE_PENDING_TRANSLATION_KEYS.outsideWorkingHoursPendingDisclaimer,
          )}
        </p>
      ) : null}

      {visiblePageAttributes[PageAttributeNames.overdueDisclaimer] &&
      debtAmount ? (
        <AppLocalizationComponent
          className={styles.paragraph}
          locizeKey={LOCIZE_PENDING_TRANSLATION_KEYS.debtDisclaimer}
          values={{
            debt: debtAmount,
          }}
          t={t}
        />
      ) : null}

      <div className={styles.buttons}>
        {visiblePageAttributes[
          PageAttributeNames.unsignedCaWithLimitDisclaimer
        ] ? (
          <AppButton
            className={styles.button}
            label={t(
              LOCIZE_PENDING_TRANSLATION_KEYS.creditLineOnboardingCTAButtonLabel,
            )}
            onClick={onCreditLineOnboardingButtonClick}
            variant={ButtonVariants.white}
            isDisabled={isRedirecting}
          />
        ) : null}

        {visiblePageAttributes[
          PageAttributeNames.cawAvailableUpToDisclaimer
        ] ? (
          <AppButton
            className={styles.button}
            label={t(
              LOCIZE_PENDING_TRANSLATION_KEYS.creditLineWithdrawalOnboardingCTAButtonLabel,
            )}
            onClick={onCreditLineWithdrawalOnboardingButtonClick}
            variant={ButtonVariants.white}
            isDisabled={isRedirecting}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.estoAccountSection] ? (
          <AppButton
            className={styles.button}
            label={tc(LOCIZE_COMMON_TRANSLATION_KEYS.estoAccount)}
            onClick={onEstoAccountButtonClick}
            variant={ButtonVariants.primary}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.checkIncomeButton] ? (
          <AppButton
            className={styles.button}
            isDisabled={pageUrlAndNavigationProcessing}
            label={t(LOCIZE_PENDING_TRANSLATION_KEYS.checkIncomeButtonLabel)}
            onClick={onCheckIncomeButtonClick}
            variant={ButtonVariants.primary}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.overdueDisclaimer] ? (
          <AppButton
            className={styles.button}
            label={t(LOCIZE_PENDING_TRANSLATION_KEYS.payDebtButton)}
            onClick={onPayDebtButtonClick}
            variant={ButtonVariants.primary}
          />
        ) : null}
      </div>
    </div>
  );
};

export default Page;
