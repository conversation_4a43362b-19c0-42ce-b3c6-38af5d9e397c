import { ApplicationState } from 'modules/terminal-application';
import type { MerchantApplicationQuery } from 'shared/api';
import { useQueryParams } from 'shared/constants/routes';
import {
  useApplicationId,
  useMerchantApplication,
} from 'shared/hooks/application';
import { ApplicationLinkAction } from 'shared/types';
import { isEnumValue } from 'shared/utils';

export const useApplicationState = (
  data?: MerchantApplicationQuery,
): ApplicationState => {
  if (data?.merchant_application?.rejected_at) {
    return ApplicationState.REJECTED;
  }

  if (data?.merchant_application?.signed_at) {
    return ApplicationState.SIGNED;
  }

  return ApplicationState.PENDING;
};

export const useAction = (): ApplicationLinkAction => {
  const query = useQueryParams();
  const action = query.action;

  if (
    typeof action === 'string' &&
    isEnumValue(ApplicationLinkAction)(action)
  ) {
    return action;
  }

  return ApplicationLinkAction.SendLink;
};

export const useMerchantApplicationById = () => {
  const applicationId = useApplicationId();
  return useMerchantApplication(applicationId);
};

export const useIsPracticeMode = () => {
  const { data } = useMerchantApplicationById();
  return !!data?.merchant_application?.is_test;
};
