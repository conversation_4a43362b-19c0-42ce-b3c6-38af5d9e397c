import { Box, Button, Center, SimpleGrid, Text } from '@chakra-ui/react';
import { useIsCashierBonusEnabled } from 'modules/applications/list/controls/hooks';
import {
  ActionsBlock,
  ApplicationState,
  ApplicationStatus,
} from 'modules/terminal-application';
import QRCode from 'qrcode.react';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { ApplicationScheduleType } from 'shared/api';
import { Header, Loader } from 'shared/components';
import {
  LocizeCommonKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { usePageTitle } from 'shared/hooks/app';
import { useApplicationId } from 'shared/hooks/application';
import { useIsMobileLayout } from 'shared/hooks/layout';
import { ApplicationLinkAction } from 'shared/types';

import {
  useAction,
  useApplicationState,
  useIsPracticeMode,
  useMerchantApplicationById,
} from './TerminalApplicationPage.hooks';

const TerminalApplicationPage = () => {
  const { t } = useTranslation(LocizeNamespaces.TERMINAL);
  const { t: tc } = useTranslation(LocizeNamespaces.COMMON);
  const terminalAction = useAction();
  const isMobileLayout = useIsMobileLayout();
  const applicationId = useApplicationId();
  const isCashierBonusEnabled = useIsCashierBonusEnabled();
  // TODO: Process error
  const { loading, data } = useMerchantApplicationById();
  const applicationState = useApplicationState(data);

  const cashierBonusAmount =
    data?.merchant_application?.credit_info?.cashier_bonus_amount;

  const shouldShowCashierBonus =
    isCashierBonusEnabled &&
    !!cashierBonusAmount &&
    data?.merchant_application?.schedule_type ===
      ApplicationScheduleType.REGULAR &&
    applicationState === ApplicationState.SIGNED;

  const isPracticeMode = useIsPracticeMode();

  const navigate = useNavigate();
  const onNewApplication = useCallback(() => {
    navigate('/terminal');
  }, [navigate]);

  const onExitPracticeMode = useCallback(() => {
    navigate('/terminal');
  }, [navigate]);

  const purchaseFlowUrl = data?.merchant_application?.purchase_url ?? '';
  const isFromRetail = data?.merchant_application?.from_retail ?? true;

  usePageTitle(tc(LocizeCommonKeys.SIDEBAR_TERMINAL_OPTION));

  if (loading) {
    return <Loader />;
  }

  return (
    <SimpleGrid gap={4} gridAutoRows="auto 1fr" height="100%">
      <Header
        after={
          isPracticeMode ? (
            <Text ml="auto" mr={6} textStyle="body2-highlight">
              {t('practice-mode.on')}
            </Text>
          ) : undefined
        }
        title={tc(LocizeCommonKeys.SIDEBAR_TERMINAL_OPTION)}
      />
      <Box>
        {!!isPracticeMode && !isMobileLayout && (
          <Box alignItems="center" display="flex" p="20px">
            <Text ml={6} textStyle="body2-highlight">
              {t('practice-mode.on')}
            </Text>
          </Box>
        )}
        <Center height="100%" px="20px">
          <Box w={['100%', '400px']}>
            <Box
              alignItems="center"
              display="flex"
              flexDirection="column"
              mb={[8, 10]}
            >
              <ApplicationStatus
                withCashierBonus={shouldShowCashierBonus}
                cashierBonusAmount={cashierBonusAmount}
                action={terminalAction}
                isPracticeMode={isPracticeMode}
                isShowSubtitle={isFromRetail}
                state={applicationState}
              />
              {terminalAction === ApplicationLinkAction.GenerateQR &&
                applicationState === ApplicationState.PENDING && (
                  <Box mt={5}>
                    <QRCode size={142} value={purchaseFlowUrl} />
                  </Box>
                )}
            </Box>
            {applicationState === ApplicationState.PENDING && (
              <Box mb={[8, 10]}>
                <ActionsBlock
                  action={terminalAction}
                  applicationId={applicationId}
                  isFromRetail={isFromRetail}
                  isPracticeMode={isPracticeMode}
                  purchaseUrl={purchaseFlowUrl}
                />
              </Box>
            )}
            <Button
              data-cy="new-purchase-button"
              mb={3}
              onClick={onNewApplication}
              w="100%"
            >
              {t(
                isPracticeMode
                  ? 'application-status.new-practice-purchase'
                  : 'application-status.new-purchase',
              )}
            </Button>
            {!!isPracticeMode && (
              <Button
                data-cy="disable-practice-mode-button"
                onClick={onExitPracticeMode}
                w="100%"
              >
                {t('application-status.exit-practice-mode')}
              </Button>
            )}
          </Box>
        </Center>
      </Box>
    </SimpleGrid>
  );
};

export default TerminalApplicationPage;
