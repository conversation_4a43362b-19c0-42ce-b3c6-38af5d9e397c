import { ChakraProvider } from '@chakra-ui/react';
import {
  type PropsWithChildren,
  type ReactNode,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { getTheme } from 'shared/chakra-theme';
import i18n from 'shared/lib/i18n';
import type { AvailableLanguage } from 'shared/types';

export const AppChakraProvider = ({ children }: PropsWithChildren) => {
  const [lang, setLang] = useState(i18n.language);

  const theme = useMemo(
    () => getTheme({ language: lang as AvailableLanguage }),
    [lang],
  );

  useEffect(() => {
    i18n.on('languageChanged', setLang);

    return () => {
      i18n.off('languageChanged', setLang);
    };
  }, []);

  return <ChakraProvider theme={theme}>{children}</ChakraProvider>;
};

export const withChakra = (component: () => ReactNode) => () => {
  return <AppChakraProvider>{component()}</AppChakraProvider>;
};
