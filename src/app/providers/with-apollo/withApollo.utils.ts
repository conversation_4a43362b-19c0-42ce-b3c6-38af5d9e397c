import {
  ApolloClient,
  ApolloLink,
  HttpLink,
  InMemoryCache,
} from '@apollo/client';
import { onError } from '@apollo/client/link/error';
import { createBrowserHistory } from 'history';
import { LoginRoute } from 'shared/constants/routes';
import { isProd } from 'shared/lib';
import { authModel } from 'shared/models/auth';
import { LoginMethods } from 'shared/types';
import { isUnauthorizedError } from 'shared/utils';

const history = createBrowserHistory();

const adminOperations = ['adminMerchantsQuery'];

const createHttpLink = (uri: string | undefined): HttpLink =>
  new HttpLink({
    uri,
    credentials: 'include',
  });

const createAdminAwareHttpLink = (): ApolloLink => {
  const uri = process.env.REACT_APP_API_ENDPOINT;
  const adminUri = `${uri}/admin`;

  return ApolloLink.split(
    (op) => adminOperations.includes(op.operationName),
    createHttpLink(adminUri),
    createHttpLink(uri),
  );
};

// Redirect user to login page on auth error
const authErrorLink = onError(({ graphQLErrors }) => {
  if (
    graphQLErrors?.some(isUnauthorizedError) &&
    !location.pathname.includes('login')
  ) {
    authModel.setUserEv(null);
    history.push(
      LoginRoute.format({
        redirectUrl: location.pathname + location.search,
        method: LoginMethods.MobileId,
        sessionExpired: true,
      }),
    );
  }
});

export const apolloClient = new ApolloClient({
  link: authErrorLink.concat(createAdminAwareHttpLink()),
  cache: new InMemoryCache(),
  connectToDevTools: !isProd,
});
