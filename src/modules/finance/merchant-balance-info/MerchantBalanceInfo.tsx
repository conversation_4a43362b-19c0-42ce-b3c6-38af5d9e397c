import { HStack, Text, VStack } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { LocizeNamespaces } from 'shared/constants/localization-keys';

import { useMerchantBalanceCardsData } from './MerchantBalanceInfo.hooks';

export const MerchantBalanceInfo = () => {
  const { t } = useTranslation(LocizeNamespaces.FINANCE);
  const { balanceCardsData, isFetched } = useMerchantBalanceCardsData();

  return (
    <HStack flexWrap="wrap" spacing={{ base: 2, xl: 6 }}>
      {balanceCardsData.map(
        ({ balance, description, title, isIncreased, isVisible = true }) =>
          isVisible && (
            <VStack
              alignItems="stretch"
              border="1px solid var(--chakra-colors-neutral-100)"
              borderRadius="8px"
              flex="1"
              height="100%"
              key={title}
              minWidth="128px"
              p={4}
              spacing={0}
            >
              <Text mb="16px">{t(title)}</Text>
              <Text
                as="h4"
                color={isIncreased ? 'green.700' : ''}
                mb="8px"
                textStyle="h4"
              >
                {isFetched ? balance : '-'}
              </Text>
              <Text color="neutral.600" textStyle="caption">
                {t(description)}
              </Text>
            </VStack>
          ),
      )}
    </HStack>
  );
};
