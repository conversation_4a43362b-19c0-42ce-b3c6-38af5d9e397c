import { useReactiveVar } from '@apollo/client';
import {
  Button,
  type ChakraProps,
  HStack,
  Icon,
  Td as ChakraTd,
  Text,
  Tr,
} from '@chakra-ui/react';
import { format } from 'date-fns';
import type { PropsWithChildren } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { FiDownload } from 'react-icons/fi';
import { Loader } from 'shared/components';
import { useDateFnsLocale } from 'shared/components/date-range/common';
import {
  LocizeFinanceKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';

import { useInvoicesList } from '../InvoicesTable.hooks';
import { invoicesTableState } from '../InvoicesTable.utils';

type Props = {
  page: number;
  portalElement: HTMLDivElement | null;
};

export const InvoicesCollectionByPage = ({ page, portalElement }: Props) => {
  const locale = useDateFnsLocale();

  const { t, i18n } = useTranslation(LocizeNamespaces.FINANCE);
  const tableState = useReactiveVar(invoicesTableState);
  const { loading, data } = useInvoicesList({
    page,
  });

  if (loading) {
    return (
      portalElement && createPortal(<Loader height="5rem" />, portalElement)
    );
  }

  if (!data?.invoices?.data?.length) {
    return null;
  }

  return (
    <>
      {data?.invoices?.data.map((invoice) => {
        if (!invoice) {
          return null;
        }

        const { id, total_amount, invoice_nr, due_at, url } = invoice;

        return (
          <Tr key={id}>
            <Td>
              <Text>{invoice_nr}</Text>
            </Td>
            <Td>
              <Text>{format(new Date(due_at), 'd.MM.yyyy', { locale })}</Text>
            </Td>
            <Td>
              <Text>
                {`${total_amount.toLocaleString(i18n.language, {
                  maximumFractionDigits: 2,
                  minimumFractionDigits: 2,
                })}€`}
              </Text>
            </Td>
            <Td>
              {!!url && (
                <HStack
                  alignItems="center"
                  as="a"
                  cursor="pointer"
                  download
                  href={url}
                  justifyContent="flex-end"
                  spacing={2}
                  target="_blank"
                >
                  <Text>
                    {t(LocizeFinanceKeys.INVOICES_TABLE_DOWNLOAD_TEXT)}
                  </Text>
                  <Icon as={FiDownload} boxSize={4} />
                </HStack>
              )}
            </Td>
          </Tr>
        );
      })}
      {tableState.page === page &&
        !!data?.invoices.has_more_pages &&
        !!portalElement &&
        createPortal(
          <Button
            data-cy="transactions-load-more"
            onClick={() =>
              invoicesTableState({ ...tableState, page: tableState.page + 1 })
            }
            width="full"
          >
            {t(LocizeFinanceKeys.TABLE_LOAD_MORE_DATA_LABEL)}
          </Button>,
          portalElement,
        )}
    </>
  );
};

const Td = ({ children, ...props }: PropsWithChildren<ChakraProps>) => (
  <ChakraTd
    borderBottom="1px solid"
    borderColor="neutral.100"
    color="neutral.must"
    pl={[0, null, 3]}
    pr={5}
    py={[3, 2]}
    textStyle="body2"
    whiteSpace="nowrap"
    {...props}
  >
    {children}
  </ChakraTd>
);
