import { useContext } from 'react';
import { Direction } from 'shared/api';
import { GlobalStateContext } from 'shared/hooks/state';

import { useInvoicesListQuery } from './api';
import { INVOICES_TABLE_LIMIT_BY_PAGE } from './InvoicesTable.constants';

type UseInvoicesListParams = {
  page: number;
};

export const useInvoicesList = ({ page = 1 }: UseInvoicesListParams) => {
  const { merchantId } = useContext(GlobalStateContext);

  return useInvoicesListQuery({
    variables: {
      merchantId,
      limit: INVOICES_TABLE_LIMIT_BY_PAGE,
      direction: Direction.DESC,
      page,
    },
  });
};
