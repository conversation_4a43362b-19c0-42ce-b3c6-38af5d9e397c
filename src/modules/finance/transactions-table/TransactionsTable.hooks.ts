import { useReactiveVar } from '@apollo/client';
import { useContext, useEffect, useState } from 'react';
import { useTimestamp } from 'shared/components';
import { GlobalStateContext } from 'shared/hooks/state';
import { useDebouncedValue } from 'shared/hooks/utils';

import { useTransactionsQuery } from './api';
import {
  BOOK_TYPES,
  METHODS,
  TRANSACTIONS_TABLE_LIMIT_BY_PAGE,
} from './TransactionsTable.constants';
import { transactionsTableState } from './TransactionsTable.utils';

type TransactionsParams = {
  page: number;
};

export const useTransactionsData = ({ page }: TransactionsParams) => {
  const { merchantId } = useContext(GlobalStateContext);
  const tableState = useReactiveVar(transactionsTableState);
  const timestampFrom = useTimestamp(tableState.dateRange.from || new Date());
  const timestampTo = useTimestamp(tableState.dateRange.to || new Date());

  return useTransactionsQuery({
    skip: !merchantId,
    variables: {
      merchantId: merchantId ?? -1,
      dateRange: {
        timestamp_from: timestampFrom,
        timestamp_to: timestampTo,
      },
      merchantRefOrCustomerName: tableState.search,
      methods: METHODS,
      bookTypes: BOOK_TYPES,
      limit: TRANSACTIONS_TABLE_LIMIT_BY_PAGE,
      page,
    },
  });
};

export const useTransactionSearch = () => {
  const tableState = useReactiveVar(transactionsTableState);
  const [searchValue, setSearchValue] = useState(tableState.search);
  const debouncedSearch = useDebouncedValue(searchValue, 500);

  useEffect(() => {
    if (debouncedSearch !== tableState.search) {
      transactionsTableState({
        ...tableState,
        search: debouncedSearch,
        page: 1,
      });
    }
  }, [debouncedSearch, tableState]);

  return { searchValue, setSearchValue };
};
