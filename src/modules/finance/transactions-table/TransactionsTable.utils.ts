import { makeVar } from '@apollo/client';
import { startOfMonth } from 'date-fns';
import type { DateRangeType } from 'shared/components';

type TransactionsTableState = {
  page: number;
  search: string;
  dateRange: DateRangeType;
};

export const defaultTransactionsTableState: TransactionsTableState = {
  search: '',
  dateRange: {
    from: startOfMonth(new Date()),

    to: new Date(),
  },
  page: 1,
};

export const transactionsTableState = makeVar<TransactionsTableState>(
  defaultTransactionsTableState,
);
