import { TransactionBookType, TransactionMerchantMethod } from 'shared/api';

export const METHODS = [
  TransactionMerchantMethod.LOAN_ISSUED_ACTUAL,
  TransactionMerchantMethod.LOAN_MODIFIED_ACTUAL,
  TransactionMerchantMethod.LOAN_CANCELLED_ACTUAL,
  TransactionMerchantMethod.MERCHANT_PAYOUT,
  TransactionMerchantMethod.DIRECT_PAYMENT,
  TransactionMerchantMethod.DIRECT_PAYMENT_RETURNED,
];

export const BOOK_TYPES = [
  TransactionBookType.MERCHANT_BONUS_PAYABLE,
  TransactionBookType.MERCHANT_PRINCIPAL_PAYABLE,
  TransactionBookType.MERCHANT_REVERSE_KICKBACK_PAYABLE,
  TransactionBookType.MERCHANT_DIRECT_PAYMENTS_PAYABLE,
  TransactionBookType.MERCHANT_MEDIATION_FEE_PAYABLE,
];

export const TRANSACTION<PERSON>_TABLE_LIMIT_BY_PAGE = 12;
