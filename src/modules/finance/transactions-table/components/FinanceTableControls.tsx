import { useReactiveVar } from '@apollo/client';
import { Box, Icon, SimpleGrid, Text } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FiSearch } from 'react-icons/fi';
import { TextInput, useFormattedDateRange } from 'shared/components';

import { useTransactionSearch } from '../TransactionsTable.hooks';
import { transactionsTableState } from '../TransactionsTable.utils';
import { DateSelectBlock } from './DateBlock';

export const FinanceTableControls = () => {
  const { t } = useTranslation('finance');
  const { searchValue, setSearchValue } = useTransactionSearch();
  const { dateRange } = useReactiveVar(transactionsTableState);
  const { formattedDate, isSameDay } = useFormattedDateRange(dateRange);

  return (
    <>
      <SimpleGrid
        alignContent="center"
        gap={6}
        templateColumns={{ base: '1fr', lg: '1fr auto' }}
      >
        <TextInput
          data-cy="finance-search"
          flex="1"
          minW="12.5rem"
          onChange={(e) => {
            setSearchValue(e.target.value);
          }}
          placeholder={t('search')}
          startElement={<Icon as={FiSearch} boxSize={6} />}
          sx={{ pb: 0 }}
          value={searchValue}
        />
        <DateSelectBlock />
      </SimpleGrid>
      <Box>
        <Text fontSize="1.125rem" fontWeight={600}>
          {t(isSameDay ? 'same-day-trans-for' : 'trans-for')} {formattedDate}
        </Text>
      </Box>
    </>
  );
};
