import { type ChakraProps, Text } from '@chakra-ui/react';
import { type FC, memo } from 'react';

import type { TransactionsQuery } from '../api';

type TransactionClientNameProps = {
  applicationTrashed: NonNullOrUndefined<
    ArrayElement<
      NonNullOrUndefined<
        NonNullOrUndefined<TransactionsQuery['transactions']>['data']
      >
    >
  >['application_trashed'];
} & ChakraProps;

export const TransactionClientName: FC<TransactionClientNameProps> = memo(
  ({ applicationTrashed }) => {
    if (applicationTrashed?.for_private_person) {
      return (
        <Text as="span" whiteSpace="nowrap">
          {applicationTrashed.user_info
            ? [
                applicationTrashed.user_info.first_name,
                applicationTrashed.user_info.last_name,
              ].join(' ')
            : '-'}
        </Text>
      );
    }
    return applicationTrashed?.legal_person_info ? (
      <Text as="span">
        {applicationTrashed?.legal_person_info.name}{' '}
        <Text as="span" whiteSpace="nowrap">
          {applicationTrashed?.user_info
            ? `(${[
                applicationTrashed?.user_info.first_name,
                applicationTrashed?.user_info.last_name,
              ].join(' ')})`
            : ''}
        </Text>
      </Text>
    ) : (
      <Text as="span">-</Text>
    );
  },
);

TransactionClientName.displayName = 'TransactionClient';
