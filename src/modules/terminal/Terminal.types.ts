import type { ApplicationScheduleType } from 'shared/api';

export enum StoreTypes {
  ONLINE = 'online',
  PHYSICAL = 'physical',
}

export enum TerminalFormStep {
  STORE_SELECTION = 0,
  PAYMENT_PLAN_SELECTION = 1,
  PURCHASE_DETAILS = 2,
  PERSONAL_INFORMATION = 3,
  DOCUMENT_IDENTIFICATION = 4,
}

export type TerminalPaymentPlan = {
  scheduleType: ApplicationScheduleType;
  name: Nullish<string>;
  logoUrl: Nullish<string>;
  minLoanAmount: number;
  maxLoanAmount: number;
  periodLimitsHint: string;
  cashierBonusPct?: number;
};

export type TerminalCreditSettingsPeriodInfo = {
  amount: string | number;
  month: number;
};
