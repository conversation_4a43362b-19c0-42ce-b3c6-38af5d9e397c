import { useDownPaymentSettings } from 'modules/terminal/hooks/useDownPaymentSettings';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { StoreTypes } from 'modules/terminal/Terminal.types';
import { useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { ApplicationScheduleType } from 'shared/api';

/**
 * Custom hook that determines if a merchant is required to make a down payment.
 * It checks the store type, schedule type, and down payment settings.
 * Returns a boolean indicating whether a down payment is required.
 */
export const useIsRequiredMerchantDownPayment = () => {
  const { watch } = useFormContext<TerminalFormSchemaType>();

  const [storeType, scheduleType] = watch(['storeType', 'scheduleType']);
  const { downPaymentSettings } = useDownPaymentSettings();

  return useMemo(
    () =>
      storeType === StoreTypes.PHYSICAL &&
      scheduleType !== ApplicationScheduleType.SMALL_LOAN &&
      downPaymentSettings.downPaymentMinimumLoanAmount > 0,
    [downPaymentSettings.downPaymentMinimumLoanAmount, scheduleType, storeType],
  );
};
