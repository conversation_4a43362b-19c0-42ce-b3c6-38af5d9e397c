import { zodResolver } from '@hookform/resolvers/zod';
import { useDownPaymentSettings } from 'modules/terminal/hooks/useDownPaymentSettings';
import { useTerminalPaymentPlanLimitsByScheduleType } from 'modules/terminal/hooks/useTerminalPaymentPlanLimitsByScheduleType';
import { terminalFormDefaultValue } from 'modules/terminal/Terminal.constants';
import {
  TerminalFormSchema,
  type TerminalFormSchemaType,
} from 'modules/terminal/Terminal.schema';
import { useContext, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { useMerchantSettings } from 'shared/hooks/merchant';
import { GlobalStateContext } from 'shared/hooks/state';

/**
 * Custom hook for managing the terminal form.
 * @returns The form methods object.
 */
export const useTerminalForm = () => {
  const isInitialResetSkipped = useRef(false);
  const { merchantId } = useContext(GlobalStateContext);
  const downPaymentSettings = useDownPaymentSettings();
  const { settings } = useMerchantSettings();
  const getPaymentPlanAmountLimits =
    useTerminalPaymentPlanLimitsByScheduleType();

  const formMethods = useForm<TerminalFormSchemaType>({
    shouldFocusError: true,
    mode: 'onChange',
    defaultValues: terminalFormDefaultValue,
    resolver: zodResolver(
      TerminalFormSchema({
        downPaymentSettings: downPaymentSettings.downPaymentSettings,
        maxAmountIdVerificationNotRequired:
          settings?.max_amount_id_verification_not_required,
        getPaymentPlanAmountLimits,
      }),
    ),
  });

  useEffect(() => {
    if (!isInitialResetSkipped.current) {
      isInitialResetSkipped.current = true;
      return;
    }

    formMethods.reset();
  }, [formMethods, merchantId]);

  return formMethods;
};
