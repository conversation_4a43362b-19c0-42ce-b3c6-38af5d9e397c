import { useTerminalPaymentPlans } from 'modules/terminal/hooks/useTerminalPaymentPlans';
import type { TerminalPaymentPlan } from 'modules/terminal/Terminal.types';
import { useMemo } from 'react';
import type { ApplicationScheduleType } from 'shared/api';

/**
 * Custom hook to retrieve a terminal payment plan based on the provided schedule type.
 * @param scheduleType - The schedule type of the payment plan.
 * @returns The terminal payment plan matching the schedule type, or null if not found.
 */
export const useTerminalPaymentPlanByScheduleType = (
  scheduleType: Nullable<ApplicationScheduleType>,
) => {
  const paymentPlans = useTerminalPaymentPlans();

  return useMemo((): Nullable<TerminalPaymentPlan> => {
    if (!scheduleType) {
      return null;
    }

    return (
      paymentPlans.find((plan) => plan.scheduleType === scheduleType) ?? null
    );
  }, [paymentPlans, scheduleType]);
};
