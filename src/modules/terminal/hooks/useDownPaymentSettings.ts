import { useTranslation } from 'react-i18next';
import { usePricing } from 'shared/hooks/merchant';
import { getPricingSettingValue } from 'shared/utils';

const DOWN_PAYMENT_MINIMUM_LOAN_AMOUNT = 'down_payment.minimum_loan_amount';
const DOWN_PAYMENT_MINIMUM_REQUIRED_PCT = 'down_payment.minimum_required_pct';

export type DownPaymentSettings = {
  merchantDownPaymentHint: string;
  downPaymentSettings: {
    downPaymentMinimumLoanAmount: number;
    downPaymentMinimumRequiredPct: number;
  };
};

/**
 * Custom hook that retrieves down payment settings.
 * @returns An object containing the merchant down payment hint and down payment settings.
 */
export const useDownPaymentSettings = (): DownPaymentSettings => {
  const pricingSettings = usePricing([
    DOWN_PAYMENT_MINIMUM_LOAN_AMOUNT,
    DOWN_PAYMENT_MINIMUM_REQUIRED_PCT,
  ]);
  const { t } = useTranslation('terminal');

  const downPaymentMinimumLoanAmount = Number(
    getPricingSettingValue(
      DOWN_PAYMENT_MINIMUM_LOAN_AMOUNT,
      pricingSettings as Array<{ key: string; value: string }> | undefined,
    ),
  );
  const downPaymentMinimumRequiredPct = Number(
    getPricingSettingValue(
      DOWN_PAYMENT_MINIMUM_REQUIRED_PCT,
      pricingSettings as Array<{ key: string; value: string }> | undefined,
    ),
  );

  const merchantDownPaymentHint = t(
    'purchase-details.merchant-down-payment-hint',
    {
      downPaymentMinimumLoanAmount,
      downPaymentMinimumRequiredPct,
    },
  );

  return {
    merchantDownPaymentHint,
    downPaymentSettings: {
      downPaymentMinimumLoanAmount,
      downPaymentMinimumRequiredPct,
    },
  };
};
