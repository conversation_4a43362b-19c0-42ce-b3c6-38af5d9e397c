import memoize from 'lodash/memoize';
import { useTerminalPaymentPlans } from 'modules/terminal/hooks/useTerminalPaymentPlans';
import type { TerminalPaymentPlan } from 'modules/terminal/Terminal.types';
import { useCallback } from 'react';
import type { ApplicationScheduleType } from 'shared/api';

/**
 * Custom hook that returns a memoized function to get the payment plan amount limits based on the schedule type.
 * @returns A memoized function that takes a schedule type and returns the corresponding payment plan amount limits.
 */
export const useTerminalPaymentPlanLimitsByScheduleType = () => {
  const paymentPlans = useTerminalPaymentPlans();

  const getPaymentPlanAmountLimits = useCallback(
    (
      scheduleType: Nullable<ApplicationScheduleType>,
    ): Nullable<TerminalPaymentPlan> => {
      if (!scheduleType) {
        return null;
      }

      return (
        paymentPlans.find((plan) => plan.scheduleType === scheduleType) ?? null
      );
    },
    [paymentPlans],
  );

  return memoize(getPaymentPlanAmountLimits);
};
