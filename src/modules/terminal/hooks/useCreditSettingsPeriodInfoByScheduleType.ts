import type { TerminalCreditSettingsPeriodInfo } from 'modules/terminal/Terminal.types';
import { useMemo } from 'react';
import { ApplicationScheduleType, useCreditSettingsQuery } from 'shared/api';
import { useMerchantDetails } from 'shared/hooks/merchant';
import { useDebouncedValue } from 'shared/hooks/utils';

type UseCreditSettingsPeriodInfoByScheduleTypeParams = {
  scheduleType: Nullable<ApplicationScheduleType>;
  netTotal: number;
  skip?: boolean;
};

/**
 * Custom hook that retrieves credit settings period information based on the schedule type and netTotal.
 * @param {UseCreditSettingsPeriodInfoByScheduleTypeParams} params - The parameters for the hook.
 * @returns {Object} An object containing the loading state and the credit settings period information.
 * @property {boolean} isLoading - Indicates whether the data is currently being loaded.
 * @property {Nullable<Array<TerminalCreditSettingsPeriodInfo>>} creditSettingsPeriodInfo - The credit settings period information, or null if it is not available.
 */
export const useCreditSettingsPeriodInfoByScheduleType = ({
  scheduleType,
  netTotal,
  skip,
}: UseCreditSettingsPeriodInfoByScheduleTypeParams) => {
  const { data: merchant } = useMerchantDetails();

  const debouncedNetTotal = useDebouncedValue(netTotal);

  const { data: creditSettingsData, loading } = useCreditSettingsQuery({
    skip,
    variables: {
      scheduleType,
      merchantId: merchant?.merchant?.id,
      netTotal: debouncedNetTotal,
    },
  });

  const creditSettingsPeriodInfo: Nullable<
    Array<TerminalCreditSettingsPeriodInfo>
  > = useMemo(() => {
    if (!debouncedNetTotal) {
      return null;
    }

    if (scheduleType === ApplicationScheduleType.PAY_LATER) {
      return [{ amount: debouncedNetTotal.toString(), month: 1 }];
    }

    if (scheduleType === ApplicationScheduleType.ESTO_PAY) {
      return null;
    }

    if (!creditSettingsData?.credit_settings?.length) {
      return null;
    }

    return creditSettingsData.credit_settings.reduce<
      Array<{
        month: number;
        amount: string;
      }>
    >((acc, settings) => {
      if (!settings) {
        return acc;
      }
      const { monthly_payment, month } = settings;

      const isEstoXFixed =
        scheduleType === ApplicationScheduleType.ESTO_X &&
        typeof merchant?.merchant?.campaign
          ?.converting_schedule_fixed_contract_fee === 'number';
      const isRegularFixed =
        scheduleType === ApplicationScheduleType.REGULAR &&
        [
          merchant?.merchant?.campaign?.fixed_annual_pct_rate,
          merchant?.merchant?.campaign?.fixed_management_fee,
          merchant?.merchant?.campaign?.fixed_contract_fee,
        ].every((fee) => typeof fee === 'number');
      const isFixed = isEstoXFixed || isRegularFixed;
      let resultAmount = `${Math.ceil(monthly_payment)}`;

      if (!isFixed) {
        const upperBound = Math.ceil(monthly_payment);
        const lowerBound = Math.ceil(
          debouncedNetTotal / month + debouncedNetTotal * 0.005,
        );
        resultAmount = `${lowerBound}-${upperBound}`;
      }

      acc.push({
        month,
        amount: resultAmount,
      });

      return acc;
    }, []);
  }, [
    debouncedNetTotal,
    scheduleType,
    creditSettingsData?.credit_settings,
    merchant?.merchant?.campaign?.converting_schedule_fixed_contract_fee,
    merchant?.merchant?.campaign?.fixed_annual_pct_rate,
    merchant?.merchant?.campaign?.fixed_management_fee,
    merchant?.merchant?.campaign?.fixed_contract_fee,
  ]);

  return {
    isLoading: loading,
    creditSettingsPeriodInfo: loading ? null : creditSettingsPeriodInfo,
  };
};
