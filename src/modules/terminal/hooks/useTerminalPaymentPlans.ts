import { useScheduleTypesSettingsQuery } from 'modules/terminal/api';
import type { TerminalPaymentPlan } from 'modules/terminal/Terminal.types';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationScheduleType } from 'shared/api';
import { LocizeNamespaces } from 'shared/constants/localization-keys';
import { useMerchantDetails } from 'shared/hooks/merchant';

/**
 * Returns an array of available payment plans based on merchant details and schedule type settings.
 * Each plan contains information such as schedule type, name, logo URL, minimum loan amount, and maximum loan amount.
 * If the required  is not available, an empty array will be returned.
 *
 * @returns {Array<TerminalPaymentPlan>} An array of available payment plans
 */
export const useTerminalPaymentPlans = (): Array<TerminalPaymentPlan> => {
  const { t } = useTranslation(LocizeNamespaces.TERMINAL);

  const { data: merchantDetails } = useMerchantDetails();
  const { data: scheduleTypesSettings } = useScheduleTypesSettingsQuery();

  return useMemo(() => {
    if (
      !merchantDetails?.merchant?.campaign ||
      !merchantDetails?.merchant?.settings ||
      !scheduleTypesSettings?.schedule_types_settings
    ) {
      return [];
    }

    const { campaign, settings } = merchantDetails.merchant;
    const { schedule_types_settings } = scheduleTypesSettings;

    const plans: Array<TerminalPaymentPlan> = [];

    if (
      campaign.regular_hp_enabled &&
      schedule_types_settings?.REGULAR?.enabled
    ) {
      const from = settings.min_months_period;
      const to = settings.max_months_period;

      plans.push({
        scheduleType: ApplicationScheduleType.REGULAR,
        name: t('payment-plan.regular'),
        logoUrl: merchantDetails.merchant.logo_path,
        minLoanAmount: settings.net_total_min,
        maxLoanAmount: settings.net_total_max,
        periodLimitsHint: t('payment-plan.period', {
          period: from === to ? from : `${from}-${to}`,
          count: to,
        }),
        cashierBonusPct: settings.cashier_bonus_pct,
      });
    }

    if (
      campaign.converting_schedule_enabled &&
      schedule_types_settings?.ESTO_X?.enabled
    ) {
      plans.push({
        scheduleType: ApplicationScheduleType.ESTO_X,
        name: campaign.converting_schedule_name,
        logoUrl: campaign.converting_schedule_logo_url,
        minLoanAmount: campaign.converting_schedule_net_total_min,
        maxLoanAmount: campaign.converting_schedule_net_total_max,
        periodLimitsHint: t('payment-plan.period', {
          period: campaign?.converting_schedule_months ?? 0,
          count: campaign?.converting_schedule_months ?? 0,
        }),
      });
    }

    if (
      campaign.pay_later_enabled &&
      schedule_types_settings?.PAY_LATER?.enabled
    ) {
      plans.push({
        scheduleType: ApplicationScheduleType.PAY_LATER,
        name: campaign.pay_later_name,
        logoUrl: campaign.pay_later_logo_url,
        minLoanAmount: campaign.pay_later_net_total_min,
        maxLoanAmount: campaign.pay_later_net_total_max,
        periodLimitsHint: t('payment-plan.period', {
          period: 1,
          count: 1,
        }),
      });
    }

    if (
      settings?.can_create_small_loan &&
      schedule_types_settings?.SMALL_LOAN?.enabled
    ) {
      const from = schedule_types_settings.SMALL_LOAN.possible_periods[0] ?? 0;
      const to =
        schedule_types_settings.SMALL_LOAN.possible_periods[
          schedule_types_settings.SMALL_LOAN.possible_periods.length - 1
        ] ?? 0;

      plans.push({
        scheduleType: ApplicationScheduleType.SMALL_LOAN,
        name: t('payment-plan.small-loan'),
        logoUrl: merchantDetails.merchant.logo_path,
        minLoanAmount: schedule_types_settings.SMALL_LOAN.min_loan_amount,
        maxLoanAmount: schedule_types_settings.SMALL_LOAN.max_loan_amount,
        periodLimitsHint: t('payment-plan.period', {
          period: from === to ? from : `${from}-${to}`,
          count: to,
        }),
      });
    }

    return plans;
  }, [merchantDetails, scheduleTypesSettings, t]);
};
