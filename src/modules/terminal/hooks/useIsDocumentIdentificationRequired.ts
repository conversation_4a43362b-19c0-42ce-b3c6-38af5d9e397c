import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { StoreTypes } from 'modules/terminal/Terminal.types';
import { useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { ApplicationScheduleType } from 'shared/api';
import { useMerchantSettings } from 'shared/hooks/merchant';

/**
 * Custom hook that checks if document identification is required based on the store type and amount.
 * @returns {boolean} Returns true if document identification is required, false otherwise.
 */
export const useIsDocumentIdentificationRequired = () => {
  const { settings } = useMerchantSettings();

  const { watch } = useFormContext<TerminalFormSchemaType>();
  const [storeType, amount, scheduleType] = watch([
    'storeType',
    'amount',
    'scheduleType',
  ]);

  return useMemo(
    () =>
      storeType === StoreTypes.PHYSICAL &&
      scheduleType !== ApplicationScheduleType.SMALL_LOAN &&
      !!settings &&
      !!amount &&
      amount >= settings.max_amount_id_verification_not_required,
    [amount, scheduleType, settings, storeType],
  );
};
