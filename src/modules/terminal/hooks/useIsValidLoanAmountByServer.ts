import { useFormContext } from 'react-hook-form';
import { ApplicationScheduleType } from 'shared/api';

import { useIsValidLoanAmountByScheduleType } from '../Terminal.hooks';
import type { TerminalFormSchemaType } from '../Terminal.schema';
import { useCreditSettingsPeriodInfoByScheduleType } from './useCreditSettingsPeriodInfoByScheduleType';
import { useIsRequiredMerchantDownPayment } from './useIsRequiredMerchantDownPayment';

export const useIsValidLoanAmountByServer = () => {
  const isRequiredMerchantDownPayment = useIsRequiredMerchantDownPayment();

  const { watch } = useFormContext<TerminalFormSchemaType>();

  const [selectedScheduleType, amount, merchantDownPayment] = watch([
    'scheduleType',
    'amount',
    'merchantDownPayment',
  ]);

  const netTotal = isRequiredMerchantDownPayment
    ? amount - merchantDownPayment
    : amount;

  const isValidAmount = useIsValidLoanAmountByScheduleType({
    amount,
    scheduleType: selectedScheduleType,
  });

  const { creditSettingsPeriodInfo, isLoading } =
    useCreditSettingsPeriodInfoByScheduleType({
      scheduleType: selectedScheduleType,
      skip:
        !selectedScheduleType ||
        !isValidAmount ||
        selectedScheduleType === ApplicationScheduleType.PAY_LATER,
      netTotal,
    });

  return {
    isValidLoanAmountByServer:
      selectedScheduleType === ApplicationScheduleType.PAY_LATER ||
      !!creditSettingsPeriodInfo?.length,
    isLoading,
  };
};
