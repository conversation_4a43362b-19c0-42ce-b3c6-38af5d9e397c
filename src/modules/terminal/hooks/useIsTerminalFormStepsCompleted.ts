import { useDownPaymentSettings } from 'modules/terminal/hooks/useDownPaymentSettings';
import { useIsDocumentIdentificationRequired } from 'modules/terminal/hooks/useIsDocumentIdentificationRequired';
import { useIsRequiredMerchantDownPayment } from 'modules/terminal/hooks/useIsRequiredMerchantDownPayment';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { StoreTypes, TerminalFormStep } from 'modules/terminal/Terminal.types';
import { useFormContext } from 'react-hook-form';

import { useIsValidLoanAmountByServer } from './useIsValidLoanAmountByServer';

/**
 * Checks if all the steps in the terminal form are completed.
 * @param steps - An array of TerminalFormStep values representing the steps in the form.
 * @returns A boolean indicating whether all the steps are completed.
 */
export const useIsTerminalFormStepsCompleted = (
  steps: Array<TerminalFormStep>,
) => {
  const {
    watch,
    formState: { dirtyFields, errors },
  } = useFormContext<TerminalFormSchemaType>();
  const { downPaymentSettings } = useDownPaymentSettings();
  const isRequiredMerchantDownPayment = useIsRequiredMerchantDownPayment();
  const { storeType, storeId, scheduleType, amount } = watch();
  const { isValidLoanAmountByServer } = useIsValidLoanAmountByServer();

  const isDocumentIdentificationRequired =
    useIsDocumentIdentificationRequired();

  return steps.every((step) => {
    switch (step) {
      case TerminalFormStep.STORE_SELECTION: {
        if (storeType === StoreTypes.ONLINE && !storeId) {
          return true;
        }

        return !!storeId;
      }
      case TerminalFormStep.PAYMENT_PLAN_SELECTION: {
        return !!scheduleType;
      }
      case TerminalFormStep.PURCHASE_DETAILS: {
        if (!isValidLoanAmountByServer) {
          return false;
        }

        const isReferenceValid = !!dirtyFields.reference && !errors.reference;
        const isAmountValid = !!dirtyFields.amount && !errors.amount;

        if (!isRequiredMerchantDownPayment) {
          return isAmountValid && isReferenceValid;
        }

        const isMerchantDownPaymentValid =
          (amount <= downPaymentSettings.downPaymentMinimumLoanAmount &&
            !dirtyFields.merchantDownPayment) ||
          (!!dirtyFields.merchantDownPayment && !errors.merchantDownPayment);

        return isAmountValid && isMerchantDownPaymentValid && isReferenceValid;
      }
      case TerminalFormStep.PERSONAL_INFORMATION: {
        if (storeType === StoreTypes.PHYSICAL) {
          return true;
        }

        const isFirstNameValid = !!dirtyFields.firstName && !errors.firstName;
        const isLastNameValid = !!dirtyFields.lastName && !errors.lastName;
        const isPersonalInfoEmailValid =
          !!dirtyFields.personalInfoEmail && !errors.personalInfoEmail;

        return isFirstNameValid && isLastNameValid && isPersonalInfoEmailValid;
      }
      case TerminalFormStep.DOCUMENT_IDENTIFICATION: {
        if (!isDocumentIdentificationRequired) {
          return true;
        }

        const isDocumentNrValid =
          !!dirtyFields.documentNr && !errors.documentNr;
        const isPinValid = !!dirtyFields.pin && !errors.pin;

        return isDocumentNrValid && isPinValid;
      }
      default: {
        return false;
      }
    }
  });
};
