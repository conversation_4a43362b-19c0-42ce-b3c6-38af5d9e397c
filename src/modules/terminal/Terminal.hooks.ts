import { useTerminalPaymentPlanByScheduleType } from 'modules/terminal/hooks';
import type { ApplicationScheduleType } from 'shared/api';
import { z } from 'zod';

export const useIsValidLoanAmountByScheduleType = ({
  amount,
  scheduleType,
}: {
  scheduleType: Nullable<ApplicationScheduleType>;
  amount: number;
}) => {
  const selectedPaymentPlan =
    useTerminalPaymentPlanByScheduleType(scheduleType);

  if (!scheduleType || !selectedPaymentPlan) {
    return false;
  }

  const { minLoanAmount, maxLoanAmount } = selectedPaymentPlan;

  try {
    z.number().min(minLoanAmount).max(maxLoanAmount).parse(amount);

    return true;
  } catch {
    return false;
  }
};
