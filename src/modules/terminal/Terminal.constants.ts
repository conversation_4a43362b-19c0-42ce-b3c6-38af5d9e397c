import { availableLanguages } from 'shared/lib';

import type { TerminalFormSchemaType } from './Terminal.schema';
import { StoreTypes } from './Terminal.types';

export const terminalFormDefaultValue: TerminalFormSchemaType = {
  isPracticeMode: false,
  storeType: StoreTypes.PHYSICAL,
  storeId: null,
  amount: 0,
  merchantDownPayment: 0,
  reference: '',
  scheduleType: null,
  firstName: '',
  language: availableLanguages[0],
  lastName: '',
  personalInfoEmail: '',
  personalInfoPhone: '',
  documentNr: '',
  documentType: null,
  pin: '',
  userMessageSentType: null,
};
