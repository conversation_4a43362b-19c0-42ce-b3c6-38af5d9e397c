import { makeVar } from '@apollo/client';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { StoreTypes } from 'modules/terminal/Terminal.types';
import {
  type ApplicationScheduleType,
  CashierApplicationSpecialSettings,
  type UserDocumentType,
} from 'shared/api';
import { reactEnv } from 'shared/lib';
import { getAbbrFromLang, getFormLanguageWithFallback } from 'shared/utils';

import { terminalFormDefaultValue } from './Terminal.constants';

type SerializeTerminalApplicationDataParams = {
  merchantId: number;
  isRequiredMerchantDownPayment: boolean;
} & TerminalFormSchemaType;

export const serializeTerminalApplicationData = ({
  storeType,
  storeId,
  amount,
  scheduleType,
  merchantDownPayment,
  reference,
  firstName,
  lastName,
  personalInfoEmail,
  personalInfoPhone,
  documentType,
  documentNr,
  language,
  pin,
  isPracticeMode,
  isRequiredMerchantDownPayment,
  merchantId,
}: SerializeTerminalApplicationDataParams) => ({
  isTest: isPracticeMode,
  merchantId,
  fromRetail: storeType === StoreTypes.PHYSICAL,
  storeId,
  specialSettings: CashierApplicationSpecialSettings.NONE,
  reference,
  scheduleType: scheduleType as unknown as ApplicationScheduleType,
  documentType: documentType as unknown as UserDocumentType,
  documentNr,
  pin,
  firstName,
  lastName,
  email: personalInfoEmail,
  phone: personalInfoPhone.trim(),
  languageAbbr: getAbbrFromLang(
    getFormLanguageWithFallback(language, reactEnv),
  ),
  amount: isRequiredMerchantDownPayment
    ? +(amount - merchantDownPayment).toFixed(2)
    : amount,
  merchantDownPayment,
});

type GenerateAmountHintParams = {
  max: string | number;
  min: string | number;
  currency?: string;
};
/**
 * Generates an amount hint string.
 *
 * @param {Object} params - The parameters for generating the amount hints.
 * @param {String} [params.currency='€'] - The currency symbol to be used.
 * @param {Number} params.min - The minimum amount.
 * @param {Number} params.max - The maximum amount.
 * @returns {String} - The generated amount hint string.
 */
export const generateAmountHint = ({
  currency = '€',
  min,
  max,
}: GenerateAmountHintParams): string => {
  return `${min} - ${max} ${currency}`;
};

export const terminalFormState = {
  storeId: makeVar(terminalFormDefaultValue.storeId),
};
