import type { DownPaymentSettings } from 'modules/terminal/hooks';
import {
  StoreTypes,
  type TerminalPaymentPlan,
} from 'modules/terminal/Terminal.types';
import {
  ApplicationScheduleType,
  UserDocumentType,
  UserMessageSentType,
} from 'shared/api';
import type { AvailableLanguage } from 'shared/types';
import { z } from 'zod';

type TerminalFormSchemaConfig = {
  downPaymentSettings: DownPaymentSettings['downPaymentSettings'];
  maxAmountIdVerificationNotRequired?: number;
  getPaymentPlanAmountLimits: (
    scheduleType: Nullable<ApplicationScheduleType>,
  ) => Nullable<TerminalPaymentPlan>;
};

export const TerminalFormSchema = ({
  getPaymentPlanAmountLimits,
  ...config
}: TerminalFormSchemaConfig) =>
  z
    .object({
      isPracticeMode: z.boolean(),

      storeType: z.nativeEnum(StoreTypes),
      storeId: z.number().nullable(),

      amount: z.number(),
      merchantDownPayment: z.number(),
      reference: z.string().min(1, 'required'),

      scheduleType: z.nativeEnum(ApplicationScheduleType).nullable(),

      firstName: z.string(),
      lastName: z.string(),
      personalInfoEmail: z.string(),
      personalInfoPhone: z.string(),
      language: z.custom<AvailableLanguage>(),

      documentType: z.nativeEnum(UserDocumentType).nullable(),
      documentNr: z.string(),
      pin: z.string(),

      userMessageSentType: z.nativeEnum(UserMessageSentType).nullable(),
    })
    .refine(
      ({ storeType, storeId }) =>
        (storeType === StoreTypes.ONLINE && !storeId) ||
        (storeType === StoreTypes.PHYSICAL && !!storeId),
    )
    // Purchase Details validation
    .superRefine(
      ({ storeType, scheduleType, amount, merchantDownPayment }, ctx) => {
        const amountLimits = getPaymentPlanAmountLimits(scheduleType);
        if (!amountLimits) {
          return;
        }
        const { minLoanAmount, maxLoanAmount } = amountLimits;

        try {
          z.number().min(minLoanAmount).parse(amount);
        } catch {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'min-value',
            path: ['amount'],
          });
        }
        try {
          z.number().max(maxLoanAmount).parse(amount);
        } catch {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'max-value',
            path: ['amount'],
          });
        }

        const isRequiredMerchantDownPayment =
          storeType === StoreTypes.PHYSICAL &&
          scheduleType !== ApplicationScheduleType.SMALL_LOAN &&
          config.downPaymentSettings.downPaymentMinimumLoanAmount > 0;

        if (!isRequiredMerchantDownPayment) {
          return;
        }

        try {
          z.number()
            .max(+(amount - minLoanAmount).toFixed(2))
            .parse(merchantDownPayment);
        } catch {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'max-value',
            path: ['merchantDownPayment'],
          });
        }
        if (amount <= config.downPaymentSettings.downPaymentMinimumLoanAmount) {
          return;
        }

        const minimalMerchantDownPayment =
          amount > config.downPaymentSettings.downPaymentMinimumLoanAmount
            ? (amount *
                config.downPaymentSettings.downPaymentMinimumRequiredPct) /
              100
            : 0;

        if (merchantDownPayment < minimalMerchantDownPayment) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'min-value',
            path: ['merchantDownPayment'],
          });
        }
      },
    )
    // Personal Information validation
    .superRefine(({ storeType, personalInfoEmail }, ctx) => {
      if (storeType === StoreTypes.PHYSICAL) {
        return;
      }

      if (!personalInfoEmail) {
        return;
      }

      try {
        z.string().min(1).email().parse(personalInfoEmail);
      } catch {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'invalid-format',
          path: ['personalInfoEmail'],
        });
      }
    })
    // Document Identification validation
    .superRefine(
      (
        { storeType, scheduleType, amount, documentType, documentNr, pin },
        ctx,
      ) => {
        const isIdVerificationRequired =
          storeType === StoreTypes.PHYSICAL &&
          !!config.maxAmountIdVerificationNotRequired &&
          scheduleType !== ApplicationScheduleType.SMALL_LOAN &&
          !!amount &&
          amount >= config.maxAmountIdVerificationNotRequired;

        if (!isIdVerificationRequired) {
          return;
        }

        try {
          z.string().min(1).parse(documentNr);
        } catch {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'required',
            path: ['documentNr'],
          });
        }

        try {
          z.string().min(1).parse(pin);
        } catch {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'required',
            path: ['pin'],
          });
        }

        try {
          z.nativeEnum(UserDocumentType).parse(documentType);
        } catch {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'required',
            path: ['documentType'],
          });
        }
      },
    );

export type TerminalFormSchemaType = z.infer<
  ReturnType<typeof TerminalFormSchema>
>;
