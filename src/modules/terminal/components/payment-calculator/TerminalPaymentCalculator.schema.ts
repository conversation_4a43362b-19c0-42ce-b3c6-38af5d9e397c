import type { TerminalPaymentPlan } from 'modules/terminal/Terminal.types';
import { ApplicationScheduleType } from 'shared/api';
import { z } from 'zod';

export const TerminalPaymentCalculatorSchema = ({
  getPaymentPlanAmountLimits,
}: {
  getPaymentPlanAmountLimits: (
    scheduleType: Nullable<ApplicationScheduleType>,
  ) => Nullable<TerminalPaymentPlan>;
}) =>
  z
    .object({
      amount: z.number(),
      scheduleType: z.nativeEnum(ApplicationScheduleType).nullable(),
    })
    .superRefine(({ scheduleType, amount }, ctx) => {
      const amountLimits = getPaymentPlanAmountLimits(scheduleType);

      if (!amountLimits) {
        return;
      }
      const { minLoanAmount, maxLoanAmount } = amountLimits;

      try {
        z.number().min(minLoanAmount).parse(amount);
      } catch {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'min-value',
          path: ['amount'],
        });
      }
      try {
        z.number().max(maxLoanAmount).parse(amount);
      } catch {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'max-value',
          path: ['amount'],
        });
      }
    });

export type TerminalPaymentCalculatorSchemaType = z.infer<
  ReturnType<typeof TerminalPaymentCalculatorSchema>
>;
