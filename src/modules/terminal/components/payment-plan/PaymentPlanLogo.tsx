import { Avatar } from '@chakra-ui/react';

type PaymentPlanLogoProps = {
  isPracticeMode?: boolean;
  logoUrl: Nullish<string>;
  name: Nullish<string>;
  isSelected?: boolean;
};

export const PaymentPlanLogo = ({
  isPracticeMode,
  logoUrl,
  name,
  isSelected,
}: PaymentPlanLogoProps): JSX.Element => (
  <Avatar
    bg={
      isSelected
        ? isPracticeMode
          ? 'neutral.900'
          : 'primary.600'
        : 'neutral.100'
    }
    boxSize={9}
    color={isSelected ? 'white' : 'neutral.800'}
    css={{
      '& > img': {
        objectFit: 'contain',
        backgroundColor: 'white',
        filter: isPracticeMode ? 'grayscale(1)' : undefined,
      },
      '& > .chakra-avatar__initials': {
        fontSize: '18px',
      },
    }}
    flexShrink={0}
    getInitials={(name) => name[0]}
    name={name ?? undefined}
    src={logoUrl ?? undefined}
  />
);
