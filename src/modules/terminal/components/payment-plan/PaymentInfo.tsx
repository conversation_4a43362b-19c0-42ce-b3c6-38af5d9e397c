import { Box, Text } from '@chakra-ui/react';
import type { TerminalCreditSettingsPeriodInfo } from 'modules/terminal/Terminal.types';
import { useTranslation } from 'react-i18next';
import { ApplicationScheduleType } from 'shared/api';

export type PaymentInfoProps = {
  scheduleType: ApplicationScheduleType;
  isDesktop?: boolean;
  isPracticeMode?: boolean;
  creditSettingsPeriodInfo: Nullable<Array<TerminalCreditSettingsPeriodInfo>>;
};

export const PaymentInfo = ({
  scheduleType,
  isDesktop,
  creditSettingsPeriodInfo,
  isPracticeMode,
}: PaymentInfoProps) => {
  const { t } = useTranslation('terminal');

  const headerColor = isDesktop
    ? isPracticeMode
      ? 'neutral.900'
      : 'primary.800'
    : 'neutral.400';
  const textColor = isDesktop ? 'black' : 'white';

  if (scheduleType === ApplicationScheduleType.ESTO_PAY) {
    return null;
  }

  if (!creditSettingsPeriodInfo) {
    return null;
  }

  return (
    <Box display="flex" justifyContent="space-between" textAlign="right">
      <Box mr={isDesktop ? 4 : 3}>
        <Text color={headerColor} mb={1} textStyle="caption">
          {t('payment-plan.popover.month')}
        </Text>
        {creditSettingsPeriodInfo.map(({ month }) => (
          <Text color={textColor} key={month} textStyle="body2">
            {month}
          </Text>
        ))}
      </Box>
      <Box>
        <Text color={headerColor} mb={1} textStyle="caption">
          {t('payment-plan.popover.payment')}
        </Text>
        {creditSettingsPeriodInfo.map(({ amount }) => (
          <Text color={textColor} key={amount} textStyle="body2">
            {amount}
          </Text>
        ))}
      </Box>
    </Box>
  );
};
