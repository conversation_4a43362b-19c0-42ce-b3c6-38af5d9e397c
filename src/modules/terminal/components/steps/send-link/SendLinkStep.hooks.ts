import { SEND_LINK_TYPE_OPTIONS } from 'modules/terminal/components/steps/send-link/SendLinkStep.constants';
import {
  useIsDocumentIdentificationRequired,
  useIsRequiredMerchantDownPayment,
  useIsTerminalFormStepsCompleted,
} from 'modules/terminal/hooks';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { StoreTypes, TerminalFormStep } from 'modules/terminal/Terminal.types';
import { serializeTerminalApplicationData } from 'modules/terminal/Terminal.utils';
import { useContext, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import {
  useCashierApplicationMutation,
  UserMessageSentType,
  useSendPurchaseUrlMutation,
} from 'shared/api';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { TerminalApplicationRoute } from 'shared/constants/routes';
import { useHandleGenericError, useShowError } from 'shared/hooks/alerts';
import { GlobalStateContext } from 'shared/hooks/state';
import { useCopyToClipboard } from 'shared/hooks/utility';
import { ApplicationLinkAction } from 'shared/types';

export const useSendLinkStep = () => {
  const {
    watch,
    setValue,
    control,
    formState: { isValid: isValidTerminalForm, errors, dirtyFields },
    handleSubmit,
  } = useFormContext<TerminalFormSchemaType>();

  const [storeType, isPracticeMode, userMessageSentType] = watch([
    'storeType',
    'isPracticeMode',
    'userMessageSentType',
  ]);
  const isDocumentIdentificationRequired =
    useIsDocumentIdentificationRequired();
  const isVisible = useIsTerminalFormStepsCompleted([
    TerminalFormStep.STORE_SELECTION,
    TerminalFormStep.PURCHASE_DETAILS,
    TerminalFormStep.PAYMENT_PLAN_SELECTION,
    TerminalFormStep.DOCUMENT_IDENTIFICATION,
  ]);
  const isOnlineStore = storeType === StoreTypes.ONLINE;
  const stepNo = isDocumentIdentificationRequired ? 6 : 5;

  const colorScheme = isPracticeMode
    ? ColorSchemes.NEUTRAL
    : ColorSchemes.PRIMARY;
  const sendLinkTypeOptions = isOnlineStore
    ? [...SEND_LINK_TYPE_OPTIONS].reverse()
    : SEND_LINK_TYPE_OPTIONS;
  const isValidSendLinkBlock = (() => {
    if (userMessageSentType === UserMessageSentType.EMAIL) {
      return !!dirtyFields.personalInfoEmail && !errors.personalInfoEmail;
    }

    return !!dirtyFields.personalInfoPhone && !errors.personalInfoPhone;
  })();

  useEffect(() => {
    if (isVisible && userMessageSentType == null) {
      setValue(
        'userMessageSentType',
        isOnlineStore ? UserMessageSentType.EMAIL : UserMessageSentType.SMS,
      );
    }
  }, [isOnlineStore, isVisible, userMessageSentType, setValue]);

  return {
    isVisible,
    stepNo,
    colorScheme,
    sendLinkTypeOptions,
    isValidTerminalForm,
    handleSubmit,
    control,
    userMessageSentType,
    isValidSendLinkBlock,
  };
};

export const useSendLinkStepHandlers = () => {
  const { merchantId } = useContext(GlobalStateContext);
  const { getValues, reset } = useFormContext<TerminalFormSchemaType>();
  const [submittedAction, setSubmittedAction] =
    useState<ApplicationLinkAction | null>(null);
  const handleGenericError = useHandleGenericError();
  const [cashierApplicationMutation, { loading: isApplicationCreateLoading }] =
    useCashierApplicationMutation();
  const [sendPurchaseUrlMutation, { loading: isSendPurchaseUrlLoading }] =
    useSendPurchaseUrlMutation();
  const [, copy] = useCopyToClipboard();
  const navigate = useNavigate();
  const showError = useShowError();
  const isRequiredMerchantDownPayment = useIsRequiredMerchantDownPayment();
  const handleTerminalApplicationPageNavigateAndResetForm = (
    applicationId: number,
    action: ApplicationLinkAction,
  ) => {
    reset();
    navigate(TerminalApplicationRoute.format({ action, applicationId }));
  };

  const handleCreateApplication = async () => {
    const formValues = getValues();

    if (!merchantId) {
      showError('Merchant ID not found');
      throw new Error('Merchant ID not found');
    }

    if (formValues.scheduleType == null) {
      showError('Payment plan not found');
      throw new Error('Payment plan found');
    }

    return await cashierApplicationMutation({
      variables: serializeTerminalApplicationData({
        ...formValues,
        isRequiredMerchantDownPayment,
        merchantId,
      }),
      onError: (error: Error) => {
        handleGenericError(error);
      },
    });
  };

  const handleSendApplicationLink = async () => {
    setSubmittedAction(ApplicationLinkAction.SendLink);
    try {
      const result = await handleCreateApplication();
      if (!result?.data?.application) {
        return;
      }

      const { personalInfoEmail, personalInfoPhone, userMessageSentType } =
        getValues();

      await sendPurchaseUrlMutation({
        variables:
          userMessageSentType === UserMessageSentType.EMAIL
            ? {
                applicationId: result?.data?.application?.id,
                type: userMessageSentType,
                email: personalInfoEmail,
              }
            : {
                applicationId: result?.data?.application?.id,
                type: userMessageSentType,
                phone: personalInfoPhone.trim(),
              },
      });

      handleTerminalApplicationPageNavigateAndResetForm(
        result.data.application.id,
        ApplicationLinkAction.SendLink,
      );
    } finally {
      setSubmittedAction(null);
    }
  };

  const handleCopyApplicationLink = async () => {
    setSubmittedAction(ApplicationLinkAction.CopyLink);
    try {
      const result = await handleCreateApplication();

      if (!result?.data?.application) {
        return;
      }

      await copy(result.data.application.purchase_url);

      handleTerminalApplicationPageNavigateAndResetForm(
        result.data.application.id,
        ApplicationLinkAction.CopyLink,
      );
    } finally {
      setSubmittedAction(null);
    }
  };

  const handleOpenInNewTab = async () => {
    setSubmittedAction(ApplicationLinkAction.OpenInNewTab);
    try {
      const result = await handleCreateApplication();

      if (!result?.data?.application) {
        return;
      }

      window.open(result.data.application.purchase_url, '_blank');

      handleTerminalApplicationPageNavigateAndResetForm(
        result.data.application.id,
        ApplicationLinkAction.OpenInNewTab,
      );
    } finally {
      setSubmittedAction(null);
    }
  };

  const handleGenerateQR = async () => {
    setSubmittedAction(ApplicationLinkAction.GenerateQR);
    try {
      const result = await handleCreateApplication();

      if (!result?.data?.application) {
        return;
      }

      handleTerminalApplicationPageNavigateAndResetForm(
        result.data.application.id,
        ApplicationLinkAction.GenerateQR,
      );
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
    } finally {
      setSubmittedAction(null);
    }
  };

  return {
    submittedAction,
    handleSendApplicationLink,
    handleCopyApplicationLink,
    handleOpenInNewTab,
    isLoading: isApplicationCreateLoading || isSendPurchaseUrlLoading,
    handleGenerateQR,
  };
};
