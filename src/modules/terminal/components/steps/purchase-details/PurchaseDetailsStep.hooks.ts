import {
  useDownPaymentSettings,
  useIsRequiredMerchantDownPayment,
  useIsTerminalFormStepsCompleted,
  useIsValidLoanAmountByServer,
  useTerminalPaymentPlanByScheduleType,
} from 'modules/terminal/hooks';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { TerminalFormStep } from 'modules/terminal/Terminal.types';
import { generateAmountHint } from 'modules/terminal/Terminal.utils';
import { useEffect, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';

export const usePurchaseDetailsStep = () => {
  const isStepCompleted = useIsTerminalFormStepsCompleted([
    TerminalFormStep.PURCHASE_DETAILS,
  ]);
  const isVisible = useIsTerminalFormStepsCompleted([
    TerminalFormStep.STORE_SELECTION,
    TerminalFormStep.PAYMENT_PLAN_SELECTION,
  ]);
  const { merchantDownPaymentHint } = useDownPaymentSettings();
  const isRequiredMerchantDownPayment = useIsRequiredMerchantDownPayment();
  const { isValidLoanAmountByServer } = useIsValidLoanAmountByServer();
  const {
    watch,
    control,
    formState: { isSubmitting, errors },
    register,
    setValue,
  } = useFormContext<TerminalFormSchemaType>();

  const [storeType, merchantDownPayment, amount, scheduleType] = watch([
    'storeType',
    'merchantDownPayment',
    'amount',
    'scheduleType',
  ]);
  const selectedPaymentPlanSettings =
    useTerminalPaymentPlanByScheduleType(scheduleType);

  const amountHint = useMemo(() => {
    if (!selectedPaymentPlanSettings) {
      return;
    }

    return generateAmountHint({
      min: selectedPaymentPlanSettings.minLoanAmount,
      max: selectedPaymentPlanSettings.maxLoanAmount,
    });
  }, [selectedPaymentPlanSettings]);

  useEffect(() => {
    if (merchantDownPayment) {
      setValue('merchantDownPayment', merchantDownPayment, {
        shouldValidate: true,
      });
    }
  }, [amount, merchantDownPayment, setValue]);

  // reset merchant down payment if it's not required
  useEffect(() => {
    if (!isRequiredMerchantDownPayment) {
      setValue('merchantDownPayment', 0);
    }
  }, [isRequiredMerchantDownPayment, setValue]);

  return {
    control,
    register,
    isSubmitting,
    errors,
    merchantDownPaymentHint,
    amountHint,
    isRequiredMerchantDownPayment,
    storeType,
    isStepCompleted,
    isVisible,
    isValidLoanAmountByServer,
  };
};
