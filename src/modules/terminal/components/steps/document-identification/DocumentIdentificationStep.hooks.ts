import {
  useIsDocumentIdentificationRequired,
  useIsTerminalFormStepsCompleted,
} from 'modules/terminal/hooks';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { TerminalFormStep } from 'modules/terminal/Terminal.types';
import { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { UserDocumentType } from 'shared/api';
import { useMerchantSettings } from 'shared/hooks/merchant';

const useIsDocumentIdentificationStepVisible = () => {
  const isPreviousStepsCompleted = useIsTerminalFormStepsCompleted([
    TerminalFormStep.STORE_SELECTION,
    TerminalFormStep.PURCHASE_DETAILS,
    TerminalFormStep.PAYMENT_PLAN_SELECTION,
  ]);

  const isDocumentIdentificationRequired =
    useIsDocumentIdentificationRequired();

  return isPreviousStepsCompleted && isDocumentIdentificationRequired;
};

export const useDocumentIdentificationStep = () => {
  const { settings } = useMerchantSettings();

  const {
    register,
    formState: { errors, isSubmitting },
    watch,
    control,
    setValue,
  } = useFormContext<TerminalFormSchemaType>();
  const [isPracticeMode, documentType] = watch([
    'isPracticeMode',
    'documentType',
  ]);

  const isCompleted = useIsTerminalFormStepsCompleted([
    TerminalFormStep.DOCUMENT_IDENTIFICATION,
  ]);
  const isVisible = useIsDocumentIdentificationStepVisible();

  useEffect(() => {
    if (documentType == null && isVisible) {
      setValue('documentType', UserDocumentType.ID_CARD);
    }

    if (!isVisible) {
      setValue('documentType', null);
      setValue('documentNr', '');
      setValue('pin', '');
    }
  }, [documentType, isVisible, setValue]);

  return {
    isCompleted,
    isPracticeMode,
    isVisible,
    isSubmitting,
    register,
    control,
    errors,
    settings,
  };
};
