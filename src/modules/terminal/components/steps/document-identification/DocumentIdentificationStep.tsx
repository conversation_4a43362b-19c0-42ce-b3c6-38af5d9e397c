import { TerminalStepWrapper } from 'modules/terminal/components/shared';
import { useDocumentIdentificationStep } from 'modules/terminal/components/steps/document-identification/DocumentIdentificationStep.hooks';
import { useTranslation } from 'react-i18next';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { ControlledToggle, TextInput, ToggleItem } from 'shared/components';
import { CypressTerminalKeys } from 'shared/constants/cypress-keys/terminal-page';
import { LocizeNamespaces } from 'shared/constants/localization-keys';
import { availableIdentityDocuments } from 'shared/lib';

export const DocumentIdentificationStep = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.TERMINAL);

  const {
    settings,
    isCompleted,
    isSubmitting,
    errors,
    isPracticeMode,
    isVisible,
    control,
    register,
  } = useDocumentIdentificationStep();

  return (
    <TerminalStepWrapper
      dataCy={CypressTerminalKeys.DOCUMENT_IDENTIFICATION_STEP}
      isCompleted={isCompleted}
      isVisible={isVisible}
      notCompletedEl={t('identity-document.not-completed-text')}
      stepNo={5}
      subtitle={t('identity-document.disclaimer', {
        amount:
          settings?.max_amount_id_verification_not_required.toLocaleString(
            i18n.language,
          ),
      })}
      title={t('identity-document.title')}
    >
      <TextInput
        data-cy={CypressTerminalKeys.DOCUMENT_IDENTIFICATION_PIN_INPUT}
        label={t('identity-document.pin.label')}
        {...register('pin')}
        error={
          errors.pin?.message
            ? t(`common:forms.${errors.pin.message}`)
            : undefined
        }
        isDisabled={isSubmitting}
        mb={4}
      />
      <ControlledToggle
        control={control}
        isDisabled={isSubmitting}
        mb={4}
        name="documentType"
        w="100%"
      >
        {availableIdentityDocuments.map((type, index) => (
          <ToggleItem
            colorScheme={
              isPracticeMode ? ColorSchemes.NEUTRAL : ColorSchemes.PRIMARY
            }
            dataCy={`${CypressTerminalKeys.DOCUMENT_IDENTIFICATION_DOCUMENT_TYPE_SELECTOR_OPTION}-${index}`}
            key={`document-type-${type}`}
            value={type}
          >
            {t(`identity-document.document-type.${type}`)}
          </ToggleItem>
        ))}
      </ControlledToggle>
      <TextInput
        data-cy={
          CypressTerminalKeys.DOCUMENT_IDENTIFICATION_DOCUMENT_NUMBER_INPUT
        }
        label={t('identity-document.document-number.label')}
        {...register('documentNr')}
        error={
          errors.documentNr?.message
            ? t(`common:forms.${errors.documentNr?.message}`)
            : undefined
        }
        isDisabled={isSubmitting}
      />
    </TerminalStepWrapper>
  );
};
