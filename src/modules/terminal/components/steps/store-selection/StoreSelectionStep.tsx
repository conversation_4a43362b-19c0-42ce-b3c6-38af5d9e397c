import { <PERSON>, <PERSON>lapse, Flex, Link } from '@chakra-ui/react';
import { TerminalStepWrapper } from 'modules/terminal/components/shared';
import { useStoreSelectionStep } from 'modules/terminal/components/steps/store-selection/StoreSelectionStep.hooks';
import { StoreTypes } from 'modules/terminal/Terminal.types';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { ControlledToggle, Select, ToggleItem } from 'shared/components';
import { CypressTerminalKeys } from 'shared/constants/cypress-keys/terminal-page';
import { SettingsTabRoute } from 'shared/constants/routes';
import { SettingsRouteTabs } from 'shared/types';

import { terminalFormState } from '../../../Terminal.utils';

const STORE_TYPES = [
  {
    value: StoreTypes.PHYSICAL,
    label: 'select-store.physical-store',
    dataCy: CypressTerminalKeys.STORE_TYPE_SELECTOR_PHYSICAL_OPTION,
  },
  {
    value: StoreTypes.ONLINE,
    label: 'select-store.online-store',
    dataCy: CypressTerminalKeys.STORE_TYPE_SELECTOR_ONLINE_OPTION,
  },
] as const;

export const StoreSelectionStep = () => {
  const { t } = useTranslation('terminal');
  const {
    isStoresFetched,
    storeOptions,
    storeType,
    isSubmitting,
    isPracticeMode,
    control,
    selectedStore,
  } = useStoreSelectionStep();

  return (
    <TerminalStepWrapper
      isCompleted={false}
      isVisible
      stepNo={1}
      title={t('select-store.title')}
    >
      <ControlledToggle
        control={control}
        isDisabled={isSubmitting}
        mb={4}
        name="storeType"
        w="100%"
      >
        {STORE_TYPES.map(({ value, label, dataCy }) => (
          <ToggleItem
            colorScheme={
              isPracticeMode ? ColorSchemes.NEUTRAL : ColorSchemes.PRIMARY
            }
            dataCy={dataCy}
            key={label}
            value={value}
          >
            {t(label)}
          </ToggleItem>
        ))}
      </ControlledToggle>
      <Collapse in={storeType === StoreTypes.PHYSICAL} unmountOnExit>
        {isStoresFetched && !(storeOptions.length === 0) ? (
          <>
            <Controller
              control={control}
              name="storeId"
              render={({ field: { onChange } }) => (
                <Select
                  data-cy={CypressTerminalKeys.PHYSICAL_STORE_SELECTOR}
                  isDisabled={isSubmitting}
                  items={storeOptions}
                  label={t('select-store.select-input-label')}
                  onChange={({ value }) => {
                    // update storeId in terminalFormState
                    terminalFormState.storeId(value);
                    onChange(value);
                  }}
                  value={selectedStore}
                />
              )}
            />

            <Flex justify="flex-end">
              <Link
                color="primary.800"
                href={SettingsTabRoute.format({
                  tab: SettingsRouteTabs.Stores,
                })}
                textStyle="body1-highlight"
              >
                {t('select-store.add-store')}
              </Link>
            </Flex>
          </>
        ) : (
          <Box
            bg="primary.100"
            color="primary.900"
            mt={2}
            px={3}
            py={2}
            textStyle="body2"
          >
            {t('select-store.no-stores')}{' '}
            <Link
              color="primary.800"
              href={SettingsTabRoute.format({
                tab: SettingsRouteTabs.Stores,
              })}
              textStyle="body1-highlight"
            >
              {t('select-store.settings-link')}
            </Link>
          </Box>
        )}
      </Collapse>
    </TerminalStepWrapper>
  );
};
