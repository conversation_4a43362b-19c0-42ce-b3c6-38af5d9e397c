import { NetworkStatus, useReactiveVar } from '@apollo/client';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { StoreTypes } from 'modules/terminal/Terminal.types';
import { useEffect, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import type { MerchantQuery } from 'shared/api';
import { useMerchantDetails } from 'shared/hooks/merchant';

import { terminalFormState } from '../../../Terminal.utils';

type Store = NonNullOrUndefined<
  ArrayElement<
    NonNullOrUndefined<
      NonNullOrUndefined<
        NonNullOrUndefined<MerchantQuery['merchant']>
      >['stores']
    >
  >
>;

export const useStoreSelectionStep = () => {
  const {
    watch,
    control,
    formState: { isSubmitting },
    setValue,
    resetField,
  } = useFormContext<TerminalFormSchemaType>();
  const [isPracticeMode, storeType, storeId] = watch([
    'isPracticeMode',
    'storeType',
    'storeId',
  ]);

  const { networkStatus, data } = useMerchantDetails({
    notifyOnNetworkStatusChange: true,
  });

  const filteredStores = useMemo(
    () =>
      data?.merchant?.stores?.reduce<Array<Store>>(
        (acc, store) => (store ? [...acc, store] : acc),
        [],
      ) || [],
    [data?.merchant?.stores],
  );

  const storeOptions = useMemo(
    () => filteredStores.map(({ id, name }) => ({ label: name, value: id })),
    [filteredStores],
  );

  const selectedStore = useMemo(
    () => storeOptions.find((store) => store?.value === storeId) || undefined,
    [storeId, storeOptions],
  );

  const savedStoreId = useReactiveVar(terminalFormState.storeId);

  useEffect(() => {
    // Reset store ID on store type change
    if (storeType === StoreTypes.ONLINE && !!storeId) {
      resetField('storeId');
      return;
    }

    if (!storeId && storeType === StoreTypes.PHYSICAL) {
      if (savedStoreId) {
        // Set saved store ID if it's exist
        setTimeout(() => {
          setValue('storeId', savedStoreId);
        }, 0);
      } else if (filteredStores.length > 0) {
        // Set first store ID on physical store if it's exist
        setTimeout(() => {
          setValue('storeId', filteredStores[0].id);
        }, 0);
      }
    }
  }, [filteredStores, resetField, setValue, storeId, storeType, savedStoreId]);

  return {
    control,
    isSubmitting,
    isPracticeMode,
    storeType,
    isStoresFetched: networkStatus === NetworkStatus.ready,
    storeOptions,
    selectedStore,
  };
};
