import { useIsTerminalFormStepsCompleted } from 'modules/terminal/hooks';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { StoreTypes, TerminalFormStep } from 'modules/terminal/Terminal.types';
import { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';

export const usePersonalInfoStep = () => {
  const {
    watch,
    control,
    register,
    resetField,
    formState: { isSubmitting },
  } = useFormContext<TerminalFormSchemaType>();
  const [storeType, isPracticeMode] = watch(['storeType', 'isPracticeMode']);

  const isVisible = useIsTerminalFormStepsCompleted([
    TerminalFormStep.STORE_SELECTION,
    TerminalFormStep.PURCHASE_DETAILS,
    TerminalFormStep.PAYMENT_PLAN_SELECTION,
  ]);

  useEffect(() => {
    if (storeType === StoreTypes.PHYSICAL) {
      resetField('firstName');
      resetField('lastName');
      resetField('personalInfoEmail');
      resetField('personalInfoPhone');
    }
  }, [resetField, storeType]);

  return {
    isVisible,
    isSubmitting,
    control,
    register,
    isPracticeMode,
    storeType,
  };
};
