import { VStack } from '@chakra-ui/react';
import { TerminalPaymentPlanItem } from 'modules/terminal/components';
import { TerminalStepWrapper } from 'modules/terminal/components/shared';
import { useTranslation } from 'react-i18next';
import { CypressTerminalKeys } from 'shared/constants/cypress-keys/terminal-page';
import { LocizeNamespaces } from 'shared/constants/localization-keys';

import { usePaymentPlanSelectionStep } from './PaymentPlanSelectionStep.hooks';

export const PaymentPlanSelectionStep = () => {
  const { t } = useTranslation(LocizeNamespaces.TERMINAL);
  const {
    paymentPlans,
    selectedScheduleType,
    isVisible,
    creditSettingsPeriodInfo,
    isCreditSettingsLoading,
  } = usePaymentPlanSelectionStep();

  return (
    <TerminalStepWrapper
      dataCy={CypressTerminalKeys.PAYMENT_PLAN_SELECTION_STEP}
      isCompleted
      isVisible={isVisible}
      stepNo={2}
      title={t('payment-plan.title')}
    >
      <VStack alignItems="stretch" shouldWrapChildren spacing={2}>
        {paymentPlans.map((plan, index) => {
          const isSelected = selectedScheduleType === plan.scheduleType;
          return (
            <TerminalPaymentPlanItem
              creditSettingsPeriodInfo={
                isSelected ? creditSettingsPeriodInfo : null
              }
              dataCy={`${CypressTerminalKeys.PAYMENT_PLAN_SELECTION_OPTION}-${index}`}
              isCreditSettingsLoading={
                isSelected ? isCreditSettingsLoading : false
              }
              isSelected={plan.scheduleType === selectedScheduleType}
              key={plan.scheduleType}
              plan={plan}
            />
          );
        })}
      </VStack>
    </TerminalStepWrapper>
  );
};
