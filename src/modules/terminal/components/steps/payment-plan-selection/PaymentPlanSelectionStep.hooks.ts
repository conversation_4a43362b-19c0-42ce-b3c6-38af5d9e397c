import {
  useCreditSettingsPeriodInfoByScheduleType,
  useIsRequiredMerchantDownPayment,
  useIsTerminalFormStepsCompleted,
  useTerminalPaymentPlans,
} from 'modules/terminal/hooks';
import { useIsValidLoanAmountByScheduleType } from 'modules/terminal/Terminal.hooks';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { TerminalFormStep } from 'modules/terminal/Terminal.types';
import { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { ApplicationScheduleType } from 'shared/api';

export const usePaymentPlanSelectionStep = () => {
  const isVisible = useIsTerminalFormStepsCompleted([
    TerminalFormStep.STORE_SELECTION,
  ]);
  const paymentPlans = useTerminalPaymentPlans();
  const isRequiredMerchantDownPayment = useIsRequiredMerchantDownPayment();

  const { watch, setValue } = useFormContext<TerminalFormSchemaType>();

  const [selectedScheduleType, amount, merchantDownPayment] = watch([
    'scheduleType',
    'amount',
    'merchantDownPayment',
  ]);

  const netTotal = isRequiredMerchantDownPayment
    ? amount - merchantDownPayment
    : amount;

  const isValidAmount = useIsValidLoanAmountByScheduleType({
    amount,
    scheduleType: selectedScheduleType,
  });

  const { creditSettingsPeriodInfo, isLoading: isCreditSettingsLoading } =
    useCreditSettingsPeriodInfoByScheduleType({
      scheduleType: selectedScheduleType,
      skip:
        !selectedScheduleType ||
        !isValidAmount ||
        selectedScheduleType === ApplicationScheduleType.PAY_LATER,
      netTotal,
    });

  // auto select first payment plan
  useEffect(() => {
    if (!selectedScheduleType && paymentPlans.length > 0) {
      setValue('scheduleType', paymentPlans[0].scheduleType);
    }
  }, [paymentPlans, selectedScheduleType, setValue]);

  return {
    selectedScheduleType,
    paymentPlans,
    isVisible,
    creditSettingsPeriodInfo,
    isCreditSettingsLoading,
  };
};
