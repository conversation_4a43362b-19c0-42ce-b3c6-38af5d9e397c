import {
  Badge,
  Box,
  HStack,
  Icon,
  IconButton,
  Switch,
  Text,
  Tooltip,
  useDisclosure,
} from '@chakra-ui/react';
import {
  useHasCashierAnyBonus,
  useIsCashierBonusEnabled,
} from 'modules/applications/list/controls/hooks';
import { CreditEligibilityModal } from 'modules/applications/shared/components/credit-eligibility-modal';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FiRotateCw, FiUserCheck } from 'react-icons/fi';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { ConfirmationPopover } from 'shared/components';
import { CypressTerminalKeys } from 'shared/constants/cypress-keys/terminal-page';

import { CashierBonus } from './cashier-bonus/CashierBonus';

export const TerminalControlsDesktop = () => {
  const { t } = useTranslation('terminal');
  const {
    watch,
    setValue,
    reset: handleFormReset,
  } = useFormContext<TerminalFormSchemaType>();
  const isPracticeMode = watch('isPracticeMode');
  const isCashierBonusEnabled = useIsCashierBonusEnabled();
  const hasCashierAnyBonus = useHasCashierAnyBonus();
  const {
    isOpen: isCreditModalOpen,
    onOpen: onCreditModalOpen,
    onClose: onCreditModalClose,
  } = useDisclosure();

  const handlePracticeModeToggle = () => {
    setValue('isPracticeMode', !isPracticeMode, { shouldDirty: true });
  };

  return (
    <Box alignItems="center" display="flex" h={16} px={2}>
      <HStack alignItems="center" ml={4} spacing={2}>
        <Switch
          colorScheme={ColorSchemes.PRIMARY}
          data-cy={CypressTerminalKeys.PRACTICE_MODE_TOGGLER}
          isChecked={isPracticeMode}
          onChange={handlePracticeModeToggle}
        />
        <Text
          color={isPracticeMode ? 'neutral.900' : 'neutral.800'}
          textStyle={isPracticeMode ? 'body2-highlight' : 'body2'}
        >
          {t(`practice-mode.${isPracticeMode ? 'on' : 'off'}`)}
        </Text>
      </HStack>

      <Box alignItems="center" display="flex" ml="auto">
        {isCashierBonusEnabled && hasCashierAnyBonus && <CashierBonus />}
        <Box position="relative">
          <Tooltip
            arrowSize={8}
            hasArrow
            label="Check Credit Eligibility"
            placement="bottom-end"
          >
            <IconButton
              aria-label="credit-eligibility"
              data-cy="credit-eligibility-test-button"
              icon={<Icon as={FiUserCheck} boxSize={6} color="primary.800" />}
              isRound
              onClick={onCreditModalOpen}
              variant="ghost"
            />
          </Tooltip>
          <Badge
            position="absolute"
            top="-2px"
            right="-2px"
            colorScheme="green"
            fontSize="10px"
            px={1}
            py={0.5}
            borderRadius="full"
            textTransform="uppercase"
            fontWeight="bold"
          >
            New
          </Badge>
        </Box>
        <ConfirmationPopover
          actionText={t('clear-terminal.action')}
          cyPrefix={CypressTerminalKeys.RESET_BUTTON_CONFIRM}
          onAction={handleFormReset}
          popoverPlacement="bottom-end"
          title={t('clear-terminal.title')}
          trigger={
            <Box>
              <Tooltip
                arrowSize={8}
                hasArrow
                label={t('clear-terminal.tooltip')}
                placement="bottom-end"
              >
                <IconButton
                  aria-label="reset"
                  data-cy={CypressTerminalKeys.RESET_BUTTON}
                  icon={
                    <Icon as={FiRotateCw} boxSize={6} color="primary.800" />
                  }
                  isRound
                  variant="ghost"
                />
              </Tooltip>
            </Box>
          }
        />
      </Box>

      <CreditEligibilityModal
        isOpen={isCreditModalOpen}
        onClose={onCreditModalClose}
      />
    </Box>
  );
};
