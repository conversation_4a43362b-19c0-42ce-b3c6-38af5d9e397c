import { Box, Text } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { ResendLinkBlock } from 'shared/components';
import { TerminalApplicationRoute } from 'shared/constants/routes';
import { ApplicationLinkAction } from 'shared/types';

export type ActionBlockProps = {
  action: ApplicationLinkAction;
  applicationId: number;
  purchaseUrl: string;
  isPracticeMode: boolean;
  isFromRetail: boolean;
};

export const ActionsBlock = ({ applicationId, ...props }: ActionBlockProps) => {
  const { t } = useTranslation('terminal');

  return (
    <Box
      border="1px solid"
      borderColor="neutral.100"
      borderRadius="4px"
      data-cy="actions-block"
      px={4}
      py={5}
    >
      <Text mb={5} mx={3} textStyle="body2-highlight">
        {t('application-status.actions-title')}
      </Text>
      <ResendLinkBlock
        {...props}
        applicationId={applicationId}
        qrCodeRedirect={TerminalApplicationRoute.format({
          applicationId,
          action: ApplicationLinkAction.GenerateQR,
        })}
      />
    </Box>
  );
};
