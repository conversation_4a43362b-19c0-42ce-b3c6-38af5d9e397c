import { NetworkStatus, useApolloClient } from '@apollo/client';
import {
  type PropsWithChildren,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import {
  ADMIN_MERCHANTS_QUERY,
  type AdminMerchantsQuery,
  type AdminMerchantsQueryVariables,
} from 'shared/api';
import { Loader } from 'shared/components';
import { useAssignedMerchants } from 'shared/hooks/merchant';
import { GlobalStateContext } from 'shared/hooks/state';
import { useCurrentUser, useIsSuperAdmin } from 'shared/hooks/user';
import {
  CURRENT_MERCHANT_STORAGE_KEY,
  getItem,
  MERCHANT_INVITE_HASH_KEY,
  setItem,
} from 'shared/lib';
import { authModel } from 'shared/models/auth';

export const GlobalStateProvider = ({ children }: PropsWithChildren) => {
  const apollo = useApolloClient();
  const { user, networkStatus } = useCurrentUser({
    notifyOnNetworkStatusChange: true,
  });
  const [isLoadingDefaultMerchant, setIsLoadingDefaultMerchant] =
    useState(false);
  const [inMemoryMerchantId, _setInMemoryMerchantId] = useState<number | null>(
    null,
  );
  const setMerchantId = useCallback((id: number | null) => {
    _setInMemoryMerchantId(id);
    setItem(CURRENT_MERCHANT_STORAGE_KEY, id);
  }, []);

  // if user visit merchant-invite page - set isInviteVisitedPage to true
  const [isVisitedInvitePage, _setIsVisitedInvitePage] = useState<
    string | null
  >(null);
  const setIsVisitedInvitePage = useCallback((isInvitePage: string) => {
    _setIsVisitedInvitePage(isInvitePage);
    setItem(MERCHANT_INVITE_HASH_KEY, isInvitePage);
  }, []);

  const isSuperAdmin = useIsSuperAdmin();
  const merchants = useAssignedMerchants();

  // settle current merchant in case user doesn't have one selected previously
  // or if previously selected merchant is not available anymore
  useEffect(() => {
    const persistedInviteHash = getItem<string>(MERCHANT_INVITE_HASH_KEY);
    if (persistedInviteHash) {
      setIsVisitedInvitePage(persistedInviteHash);
    }

    const persistedId = getItem<number>(CURRENT_MERCHANT_STORAGE_KEY);

    // if it's super admin
    if (isSuperAdmin) {
      // if we already have merchant id do nothing
      if (inMemoryMerchantId) {
        return;
      }
      // if we have merchant id from local storage - set in memory id
      if (persistedId) {
        setMerchantId(persistedId);
      }
      // fetch all merchants otherwise and set first available id
      else {
        setIsLoadingDefaultMerchant(true);
        apollo
          .query<AdminMerchantsQuery, AdminMerchantsQueryVariables>({
            query: ADMIN_MERCHANTS_QUERY,
            variables: { name: '', limit: 1 },
          })
          .then((resp) => {
            const firstMerchantId = resp?.data?.merchants?.data?.[0].id;
            if (firstMerchantId) {
              setMerchantId(firstMerchantId);
            }
            setIsLoadingDefaultMerchant(false);
          })
          .catch(() => {
            setIsLoadingDefaultMerchant(false);
            // TODO: add some sentry error capture
          });
      }

      return;
    }

    // if user doesn't have merchants assigned do nothing
    if (!merchants.length) {
      return;
    }

    const merchantId = inMemoryMerchantId || persistedId;
    const isMerchantInList = !!merchants.find(
      (merch) => merch?.id === merchantId,
    );
    const defaultId = merchants?.[0]?.id || null;

    if (isMerchantInList) {
      if (!inMemoryMerchantId || inMemoryMerchantId !== merchantId) {
        setMerchantId(merchantId);
      }
    } else if (defaultId) {
      setMerchantId(defaultId);
    }
  }, [
    merchants,
    setMerchantId,
    inMemoryMerchantId,
    isSuperAdmin,
    apollo,
    setIsVisitedInvitePage,
  ]);

  const contextValue = useMemo(
    () => ({
      merchantId: inMemoryMerchantId,
      setMerchantId,
      isVisitedInvitePage,
      setIsVisitedInvitePage,
    }),
    [
      inMemoryMerchantId,
      setMerchantId,
      setIsVisitedInvitePage,
      isVisitedInvitePage,
    ],
  );

  useEffect(() => {
    if (networkStatus !== NetworkStatus.ready) {
      return;
    }

    if (!user) {
      return;
    }

    authModel.setUserEv({
      id: user.id,
      email: user.email,
      firstName: user.profile?.first_name,
      lastName: user.profile?.last_name,
      languageAbbr: user.language_abbr,
    });
  }, [networkStatus, user]);

  return (
    <GlobalStateContext.Provider value={contextValue}>
      {isLoadingDefaultMerchant ? <Loader fullScreen /> : children}
    </GlobalStateContext.Provider>
  );
};
