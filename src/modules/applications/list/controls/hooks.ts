import { useMerchantDetails } from 'shared/hooks/merchant';
import { useIsCashier } from 'shared/hooks/user';

export const useIsCashierBonusEnabled = (): boolean => {
  const isCashier = useIsCashier();
  const { data: merchantData } = useMerchantDetails();

  const isCashierLoyaltyEnabled =
    !!merchantData?.merchant?.cashier_loyalty_enabled;

  return isCashier && isCashierLoyaltyEnabled;
};

export const useHasCashierAnyBonus = () => {
  const { data: merchantDetails } = useMerchantDetails();

  return !!merchantDetails?.merchant?.settings?.cashier_bonus_pct;
};
