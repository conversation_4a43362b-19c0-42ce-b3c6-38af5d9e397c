import { Box, Icon, IconButton } from '@chakra-ui/react';
import { SearchInputForApplicationTable } from 'modules/applications/list/controls/shared';
import { MobileFilters } from 'modules/applications/list/MobileFilters';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FiArrowLeft, FiSearch } from 'react-icons/fi';
import { Header } from 'shared/components';
import {
  LocizeCommonKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';

export const ApplicationTableHeaderControls = () => {
  const { t: tc } = useTranslation(LocizeNamespaces.COMMON);
  const [isSearchActive, setIsSearchActive] = useState(false);

  return (
    <>
      <Header
        after={
          !isSearchActive ? (
            <Box alignItems="center" display="flex" ml="auto">
              <IconButton
                aria-label="search toggle"
                data-cy="applications-search-toggle"
                icon={<Icon as={FiSearch} boxSize={6} color="primary.800" />}
                isRound
                onClick={() => {
                  setIsSearchActive(true);
                }}
                variant="ghost"
              />
              <MobileFilters />
            </Box>
          ) : undefined
        }
        before={
          isSearchActive ? (
            <>
              <IconButton
                aria-label="back"
                icon={<Icon as={FiArrowLeft} boxSize={6} color="primary.800" />}
                isRound
                mx={2}
                my={1}
                onClick={() => {
                  setIsSearchActive(false);
                }}
                variant="ghost"
              />
              <SearchInputForApplicationTable />
              <Box alignItems="center" display="flex">
                <MobileFilters />
              </Box>
            </>
          ) : undefined
        }
        isSidebarButtonEnabled={!isSearchActive}
        title={
          !isSearchActive
            ? tc(LocizeCommonKeys.SIDEBAR_APPLICATIONS_OPTION)
            : undefined
        }
      />
    </>
  );
};
