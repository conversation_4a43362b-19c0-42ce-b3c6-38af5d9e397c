import { useReactiveVar } from '@apollo/client';
import { SwitchFilter } from 'modules/applications/list/controls/shared';
import { applicationsTableState } from 'modules/applications/shared';
import { useTranslation } from 'react-i18next';
import { AdminApplicationStatuses } from 'shared/api';

export const ApplicationsTestTableFilter = () => {
  const { t } = useTranslation('applications');
  const statuses = useReactiveVar(applicationsTableState.statuses);
  const isTestShown = statuses.includes(AdminApplicationStatuses.TEST_MODE);

  const onToggleShowTest = () => {
    applicationsTableState.statuses(
      isTestShown
        ? statuses.filter(
            (status) => status !== AdminApplicationStatuses.TEST_MODE,
          )
        : [...statuses, AdminApplicationStatuses.TEST_MODE],
    );
    applicationsTableState.page(1);
  };

  return (
    <SwitchFilter
      cyPrefix="test"
      isChecked={isTestShown}
      label={t('filters.test-applications')}
      onToggle={onToggleShowTest}
    />
  );
};
