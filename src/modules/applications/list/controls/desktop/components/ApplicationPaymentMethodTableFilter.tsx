import { useReactiveVar } from '@apollo/client';
import {
  applicationsTableState,
  defaultApplicationTableState,
  PaymentMethodBadge,
  useFilterPaymentMethods,
} from 'modules/applications/shared';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { MultiSelect } from 'shared/components';

export const ApplicationPaymentMethodTableFilter = () => {
  const { t } = useTranslation('applications');
  const selectPaymentMethods = useFilterPaymentMethods();
  const paymentMethods = useReactiveVar(applicationsTableState.paymentMethods);

  const defaultSelectPaymentMethodValue = useMemo(
    () =>
      selectPaymentMethods.filter((item) =>
        defaultApplicationTableState.paymentMethods.some((value) =>
          item.value.includes(value),
        ),
      ),
    [selectPaymentMethods],
  );

  const selectPaymentMethodValue = selectPaymentMethods.filter((item) =>
    paymentMethods.some((value) => item.value.includes(value)),
  );

  const onSelectPaymentMethodChange = (
    items: typeof selectPaymentMethodValue,
  ): void => {
    const withoutSelectPaymentMethods = paymentMethods.filter(
      (status) =>
        !selectPaymentMethods.find(({ value }) => value.includes(status)),
    );
    applicationsTableState.page(1);
    applicationsTableState.paymentMethods([
      ...withoutSelectPaymentMethods,
      ...items.flatMap((item) => item.value),
    ]);
  };

  return (
    <MultiSelect
      allSelectedText={t('filters.all-payment-methods')}
      data-cy="applications-multiselect"
      defaultValue={defaultSelectPaymentMethodValue}
      flexShrink={0}
      items={selectPaymentMethods}
      maxSelectedShown={2}
      minW="12.5rem"
      onChange={onSelectPaymentMethodChange}
      renderItem={(item) => (
        <PaymentMethodBadge paymentMethod={item.value[0]} />
      )}
      value={selectPaymentMethodValue}
      withResetBtn
    />
  );
};
