import { useReactiveVar } from '@apollo/client';
import { SwitchFilter } from 'modules/applications/list/controls/shared';
import { applicationsTableState } from 'modules/applications/shared';
import { useTranslation } from 'react-i18next';

export const ApplicationsIssuedByMeTableFilter = () => {
  const { t } = useTranslation('applications');
  const isIssuedByMe = useReactiveVar(applicationsTableState.issuedByMe);

  const onToggleIssuedByMe = () => {
    applicationsTableState.issuedByMe(!isIssuedByMe);
    applicationsTableState.page(1);
  };

  return (
    <SwitchFilter
      isChecked={isIssuedByMe}
      label={t('filters.issued-by-me')}
      onToggle={onToggleIssuedByMe}
    />
  );
};
