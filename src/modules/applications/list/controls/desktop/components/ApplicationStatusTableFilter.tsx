import { useReactiveVar } from '@apollo/client';
import {
  applicationsTableState,
  defaultApplicationTableState,
  StatusBadge,
  useApplicationStatusesFilterOptions,
} from 'modules/applications/shared';
import { useTranslation } from 'react-i18next';
import { MultiSelect } from 'shared/components';

export const ApplicationStatusTableFilter = () => {
  const { t } = useTranslation('applications');

  const statuses = useReactiveVar(applicationsTableState.statuses);
  const selectStatusItems = useApplicationStatusesFilterOptions();

  const selectStatusValue = selectStatusItems.filter((item) =>
    statuses.includes(item.value),
  );

  const defaultSelectStatusValue = selectStatusItems.filter((item) =>
    defaultApplicationTableState.statuses.includes(item.value),
  );

  const onSelectStatusChange = (items: typeof selectStatusValue) => {
    const withoutSelectStatuses = statuses.filter(
      (status) => !selectStatusItems.find(({ value }) => value === status),
    );
    applicationsTableState.statuses([
      ...withoutSelectStatuses,
      ...items.map(({ value }) => value),
    ]);
    applicationsTableState.page(1);
  };

  return (
    <MultiSelect
      allSelectedText={t('filters.all-statuses')}
      data-cy="applications-multiselect"
      defaultValue={defaultSelectStatusValue}
      flexShrink={0}
      items={selectStatusItems}
      maxSelectedShown={2}
      minW="12.5rem"
      onChange={onSelectStatusChange}
      renderItem={(item) => <StatusBadge status={item.value} />}
      value={selectStatusValue}
      withResetBtn
    />
  );
};
