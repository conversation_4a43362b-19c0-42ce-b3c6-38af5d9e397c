import { Box, HStack, Stack } from '@chakra-ui/react';

import { SearchInputForApplicationTable } from '../shared';
import {
  ApplicationPaymentMethodTableFilter,
  ApplicationsIssuedByMeTableFilter,
  ApplicationStatusTableFilter,
  ApplicationsTestTableFilter,
} from './components';

export const ApplicationsTableDesktopControls = () => {
  return (
    <Box pb={6} pt={10} px={10}>
      <Stack
        alignItems={['stretch', null, null, null, 'center']}
        direction={['column', null, null, null, 'row']}
        spacing={[4, null, null, null, 6]}
      >
        <HStack alignItems="center" flexGrow={1} maxW="44rem" spacing={6}>
          <SearchInputForApplicationTable />
          <ApplicationStatusTableFilter />
          <ApplicationPaymentMethodTableFilter />
        </HStack>
        <HStack alignItems="center" flexShrink={0} spacing={6}>
          <ApplicationsIssuedByMeTableFilter />
          <ApplicationsTestTableFilter />
        </HStack>
      </Stack>
    </Box>
  );
};
