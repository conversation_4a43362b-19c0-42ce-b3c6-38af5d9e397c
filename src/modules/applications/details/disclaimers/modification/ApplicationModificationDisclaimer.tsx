import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ltip, useDisclosure } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { TiDeleteOutline } from 'react-icons/ti';
import { MerchantApplicationDetailsDocument } from 'shared/api';
import {
  ApplicationDetailsDisclaimer,
  type ApplicationDetailsDisclaimerProps,
  UndoApplicationRequestDialog,
} from 'shared/components/application';
import {
  LocizeApplicationsKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { useShowError } from 'shared/hooks/alerts';
import { useApplicationId } from 'shared/hooks/application';

import { useDeleteApplicationModificationRequestMutation } from './api';

type Props = {
  requestId: number;
} & ApplicationDetailsDisclaimerProps;

export const ApplicationModificationDisclaimer = ({
  date,
  requestId,
  type,
  requestCreatorFullName,
}: Props) => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);
  const { isOpen, onClose, onOpen } = useDisclosure();
  const applicationId = useApplicationId();

  const showError = useShowError();

  const [deleteRequest, { loading }] =
    useDeleteApplicationModificationRequestMutation({
      variables: {
        requestId,
      },
      refetchQueries: [
        {
          query: MerchantApplicationDetailsDocument,
          variables: { applicationId },
        },
      ],
      awaitRefetchQueries: true,
    });

  const handleDeleteRequestSubmit = async () => {
    try {
      await deleteRequest();
    } catch (error) {
      showError(
        "Can't cancel application modification. Please, try again later.",
      );
      if (error instanceof Error) {
        throw new Error(error?.message);
      }
    }
  };

  return (
    <>
      <ApplicationDetailsDisclaimer
        after={
          <Tooltip label={t(LocizeApplicationsKeys.APPLICATION_UPDATE_UNDO)}>
            <IconButton
              aria-label="delete-modification"
              color="primary.700"
              disabled={loading}
              fontSize="20px"
              icon={<TiDeleteOutline />}
              isRound={true}
              onClick={onOpen}
              size="20px"
              variant="ghost"
            />
          </Tooltip>
        }
        date={date}
        requestCreatorFullName={requestCreatorFullName}
        type={type}
      />
      {!!isOpen && (
        <UndoApplicationRequestDialog
          isLoading={loading}
          isOpen
          onClose={onClose}
          onSubmit={handleDeleteRequestSubmit}
        />
      )}
    </>
  );
};
