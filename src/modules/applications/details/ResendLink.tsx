import { Box } from '@chakra-ui/react';
import { ResendLinkBlock } from 'shared/components';
import type { ApplicationType } from 'shared/types';

type Props = Pick<ApplicationType, 'id' | 'purchase_url'>;

export const ResendLink = ({ purchase_url, id }: Props) => (
  <Box
    border="1px solid"
    borderColor="neutral.150"
    borderRadius="4px"
    mb="4px"
    p={4}
  >
    <ResendLinkBlock applicationId={id} purchaseUrl={purchase_url} />
  </Box>
);
