import { LocizeApplicationsKeys } from 'shared/constants/localization-keys';

export const TABLE_COLUMNS = [
  {
    label: LocizeApplicationsKeys.MODIFICATION_HISTORY_TABLE_COLUMN_REQUEST,
    isNumber: false,
  },
  {
    label: LocizeApplicationsKeys.MODIFICATION_HISTORY_TABLE_COLUMN_AMOUNT,
    isNumber: true,
  },
  {
    label: LocizeApplicationsKeys.MODIFICATION_HISTORY_TABLE_COLUMN_DATE,
    isNumber: false,
  },
  {
    label: LocizeApplicationsKeys.MODIFICATION_HISTORY_TABLE_COLUMN_REQUEST_BY,
    isNumber: false,
  },
  {
    label: LocizeApplicationsKeys.MODIFICATION_HISTORY_TABLE_COLUMN_REASON,
    isNumber: false,
  },
] as const;

export const ApplicationRequestLabel = {
  MODIFICATION:
    LocizeApplicationsKeys.MODIFICATION_HISTORY_REQUEST_AMOUNT_MODIFICATION,
  CANCELLATION:
    LocizeApplicationsKeys.MODIFICATION_HISTORY_REQUEST_CANCELLATION,
} as const;
