import { Box, Table, Tbody, Text, Thead, Tooltip, Tr } from '@chakra-ui/react';
import { format } from 'date-fns';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDateFnsLocale } from 'shared/components/date-range/common';
import { Td, Th } from 'shared/components/table';
import { LocizeNamespaces } from 'shared/constants/localization-keys';
import type { ApplicationType } from 'shared/types';
import { getFullName } from 'shared/utils/name';

import {
  ApplicationRequestLabel,
  TABLE_COLUMNS,
} from './ApplicationRequestHistory.consants';

type Props = Pick<
  ApplicationType,
  'modification_requests' | 'cancellation_request' | 'cancelled_at'
>;

type FilteredModificationRequest = NonNullOrUndefined<
  ArrayElement<NonNullOrUndefined<ApplicationType['modification_requests']>>
>;

type Request = NonNullOrUndefined<
  ApplicationType['cancellation_request'] | FilteredModificationRequest
>;

const getRequestCreatorFullName = (request: Request) =>
  request?.merchant_creator?.name ||
  `${request?.creator?.profile?.first_name} ${request?.creator?.profile?.last_name}`.trim() ||
  undefined;

export const ApplicationRequestHistory = ({
  cancellation_request,
  modification_requests,
  cancelled_at,
}: Props) => {
  const locale = useDateFnsLocale();
  const { i18n, t } = useTranslation(LocizeNamespaces.APPLICATIONS);

  const filteredModificationRequests = useMemo(
    () =>
      modification_requests
        ?.reduce<Array<FilteredModificationRequest>>(
          (acc, modification_request) => {
            if (modification_request?.handled_at) {
              return [...acc, modification_request];
            }

            return acc;
          },
          [],
        )
        .reverse() || [],
    [modification_requests],
  );

  if (
    filteredModificationRequests.length === 0 &&
    !(cancelled_at && cancellation_request)
  ) {
    return null;
  }

  return (
    <Box overflow="auto">
      <Table
        overflow={{ base: 'auto', xl: 'unset' }}
        position="relative"
        width="100%"
      >
        <Thead
          bg="white"
          borderBottom="1px solid"
          borderColor="neutral.100"
          left={0}
          position="sticky"
          top={0}
          width="100%"
        >
          <Tr>
            {TABLE_COLUMNS.map(({ label, isNumber }) => (
              <Th key={label} textAlign={isNumber ? 'right' : 'left'}>
                <Text>{t(label)}</Text>
              </Th>
            ))}
          </Tr>
        </Thead>
        <Tbody
          sx={{
            'tr:last-child td': {
              borderBottom: 'none',
              pb: 0,
            },
          }}
        >
          {!!cancellation_request && !!cancelled_at && (
            <Tr>
              <Td>
                <Text>{t(ApplicationRequestLabel.CANCELLATION)}</Text>
              </Td>
              <Td textAlign="right">-</Td>
              <Td>
                <Text>
                  {format(new Date(cancelled_at * 1000), 'd.MM.yyyy', {
                    locale,
                  })}
                </Text>
              </Td>
              <Td maxW={{ xl: '7.5em' }}>
                <Tooltip
                  label={getFullName({
                    lastName: cancellation_request?.creator?.profile?.last_name,
                    firstName:
                      cancellation_request?.creator?.profile?.first_name,
                    defaultValue: '-',
                  })}
                  openDelay={500}
                >
                  <Text
                    overflow="hidden"
                    textOverflow="ellipsis"
                    whiteSpace="nowrap"
                  >
                    {getRequestCreatorFullName(cancellation_request)}
                  </Text>
                </Tooltip>
              </Td>
              <Td maxW={{ xl: '17em' }}>
                <Tooltip label={cancellation_request.reason} openDelay={500}>
                  <Text
                    overflow="hidden"
                    textOverflow="ellipsis"
                    whiteSpace="nowrap"
                  >
                    {cancellation_request.reason}
                  </Text>
                </Tooltip>
              </Td>
            </Tr>
          )}
          {filteredModificationRequests.map((modificationRequest) => {
            const { id, reason, new_requested_amount, handled_at } =
              modificationRequest;

            const fullname = getRequestCreatorFullName(modificationRequest);

            return (
              <Tr key={id}>
                <Td>
                  <Text>{t(ApplicationRequestLabel.MODIFICATION)}</Text>
                </Td>
                <Td textAlign="right">
                  <Text>
                    {`${new_requested_amount.toLocaleString(i18n.language, {
                      maximumFractionDigits: 2,
                      minimumFractionDigits: 2,
                    })}€`}
                  </Text>
                </Td>
                <Td>
                  <Text>
                    {!!handled_at &&
                      format(new Date(handled_at * 1000), 'd.MM.yyyy', {
                        locale,
                      })}
                  </Text>
                </Td>
                <Td maxW={{ xl: '7.5em' }}>
                  <Tooltip label={fullname} openDelay={500}>
                    <Text
                      overflow="hidden"
                      textOverflow="ellipsis"
                      whiteSpace="nowrap"
                    >
                      {fullname}
                    </Text>
                  </Tooltip>
                </Td>
                <Td maxW={{ xl: '17em' }}>
                  <Tooltip label={reason} openDelay={500}>
                    <Text
                      overflow="hidden"
                      textOverflow="ellipsis"
                      whiteSpace="nowrap"
                    >
                      {reason}
                    </Text>
                  </Tooltip>
                </Td>
              </Tr>
            );
          })}
        </Tbody>
      </Table>
    </Box>
  );
};
