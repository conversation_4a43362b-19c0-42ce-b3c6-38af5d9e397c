import { useCallback, useMemo } from 'react';
import type { FieldName, UseFormSetError } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  LocizeApplicationsKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { useShowMessage } from 'shared/hooks/alerts';
import { useCancelApplication } from 'shared/hooks/application';

import { CANCELLATION_REASONS } from './CancellationModal.contants';
import type { CancellationSchemaType } from './CancellationModal.schema';

type UseApplicationCancelParams = {
  applicationId: number;
  setError: UseFormSetError<CancellationSchemaType>;
  onSuccess: () => void;
};

export const useApplicationCancel = ({
  applicationId,
  setError,
  onSuccess,
}: UseApplicationCancelParams) => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);
  const showMessage = useShowMessage();

  const onFieldValidationError = useCallback(
    (field: string) => {
      setError(
        field as FieldName<CancellationSchemaType>,
        { message: 'invalid-format' },
        { shouldFocus: true },
      );
    },
    [setError],
  );
  const { isLoading: isSubmitting, cancelApplication } = useCancelApplication({
    onFieldValidationError,
  });

  const onSubmit = useCallback(
    async ({ reason, otherReason }: CancellationSchemaType) => {
      const result = await cancelApplication({
        applicationId,
        reason:
          reason === LocizeApplicationsKeys.CANCELLATION_MODAL_REASONS_OTHER
            ? otherReason || ''
            : t(reason),
      });

      if (!result) {
        return;
      }

      showMessage(t(LocizeApplicationsKeys.NOTIFICATIONS_SUCCESSFUL_REQUEST));
      onSuccess();
    },
    [cancelApplication, applicationId, t, showMessage, onSuccess],
  );

  return { onSubmit, isSubmitting };
};

export const useCancellationReasonOptions = () => {
  const { t } = useTranslation([
    LocizeNamespaces.APPLICATIONS,
    LocizeNamespaces.COMMON,
  ]);

  return useMemo(
    () => CANCELLATION_REASONS.map((value) => ({ label: t(value), value })),
    [t],
  );
};
