import { LocizeApplicationsKeys } from 'shared/constants/localization-keys';
import { z } from 'zod';

export const CancellationSchema = z
  .object({
    reason: z.string(),
    otherReason: z.string().optional(),
  })
  .refine(
    ({ reason, otherReason }) =>
      reason !== LocizeApplicationsKeys.CANCELLATION_MODAL_REASONS_OTHER ||
      (reason === LocizeApplicationsKeys.CANCELLATION_MODAL_REASONS_OTHER &&
        !!otherReason),
    {
      message: 'required',
      path: ['otherReason'],
    },
  );

export type CancellationSchemaType = z.infer<typeof CancellationSchema>;
