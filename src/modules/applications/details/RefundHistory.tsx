import { Box, Text } from '@chakra-ui/react';
import { format } from 'date-fns';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';
import {
  type ApplicationDirectPaymentRefunds,
  RefundStatuses,
} from 'shared/types';

export const RefundHistory = ({
  refunds,
}: {
  refunds: ApplicationDirectPaymentRefunds;
}) => {
  if (!refunds) {
    return null;
  }

  return (
    <Box mb={8}>
      {refunds.map((refundItem) => {
        if (!refundItem) {
          return null;
        }

        return (
          <RefundHistoryItem
            amount={refundItem.amount}
            key={refundItem.id}
            refundedAt={refundItem.refunded_at || 0}
            status={refundItem.status || ''}
          />
        );
      })}
    </Box>
  );
};

type RefundHistoryItemProps = {
  refundedAt: number | null;
  status: RefundStatuses | string | null;
  amount: number;
};

const RefundHistoryItem: FC<RefundHistoryItemProps> = ({
  refundedAt,
  status,
  amount,
}) => {
  const { t, i18n } = useTranslation('applications');
  const formattedDate = refundedAt
    ? format(refundedAt * 1000, 'dd.MM.yyyy')
    : '';

  // show only completed refund requests in refund history
  return status === RefundStatuses.COMPLETED ? (
    <Box
      alignItems="center"
      bg="primary.100"
      borderRadius="4px"
      display="flex"
      justifyContent="space-between"
      mb={2}
      mt={2}
      px={3}
      py={4}
    >
      <Text fontSize="14" fontWeight="700">
        {`${amount.toLocaleString(i18n.language, {
          maximumFractionDigits: 2,
          minimumFractionDigits: 2,
        })}€`}{' '}
        {t('refunded')}
      </Text>
      <Text fontSize="14">{formattedDate}</Text>
    </Box>
  ) : null;
};
