import { zodResolver } from '@hookform/resolvers/zod';
import type { ChangeEvent } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TextInput } from 'shared/components';
import { useUpdateMerchantApplication } from 'shared/hooks/application';
import { useDebounce } from 'shared/hooks/utils';
import { z } from 'zod';

const DEBOUNCE_TIMEOUT = 500;
type Props = {
  applicationId: number;
  initialValue?: string;
};

const OrderReferenceSchema = z.object({
  reference: z.string().min(1, 'required'),
});

type OrderReferenceSchemaType = z.infer<typeof OrderReferenceSchema>;

export const OrderReference = ({ applicationId, initialValue }: Props) => {
  const { t } = useTranslation(['applications', 'common']);
  const {
    control,
    formState: { errors },
  } = useForm<OrderReferenceSchemaType>({
    defaultValues: { reference: initialValue },
    mode: 'onChange',
    resolver: zodResolver(OrderReferenceSchema),
  });
  const { isCompleted, updateMerchantApplication } =
    useUpdateMerchantApplication();

  const handleChange = useDebounce(async (e: ChangeEvent<HTMLInputElement>) => {
    if (!!e.target.value && e.target.value !== initialValue) {
      await updateMerchantApplication({
        applicationId,
        reference: e.target.value,
      });
    }
  }, DEBOUNCE_TIMEOUT);

  return (
    <Controller
      control={control}
      name="reference"
      render={({ field: { onChange, ...rest } }) => (
        <TextInput
          autoComplete="off"
          data-cy="application-info-reference"
          label={t('details.order-reference')}
          {...rest}
          error={
            errors.reference?.message
              ? t(`common:forms.${errors.reference.message}`)
              : undefined
          }
          extraState={
            isCompleted ? t('common:forms.extra-state.saved') : undefined
          }
          onChange={(e) => {
            onChange(e);
            handleChange(e);
          }}
        />
      )}
    />
  );
};
