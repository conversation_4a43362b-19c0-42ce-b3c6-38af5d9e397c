import { Box, Table, Tbody, Tr } from '@chakra-ui/react';
import { useApplicationInfo } from 'modules/applications/details/application-info/ApplicationInfo.hooks';
import {
  ApplicationInfoRow,
  ApplicationInfoRowTd,
  EligibilityStatusInfo,
} from 'modules/applications/details/application-info/components';
import { ApplicationCancellationDisclaimer } from 'modules/applications/details/disclaimers/cancel';
import { ApplicationModificationDisclaimer } from 'modules/applications/details/disclaimers/modification';
import { OrderReference } from 'modules/applications/details/OrderReference';
import {
  ApplicationClientName,
  getCashier,
  getStatusUpdatedDate,
  getStatusWithBadge,
  StatusBadge,
} from 'modules/applications/shared';
import { useTranslation } from 'react-i18next';
import {
  ApplicationScheduleType,
  type MerchantApplicationDetailsQuery,
} from 'shared/api';
import { ApplicationDetailsDisclaimer } from 'shared/components/application/Disclaimer';
import { LocizeNamespaces } from 'shared/constants/localization-keys';
import { ApplicationRequestType } from 'shared/types/application';

type Props = NonNullOrUndefined<MerchantApplicationDetailsQuery['application']>;

export const ApplicationInfo = ({
  klix_payments,
  user_info,
  status,
  user_id,
  for_private_person,
  schedule_type,
  cancelled_at,
  cancellation_request,
  latest_modification_request,
  direct_payment_refunds,
  legal_person_info,
  simple_eligibility_status,
  credit_info,
  application_reference,
  id,
  merchant_data,
  requested_amount,
  from_retail,
  signed_at,
  rejected_at,
  created_at,
  processed_at,
  eligibility_state,
}: Props) => {
  const { t, i18n } = useTranslation(LocizeNamespaces.APPLICATIONS);
  const { applicationRequest, ibanConfig, isBonusShown } = useApplicationInfo({
    cancellation_request,
    latest_modification_request,
    direct_payment_refunds,
    credit_info,
    klix_payments,
    cancelled_at,
  });

  const handleIbanCopy = ibanConfig?.copy;

  const renderDisclaimer = () => {
    if (!applicationRequest) {
      return null;
    }

    if (applicationRequest.type === ApplicationRequestType.Cancellation) {
      return <ApplicationCancellationDisclaimer {...applicationRequest} />;
    }

    if (applicationRequest.type === ApplicationRequestType.AmountModification) {
      return <ApplicationModificationDisclaimer {...applicationRequest} />;
    }

    if (applicationRequest.type === ApplicationRequestType.RefundRequest) {
      return <ApplicationDetailsDisclaimer {...applicationRequest} />;
    }
  };

  return (
    <Box mb={8}>
      <OrderReference
        applicationId={id}
        initialValue={merchant_data?.reference}
      />
      {renderDisclaimer()}

      <Table mt={2} width="fit-content">
        <Tbody>
          <Tr>
            <ApplicationInfoRowTd>{t('table.status')}</ApplicationInfoRowTd>
            <ApplicationInfoRowTd>
              <StatusBadge
                left={-2}
                position="relative"
                status={getStatusWithBadge({
                  status,
                  schedule_type,
                  user_id,
                })}
                withTooltip
              />
            </ApplicationInfoRowTd>
          </Tr>
          <ApplicationInfoRow
            label={t('table.client')}
            value={
              <ApplicationClientName
                for_private_person={for_private_person}
                legal_person_info={legal_person_info}
                user_info={user_info}
              />
            }
          />
          {!!ibanConfig && (
            <ApplicationInfoRow
              label="IBAN"
              onClick={handleIbanCopy}
              value={ibanConfig.iban}
              withHoverPointer
            />
          )}
          <ApplicationInfoRow
            label={t('table.amount')}
            value={`${requested_amount.toLocaleString(i18n.language, {
              maximumFractionDigits: 2,
              minimumFractionDigits: 2,
            })}€`}
          />
          <ApplicationInfoRow
            label={t('table.method')}
            value={t(`schedule-types.${schedule_type}`)}
          />
          <ApplicationInfoRow
            label={t('table.status-updated')}
            value={getStatusUpdatedDate({
              signed_at,
              rejected_at,
              created_at,
              processed_at,
            })}
          />
          <ApplicationInfoRow
            label={t('table.cashier')}
            value={getCashier({
              application_reference,
            })}
          />
          <ApplicationInfoRow
            label={t('table.store')}
            value={
              from_retail
                ? (merchant_data?.store?.name ?? t('table.retail'))
                : t('table.online')
            }
          />
          {schedule_type !== ApplicationScheduleType.ESTO_PAY && (
            <ApplicationInfoRow
              label={t('eligibility-status')}
              value={
                <EligibilityStatusInfo
                  eligibilityState={eligibility_state}
                  simpleEligibilityStatus={simple_eligibility_status}
                />
              }
            />
          )}
          {!!isBonusShown && (
            <>
              <ApplicationInfoRow
                label={t('bonus-paid-by-esto')}
                value={`${credit_info?.merchant_bonus_amount.toLocaleString(
                  i18n.language,
                  {
                    maximumFractionDigits: 2,
                    minimumFractionDigits: 2,
                  },
                )}€`}
              />
              <ApplicationInfoRow
                label={t('bonus-type')}
                value={t(`bonus-types.${credit_info?.bonus_type}`)}
              />
            </>
          )}
        </Tbody>
      </Table>
    </Box>
  );
};
