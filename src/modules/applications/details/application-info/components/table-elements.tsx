import { type ChakraProps, Td, Tr } from '@chakra-ui/react';
import type { PropsWithChildren, ReactNode } from 'react';

export const ApplicationInfoRowTd = ({
  children,
  ...props
}: PropsWithChildren<ChakraProps>) => (
  <Td border="none" pl={0} pr={6} py={2} textStyle="body2" {...props}>
    {children}
  </Td>
);

export const ApplicationInfoRow = ({
  label,
  value,
  onClick,
  withHoverPointer = false,
}: {
  label: string;
  value: ReactNode;
  onClick?: () => void;
  withHoverPointer?: boolean;
}) => (
  <Tr cursor={withHoverPointer ? 'pointer' : undefined} onClick={onClick}>
    <ApplicationInfoRowTd>{label}</ApplicationInfoRowTd>
    <ApplicationInfoRowTd textStyle="body2-highlight">
      {value}
    </ApplicationInfoRowTd>
  </Tr>
);
