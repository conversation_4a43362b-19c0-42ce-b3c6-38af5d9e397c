import { SimpleEligibilityStatus } from 'shared/api';

/**
 * Represents the eligibility status of a UI element.
 * @enum {string}
 * @readonly
 */
export enum UiEligibilityStatus {
  POSITIVE = 'positive',
  NEGATIVE = 'negative',
  PENDING = 'pending',
}

/**
 * Tooltip text by eligibility status.
 * @type {Readonly<Record<string, string>>}
 */
export const EligibilityStatusTooltipText: Readonly<Record<string, string>> = {
  [SimpleEligibilityStatus.NO_USER]: 'No user yet',
  [SimpleEligibilityStatus.CONFIRM_BANK]: 'Waiting for account statement',
  [SimpleEligibilityStatus.CONFIRM_BANK_ACCOUNTSCORING]:
    'Waiting for account statement',
  [SimpleEligibilityStatus.SOME_REQUIRED_INFO_IS_MISSING]:
    'Some contact info is missing',
  [SimpleEligibilityStatus.WAITING_SPOUSE_CONSENT]: 'Waiting for spouse',
  [SimpleEligibilityStatus.EMTA_CONSENT]: 'Waiting for EMTA',
  [SimpleEligibilityStatus.OUTSIDE_WORKING_HOURS]: 'Outside of allowed hours',
  [SimpleEligibilityStatus.CHECK_INCOME]:
    'Manual verification: checking income',
  [SimpleEligibilityStatus.PENDING_CUSTOMER_CARE]:
    'Manual verification: waiting for customer support',
};

export const EligibilityState = {
  ELIGIBLE: 1,
  PENDING: 0,
  REJECTED: -1,
};
