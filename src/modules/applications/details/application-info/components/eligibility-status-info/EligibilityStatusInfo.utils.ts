import type { ComponentType } from 'react';
import { FiCheck, FiClock, FiX } from 'react-icons/fi';

import {
  EligibilityState,
  EligibilityStatusTooltipText,
  UiEligibilityStatus,
} from './EligibilityStatusInfo.constants';

type GetEligibilityStatusInfoReturnType = {
  color: string;
  icon: ComponentType;
  uiLabel: UiEligibilityStatus;
  tooltipText?: string;
};

/**
 * Returns the eligibility status information based on the provided eligibility state and simple eligibility status.
 *
 * @param {Object} eligibilityStatus - The eligibility status object.
 * @param {string} eligibilityStatus.simpleEligibilityStatus - The simple eligibility status.
 * @param {number} eligibilityStatus.eligibilityState - The eligibility state.
 * @returns {GetEligibilityStatusInfoReturnType|null} The eligibility status information object or null if no matching status is found.
 */
export const getEligibilityStatusInfo = ({
  eligibilityState,
  simpleEligibilityStatus,
}: {
  simpleEligibilityStatus: string;
  eligibilityState?: number | null;
}): GetEligibilityStatusInfoReturnType | null => {
  if (eligibilityState === EligibilityState.ELIGIBLE) {
    return {
      color: 'green.700',
      icon: FiCheck,
      uiLabel: UiEligibilityStatus.POSITIVE,
    };
  }

  if (eligibilityState === EligibilityState.REJECTED) {
    return {
      color: 'red.700',
      icon: FiX,
      uiLabel: UiEligibilityStatus.NEGATIVE,
    };
  }

  if (eligibilityState === EligibilityState.PENDING) {
    return {
      color: 'neutral.600',
      icon: FiClock,
      uiLabel: UiEligibilityStatus.PENDING,
      tooltipText: EligibilityStatusTooltipText[simpleEligibilityStatus],
    };
  }

  return null;
};
