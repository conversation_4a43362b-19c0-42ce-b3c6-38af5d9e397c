import { getPendingRefundRequest } from 'modules/applications/shared';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { LocizeNamespaces } from 'shared/constants/localization-keys';
import { useIsAllowedPermission } from 'shared/hooks/user';
import { useCopyToClipboard } from 'shared/hooks/utils';
import {
  ApplicationRequestType,
  type ApplicationType,
  MerchantPermissions,
} from 'shared/types';

type UseApplicationInfoParams = Pick<
  ApplicationType,
  | 'klix_payments'
  | 'cancellation_request'
  | 'direct_payment_refunds'
  | 'latest_modification_request'
  | 'credit_info'
  | 'cancelled_at'
>;

export const useApplicationInfo = ({
  klix_payments,
  cancellation_request,
  latest_modification_request,
  direct_payment_refunds,
  credit_info,
  cancelled_at,
}: UseApplicationInfoParams) => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);
  const copyToClipboard = useCopyToClipboard();
  const isAllowedPermission = useIsAllowedPermission([
    MerchantPermissions.Admin,
    MerchantPermissions.Accountant,
  ]);

  const ibanConfig = useMemo(() => {
    if (!!klix_payments?.length && klix_payments[0]?.payer_iban) {
      const iban = klix_payments[0]?.payer_iban;
      return {
        copy: () => {
          copyToClipboard(iban, t('iban-copied'));
        },
        iban,
      };
    }

    return null;
  }, [klix_payments, copyToClipboard, t]);

  const isBonusShown = useMemo(
    () =>
      isAllowedPermission &&
      !!credit_info &&
      credit_info.merchant_bonus_amount > 0,
    [credit_info, isAllowedPermission],
  );

  const applicationRequest = useMemo(() => {
    if (!cancelled_at && cancellation_request) {
      return {
        type: ApplicationRequestType.Cancellation,
        date: cancellation_request.created_at * 1000,
        requestId: cancellation_request.id,
        requestCreatorFullName:
          cancellation_request.merchant_creator?.name ||
          `${cancellation_request.creator?.profile?.first_name} ${cancellation_request.creator?.profile?.last_name}`.trim() ||
          undefined,
      };
    }

    if (latest_modification_request) {
      return {
        type: ApplicationRequestType.AmountModification,
        date: latest_modification_request.created_at * 1000,
        requestId: latest_modification_request.id,
        requestCreatorFullName:
          latest_modification_request.merchant_creator?.name ||
          `${latest_modification_request?.creator?.profile?.first_name} ${latest_modification_request?.creator?.profile?.last_name}`.trim() ||
          undefined,
      };
    }

    if (direct_payment_refunds?.length) {
      const pendingRefundRequest = getPendingRefundRequest(
        direct_payment_refunds,
      );
      const createdAt = pendingRefundRequest?.created_at;

      if (pendingRefundRequest && createdAt)
        return {
          type: ApplicationRequestType.RefundRequest,
          date: createdAt * 1000,
          requestId: -1,
        };
    }

    return null;
  }, [
    cancelled_at,
    cancellation_request,
    latest_modification_request,
    direct_payment_refunds,
  ]);

  return {
    ibanConfig,
    applicationRequest,
    isBonusShown,
  };
};
