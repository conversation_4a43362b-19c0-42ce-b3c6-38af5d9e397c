import { useCallback, useMemo } from 'react';
import type { FieldName, UseFormSetError } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  LocizeApplicationsKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { useShowMessage } from 'shared/hooks/alerts';
import { useModifyApplicationAmount } from 'shared/hooks/application';

import { AMOUNT_MODIFICATION_REASONS } from './AmountModificationModal.constants';
import type { AmountModificationSchemaType } from './AmountModificationModal.schema';

type UseApplicationModifyParams = {
  applicationId: number;
  setError: UseFormSetError<AmountModificationSchemaType>;
  onSuccess: () => void;
};

/**
 * Custom hook for modifying application amount.
 *
 * @param {UseApplicationModifyParams} params - The parameters for modifying the application amount.
 * @returns {Object} - An object containing the `isSubmitting` flag and the `onSubmit` function.
 */
export const useApplicationModify = ({
  applicationId,
  setError,
  onSuccess,
}: UseApplicationModifyParams) => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);
  const showMessage = useShowMessage();

  const onFieldValidationError = useCallback(
    (field: string) => {
      setError(
        field as FieldName<AmountModificationSchemaType>,
        { message: 'invalid-format' },
        { shouldFocus: true },
      );
    },
    [setError],
  );

  const { isLoading: isSubmitting, modifyApplicationAmount } =
    useModifyApplicationAmount({ onFieldValidationError });

  const onSubmit = useCallback(
    async ({
      newRequestedAmount,
      reason,
      otherReason,
    }: AmountModificationSchemaType) => {
      const result = await modifyApplicationAmount({
        applicationId,
        newRequestedAmount,
        reason:
          reason ===
          LocizeApplicationsKeys.AMOUNT_MODIFICATION_MODAL_REASONS_OTHER
            ? otherReason
            : t(reason),
      });

      if (!result) {
        return;
      }

      showMessage(t(LocizeApplicationsKeys.NOTIFICATIONS_SUCCESSFUL_REQUEST));
      onSuccess();
    },
    [modifyApplicationAmount, applicationId, t, showMessage, onSuccess],
  );

  return { isSubmitting, onSubmit };
};

/**
 * Custom hook that returns the options for amount modification reasons.
 * Uses the `useTranslation` hook from the `LocizeNamespaces.APPLICATIONS` namespace to translate the reason values.
 *
 * @returns An array of objects containing the label and value for each reason option.
 */
export const useAmountModificationReasonOptions = () => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);

  return useMemo(
    () =>
      AMOUNT_MODIFICATION_REASONS.map((value) => ({ label: t(value), value })),
    [t],
  );
};
