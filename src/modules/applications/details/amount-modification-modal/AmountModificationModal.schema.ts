import { LocizeApplicationsKeys } from 'shared/constants/localization-keys';
import { z } from 'zod';

export const AmountModificationSchema = ({
  newRequestedAmountMaxLength,
  newRequestedAmountMinLength,
  initialAmount,
}: {
  newRequestedAmountMinLength: number;
  newRequestedAmountMaxLength: number;
  initialAmount: number;
}) =>
  z
    .object({
      newRequestedAmount: z
        .number()
        .min(newRequestedAmountMinLength, 'required')
        .max(newRequestedAmountMaxLength, 'max-value')
        .refine((value) => value !== initialAmount),
      reason: z.string().min(1, 'required'),
      otherReason: z.string(),
    })
    .refine(
      ({ reason, otherReason }) =>
        reason !==
          LocizeApplicationsKeys.AMOUNT_MODIFICATION_MODAL_REASONS_OTHER ||
        (reason ===
          LocizeApplicationsKeys.AMOUNT_MODIFICATION_MODAL_REASONS_OTHER &&
          !!otherReason),
      {
        message: 'required',
        path: ['otherReason'],
      },
    );

export type AmountModificationSchemaType = z.infer<
  ReturnType<typeof AmountModificationSchema>
>;
