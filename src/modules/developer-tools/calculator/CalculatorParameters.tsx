import { Box, Flex, Text } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  ControlledToggle,
  NumericNumberInput,
  ToggleItem,
} from 'shared/components';
import { availableLanguages } from 'shared/lib';
import {
  type CalculatorForm,
  CalculatorFormSchema,
  CalculatorTypes,
  ExtendLanguageAliases,
} from 'shared/types/calculator';

import { CalculatorInfoPopover } from './CalculatorInfoPopover';
import CloseIcon from './icons/close.svg';

type Props = {
  onCalculatorFormSet: (data: CalculatorForm) => void;
  calculatorForm: CalculatorForm;
};

export const CalculatorParameters = ({
  calculatorForm,
  onCalculatorFormSet,
}: Props) => {
  const { t } = useTranslation(['dev-tools', 'common']);

  const { getValues, watch, control, register } = useForm<CalculatorForm>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: { ...calculatorForm },
    resolver: zodResolver(CalculatorFormSchema),
  });

  const [type, language, amount, width, height] = watch([
    'type',
    'language',
    'amount',
    'width',
    'height',
  ]);

  useEffect(() => {
    onCalculatorFormSet({ ...getValues() });
  }, [onCalculatorFormSet, type, language, amount, width, height, getValues]);

  return (
    <Box data-cy="calculator-parameters-block">
      <Text mb={[3, 3]} textStyle="h4">
        {t('calculator.parameters-title')}
      </Text>

      <Box
        bg={['transparent', 'neutral.50']}
        borderRadius={[0, '4px']}
        pb={[0, 2.5]}
        pt={[0, 5]}
        px={[0, 6]}
        w={['100%', '408px']}
      >
        <CalculatorInfoPopover />

        <ControlledToggle
          control={control}
          isDisabled={false}
          mb={6}
          name="type"
          w="100%"
        >
          <ToggleItem
            colorScheme={ColorSchemes.PRIMARY}
            dataCy="banner-view-only-type"
            value={CalculatorTypes.ViewOnly}
          >
            {t('calculator.form-view-only')}
          </ToggleItem>

          <ToggleItem
            colorScheme={ColorSchemes.PRIMARY}
            dataCy="banner-with-button-type"
            value={CalculatorTypes.WithButton}
          >
            {t('calculator.form-with-button')}
          </ToggleItem>
        </ControlledToggle>

        <Text display="flex" mb={2} p={0} textStyle="calculator1">
          {t('calculator.parameters-language')}
        </Text>

        <ControlledToggle
          control={control}
          isDisabled={false}
          mb={6}
          name="language"
          w="100%"
        >
          {availableLanguages.map((lang) => {
            return (
              <ToggleItem
                colorScheme={ColorSchemes.PRIMARY}
                dataCy={`chosen-language-${lang}`}
                key={lang}
                value={ExtendLanguageAliases[lang]}
              >
                {t(`common:language.${lang}`)}
              </ToggleItem>
            );
          })}
        </ControlledToggle>

        <Text mb={1} textStyle="calculator1">
          {t('calculator.parameters-amount')}
        </Text>

        <Controller
          control={control}
          name="amount"
          render={({ field: { onChange, ...rest } }) => (
            <NumericNumberInput
              data-cy="calculator-default-amount"
              onValueChange={({ floatValue }) => {
                onChange(floatValue);
              }}
              {...rest}
            />
          )}
        />

        <Flex alignItems="center" justifyContent="space-between">
          <Box w={['152px', '164px']}>
            <Text mb={1} textStyle="calculator1">
              {t('calculator.parameters-width')}
            </Text>

            <NumericNumberInput
              data-cy="calculator-width"
              defaultValue={calculatorForm.width}
              suffix="px"
              {...register('width')}
            />
          </Box>

          <img alt="x-icon" src={CloseIcon} />

          <Box w={['152px', '164px']}>
            <Text mb={1} textStyle="calculator1">
              {t('calculator.parameters-height')}
            </Text>
            <NumericNumberInput
              data-cy="calculator-height"
              defaultValue={calculatorForm.height}
              suffix="px"
              {...register('height')}
            />
          </Box>
        </Flex>
      </Box>
    </Box>
  );
};
