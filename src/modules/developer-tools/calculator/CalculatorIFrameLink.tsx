import { Box, Text } from '@chakra-ui/react';
import copy from 'copy-to-clipboard';
import { type FC, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { FiCopy } from 'react-icons/fi';

import { useShowMessage } from '../../../shared/hooks/alerts';
import { useIsMobileLayout } from '../../../shared/hooks/layout';
import IFrameIcon from './icons/i-frame.svg?react';
import LinkIcon from './icons/link.svg?react';

type IFrameLinkProps = {
  htmlString: string;
  iFrameUrl: string;
  changedValues: Array<string>;
};

export const CalculatorIFrameLink: FC<IFrameLinkProps> = (props) => {
  const { t } = useTranslation('dev-tools');
  const isMobileLayout = useIsMobileLayout();
  const { htmlString, iFrameUrl, changedValues } = props;

  const showMessage = useShowMessage();

  const onCopy = useCallback(
    async (data: string) => {
      copy(data);
      showMessage(
        t(
          data === htmlString
            ? 'calculator.notification-iframe-copied'
            : 'calculator.notification-link-copied',
        ),
        FiCopy,
      );
    },
    [htmlString, showMessage, t],
  );

  const notification = changedValues.reduce((acc, rec, index) => {
    const end =
      changedValues.length > 1
        ? 'calculator.notification-were-modified'
        : 'calculator.notification-was-modified';
    if (changedValues.length === 1) {
      return `${t(rec)} ${t(end)}`;
    }
    if (index === changedValues.length - 1 && changedValues.length === 2) {
      return `${acc.trim()} ${t('calculator.notification-and')} ${t(
        rec,
      ).trim()} ${t(end)}`;
    }
    if (index === changedValues.length - 1 && changedValues.length > 2) {
      return `${acc.trim()} ${t('calculator.notification-and')} ${t(rec)} ${t(
        end,
      )}`;
    }
    if (index === 0) {
      return `${t(rec)}`;
    }

    return `${acc.trim()}, ${t(rec)}`;
  }, '');

  return (
    <Box data-cy="calculator-iframe-block">
      <Box alignItems="baseline" display="flex">
        <Text mr="24px" textStyle="h4">
          {t('calculator.iframe-link-title')}
        </Text>
        {!isMobileLayout && (
          <>
            <Text
              color="primary.800"
              cursor="pointer"
              data-cy="calculator-copy-iframe"
              fontSize="14px"
              fontWeight={700}
              lineHeight="20px"
              mr="32px"
              onClick={async () => {
                await onCopy(htmlString);
              }}
            >
              {t('calculator.copy-iframe')}
            </Text>
            <Text
              color="primary.800"
              cursor="pointer"
              data-cy="calculator-copy-link"
              fontSize="14px"
              fontWeight={700}
              lineHeight="20px"
              onClick={async () => {
                await onCopy(iFrameUrl);
              }}
            >
              {t('calculator.copy-link')}
            </Text>
          </>
        )}
      </Box>

      <Box
        bg="neutral.50"
        borderRadius="4px"
        fontSize="14px"
        lineHeight="20px"
        mt={['14px', '12px']}
        p="16px 20px"
        w="100%"
      >
        {htmlString}
      </Box>

      {changedValues.length > 0 && (
        <Box
          bg="primary.100"
          borderRadius="4px"
          color="primary.900"
          data-cy="notification-changing-parameters"
          fontSize="14px"
          lineHeight="20px"
          mb="28px"
          mt={['14px', '12px']}
          p="8px 12px"
          w="100%"
        >
          <Text data-cy="notification-text">{notification}</Text>
        </Box>
      )}

      {!!isMobileLayout && (
        <>
          <Box
            cursor="pointer"
            display="flex"
            mb="24px"
            mt="20px"
            onClick={async () => {
              await onCopy(htmlString);
            }}
          >
            <IFrameIcon />
            <Text
              color="primary.800"
              fontSize="16px"
              fontWeight={700}
              lineHeight="20px"
              ml="8px"
            >
              {t('calculator.copy-iframe')}
            </Text>
          </Box>
          <Box
            cursor="pointer"
            display="flex"
            onClick={async () => {
              await onCopy(iFrameUrl);
            }}
          >
            <LinkIcon />
            <Text
              color="primary.800"
              fontSize="16px"
              fontWeight={700}
              lineHeight="20px"
              ml="8px"
            >
              {t('calculator.copy-link')}
            </Text>
          </Box>
        </>
      )}
    </Box>
  );
};
