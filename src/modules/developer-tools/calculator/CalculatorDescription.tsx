import { Box, Text } from '@chakra-ui/react';
import type { FC } from 'react';
import { Trans, useTranslation } from 'react-i18next';

export const CalculatorDescription: FC = () => {
  const { t } = useTranslation('dev-tools');

  return (
    <Box mb={[8, null, 14]}>
      <Text mb={[3, 3]} textStyle="h4">
        {t('calculator.title')}
      </Text>

      <Box mb={3}>
        <Text as="span" textStyle="body2">
          <Trans
            components={{ bold: <strong /> }}
            i18nKey="calculator.description-1"
            ns="dev-tools"
          />
        </Text>
      </Box>

      <Box>
        <Text as="span" textStyle="body2">
          <Trans
            components={{ bold: <strong /> }}
            i18nKey="calculator.description-2"
            ns="dev-tools"
          />
        </Text>
      </Box>
    </Box>
  );
};
