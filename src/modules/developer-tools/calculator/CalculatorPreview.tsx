import { Box, Text } from '@chakra-ui/react';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { useIsMobileLayout } from '../../../shared/hooks/layout';

type PreviewProps = {
  iFrameUrl: string;
  width: number;
  height: number;
};

export const CalculatorPreview: FC<PreviewProps> = (props) => {
  const { t } = useTranslation('dev-tools');
  const { iFrameUrl, width, height } = props;
  const isMobileLayout = useIsMobileLayout();

  return (
    <Box data-cy="calculator-preview-block" mb="56px">
      <Text mb={[3, 3]} textStyle="h4">
        {t('calculator.preview-title')}
      </Text>
      <iframe
        data-cy="calculator-iframe"
        height={height}
        id="calc-iframe"
        src={iFrameUrl}
        style={{
          borderRadius: '4px',
          maxWidth: isMobileLayout ? '100%' : width,
        }}
        title="preview"
        width={width}
      />
    </Box>
  );
};
