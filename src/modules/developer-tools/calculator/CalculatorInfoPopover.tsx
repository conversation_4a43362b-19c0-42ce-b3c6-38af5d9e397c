import { Box, Text } from '@chakra-ui/react';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { AppTooltip } from 'shared/components';

export const CalculatorInfoPopover: FC = () => {
  const { t } = useTranslation('dev-tools');

  return (
    <Box alignItems="center" display="flex" mb={2}>
      <Text as="span" display="flex" p={0} textStyle="calculator1">
        {t('calculator.parameters-type')}
      </Text>
      <AppTooltip isInfo label={t('calculator.parameters-type-info')} ml={1} />
    </Box>
  );
};
