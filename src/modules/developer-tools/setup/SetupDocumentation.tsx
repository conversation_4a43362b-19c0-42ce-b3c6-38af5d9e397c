import {
  Box,
  Icon,
  Icon<PERSON>utton,
  <PERSON>,
  List,
  ListItem,
  Text,
} from '@chakra-ui/react';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { CgSoftwareDownload } from 'react-icons/cg';

import <PERSON><PERSON><PERSON><PERSON><PERSON> from './logos/joomla.svg?react';
import MagentoLogo from './logos/magento.svg?react';
import OpencartLogo from './logos/opencart.svg?react';
import PrestashopLogo from './logos/prestashop.svg?react';
import WooCommerceLogo from './logos/woo-commerce.svg?react';

const ESTO_DOCS_LINK = 'https://esto.docs.apiary.io';

enum LogoType {
  WooCommerce = 'woo-commerce',
  Opencart = 'open-cart',
  Prestashop = 'prestashop',
  Magento = 'magento',
  Joomla = 'joomla',
}

const LogoComponent = {
  [LogoType.WooCommerce]: WooCommerceLogo,
  [LogoType.Opencart]: OpencartLogo,
  [LogoType.Prestashop]: PrestashopLogo,
  [LogoType.Magento]: MagentoLogo,
  [LogoType.Joomla]: JoomlaLogo,
};

const Logo: FC<{ logoType: LogoType }> = ({ logoType }) => {
  const Component = LogoComponent[logoType];

  return <Component />;
};

const shopIntegrations = Object.freeze([
  {
    id: 1,
    logo: LogoType.WooCommerce,
    link: 'woocommerce-esto.zip',
  },
  {
    id: 2,
    logo: LogoType.Opencart,
    link: 'opencart-esto-v1.ocmod.zip',
  },
  { id: 3, logo: LogoType.Opencart, link: 'opencart-esto-v2.3.ocmod.zip' },
  { id: 4, logo: LogoType.Opencart, link: 'opencart-esto-v3.ocmod.zip' },
  { id: 5, logo: LogoType.Prestashop, link: 'esto-prestashop-1.6.zip' },
  { id: 6, logo: LogoType.Prestashop, link: 'esto-prestashop-1.7.zip' },
  { id: 10, logo: LogoType.Prestashop, link: 'prestashop8/esto.zip' },
  { id: 7, logo: LogoType.Magento, link: 'esto-magento-v1.zip' },
  { id: 8, logo: LogoType.Magento, link: 'esto-magento-v2.zip' },
  { id: 9, logo: LogoType.Joomla, link: 'esto-joomla.zip' },
]);

export const SetupDocumentation: FC = () => {
  const { t } = useTranslation('dev-tools');

  return (
    <Box mb={[12, 16]}>
      <Box mb={[4]}>
        <Text mb={[3, 2]} textStyle="h4">
          {t('setup-doc.title')}
        </Text>
        <Text textStyle="body1">
          {t('setup-doc.piece-1')}{' '}
          <Link
            color="primary.800"
            href={ESTO_DOCS_LINK}
            target="_blank"
            textStyle="body1-highlight"
          >
            {t('setup-doc.piece-2')}
          </Link>{' '}
          {t('setup-doc.piece-3')}
        </Text>
      </Box>

      <List border="1px solid" borderColor="neutral.100" borderRadius="8px">
        {shopIntegrations.map(({ id, logo, link }) => (
          <ListItem
            alignItems="center"
            borderBottom={`${
              id !== shopIntegrations.length ? '1px' : '0'
            } solid`}
            borderColor="neutral.100"
            display="flex"
            key={id}
            px={4}
            py={2.5}
          >
            <Logo logoType={logo} />
            <Text color="neutral.900" mx={3} textStyle="body1-highlight">
              {t(`setup-doc.shop-integr-${id}`)}
            </Text>
            <IconButton
              aria-label="download"
              as="a"
              download
              href={`https://s3.${process.env.REACT_APP_AWS_REGION}.amazonaws.com/esto-public/integrations/${link}`}
              icon={
                <Icon as={CgSoftwareDownload} boxSize={6} color="primary.800" />
              }
              isRound
              ml="auto"
              variant="ghost"
            />
          </ListItem>
        ))}
      </List>
    </Box>
  );
};
