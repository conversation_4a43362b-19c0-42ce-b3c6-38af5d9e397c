import { Box, Button, Flex, Text } from '@chakra-ui/react';
import { type FC, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { LoginRoute } from 'shared/constants/routes';
import { LoginMethods } from 'shared/types';

export const ResetPasswordSecondStepMsg: FC = () => {
  const { t } = useTranslation('reset-password');
  const navigate = useNavigate();
  const handleGoToLoginPasswordPage = useCallback(() => {
    navigate(LoginRoute.format({ method: LoginMethods.Password }), {
      replace: true,
    });
  }, [navigate]);

  return (
    <Flex alignItems={['center', 'flex-start']} height="100%">
      <Box flexGrow={1} pb={[null, 12]} pt={[null, '150px']}>
        <Text mb={[5, 8]} textStyle="h2">
          {t('second-step-msg.title')}
        </Text>
        <Button
          colorScheme={ColorSchemes.PRIMARY}
          data-cy="redirect-to-login-link"
          onClick={handleGoToLoginPasswordPage}
          width="full"
        >
          {t('second-step-msg.link')}
        </Button>
      </Box>
    </Flex>
  );
};
