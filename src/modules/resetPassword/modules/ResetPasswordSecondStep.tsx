import { Box, Button, Text } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { type FC, useCallback, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { PasswordInput, PasswordStrengthWidget } from 'shared/components';
import { useShowError } from 'shared/hooks/alerts';
import { MIN_PASSWORD_LENGTH } from 'shared/utils';
import { z } from 'zod';

import { useResetPasswordMutation } from '../api';
import { ResetPasswordSecondStepMsg } from './ResetPasswordSecondStepMsg';

const ResetPasswordSchema = z.object({
  newPassword: z.string().min(MIN_PASSWORD_LENGTH, 'min-length'),
});

export type ResetPasswordSchemaType = z.infer<typeof ResetPasswordSchema>;

export const ResetPasswordSecondStep: FC = () => {
  const { token } = useParams();

  if (!token) {
    throw new Error('Token is absent');
  }
  const { t } = useTranslation(['reset-password', 'common']);
  const showError = useShowError();

  const [isPasswordReseted, setPasswordReseted] = useState(false);

  const {
    control,
    formState: { errors },
    handleSubmit,
    watch,
  } = useForm<ResetPasswordSchemaType>({
    resolver: zodResolver(ResetPasswordSchema),
  });

  const newPassword = watch('newPassword');

  const passwordError =
    errors.newPassword?.message &&
    (errors.newPassword.message === 'min-length'
      ? t('second-step.password-hint', { length: MIN_PASSWORD_LENGTH })
      : t(`common:forms.${errors.newPassword.message}`));

  const [resetPassword, { loading }] = useResetPasswordMutation();

  const onSubmit = useCallback(
    async ({ newPassword }: ResetPasswordSchemaType) => {
      try {
        await resetPassword({
          variables: {
            newPassword,
            confirmPassword: newPassword,
            token,
          },
        });

        setPasswordReseted(true);
      } catch {
        showError("Can't perform password reset. Please, try again later.");
      }
    },
    [resetPassword, showError, token],
  );

  return isPasswordReseted ? (
    <ResetPasswordSecondStepMsg />
  ) : (
    <Box pb={12} pt={[6, '150px']}>
      <Text mb={[5, 8]} textStyle="h2">
        {t('reset-password:second-step.title')}
      </Text>

      <Box as="form" onSubmit={handleSubmit(onSubmit)}>
        <Controller
          control={control}
          name="newPassword"
          render={({ field: { onChange, ...rest } }) => (
            <PasswordInput
              autoComplete="none"
              data-cy="new-password-input"
              error={passwordError}
              hint={t('reset-password:second-step.password-hint', {
                length: MIN_PASSWORD_LENGTH,
              })}
              isDisabled={loading}
              label={t('reset-password:second-step.label')}
              mb={[4, 5]}
              onChange={(e) => {
                onChange(e.target.value.trim());
              }}
              {...rest}
            />
          )}
        />

        <PasswordStrengthWidget mb={10} password={newPassword} />
        <Button
          colorScheme={ColorSchemes.PRIMARY}
          data-cy="new-password-sbmt"
          isLoading={loading}
          type="submit"
          width="full"
        >
          {t('reset-password:second-step.submit')}
        </Button>
      </Box>
    </Box>
  );
};
