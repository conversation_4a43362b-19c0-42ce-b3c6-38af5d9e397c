import { useCallback } from 'react';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { useCurrentUser } from 'shared/hooks/user';

import {
  type UpdatePasswordMutationVariables,
  useUpdatePasswordMutation,
} from './api';

export const useUpdateUserPassword = () => {
  const { user } = useCurrentUser();

  const [updatePasswordMutation, { loading }] = useUpdatePasswordMutation();
  const handleGenericError = useHandleGenericError();

  const updatePassword = useCallback(
    async ({
      newPassword,
      oldPassword,
    }: Omit<UpdatePasswordMutationVariables, 'userId'>) => {
      try {
        return await updatePasswordMutation({
          variables: {
            newPassword,
            oldPassword,
            userId: user?.id || 0,
          },
          update(cache) {
            cache.modify({
              id: cache.identify({ id: user?.id, __typename: 'User' }),
              fields: {
                is_password_set() {
                  return true;
                },
              },
            });
          },
        });
      } catch (e) {
        handleGenericError(e);
        return null;
      }
    },
    [handleGenericError, updatePasswordMutation, user?.id],
  );

  return { isLoading: loading, updatePassword };
};
