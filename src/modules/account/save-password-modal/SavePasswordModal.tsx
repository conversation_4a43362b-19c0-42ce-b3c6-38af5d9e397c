import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalOverlay,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FiUser } from 'react-icons/fi';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  ButtonWithLoader,
  ModalCloseButton,
  PasswordInput,
  PasswordStrengthWidget,
} from 'shared/components';
import { useShowError, useShowMessage } from 'shared/hooks/alerts';
import { MIN_PASSWORD_LENGTH } from 'shared/utils';
import { z } from 'zod';

import { useUpdateUserPassword } from './hooks';

enum PasswordUpdateMode {
  UPDATE = 'change',
  CREATE = 'create',
}

const PasswordUpdateSchema = z.object({
  oldPassword: z.string().min(1, 'required'),
  newPassword: z.string().min(MIN_PASSWORD_LENGTH, 'min-length'),
});

type PasswordUpdateSchemaType = z.infer<typeof PasswordUpdateSchema>;

type Props = {
  isOpen: boolean;
  hasPassword: boolean;
  onClose: () => void;
};

export const SavePasswordModal = ({ isOpen, onClose, hasPassword }: Props) => {
  const mode = hasPassword
    ? PasswordUpdateMode.UPDATE
    : PasswordUpdateMode.CREATE;
  const { t } = useTranslation(['account', 'common']);
  const showMessage = useShowMessage();
  const showError = useShowError();
  const {
    control,
    formState: { errors, isValid },
    handleSubmit,
    watch,
  } = useForm<PasswordUpdateSchemaType>({
    resolver: zodResolver(PasswordUpdateSchema),
  });
  const newPassword = watch('newPassword');

  const { isLoading, updatePassword } = useUpdateUserPassword();

  const onSubmit = useCallback(
    async (formValues: PasswordUpdateSchemaType) => {
      try {
        await updatePassword(formValues);

        onClose();
        showMessage(t('notifications.password-saved'), FiUser);
      } catch {
        showError("Can't perform update password. Please, try again later.");
      }
    },
    [updatePassword, onClose, showMessage, t, showError],
  );

  const newPasswordError =
    errors.newPassword?.message &&
    (errors.newPassword.message === 'min-length'
      ? t('password.password-hint', { length: MIN_PASSWORD_LENGTH })
      : t(`common:forms.${errors.newPassword.message}`));

  return (
    <Modal isOpen={isOpen} onClose={onClose} scrollBehavior="inside">
      <ModalOverlay display={['none', 'flex']} />
      <ModalContent
        as="form"
        data-cy={`${mode}-password-modal-content`}
        maxW="29rem"
        onSubmit={handleSubmit(onSubmit)}
      >
        <ModalHeader>{t(`password.modal.${mode}.title`)}</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={[4, 1]}>
          {mode === PasswordUpdateMode.UPDATE && (
            <Controller
              control={control}
              name="oldPassword"
              render={({ field: { onChange, ...rest } }) => (
                <PasswordInput
                  autoComplete="none"
                  autoFocus
                  data-cy="change-old-password-input"
                  error={
                    errors.oldPassword?.message
                      ? t(`common:forms.${errors.oldPassword.message}`)
                      : undefined
                  }
                  isDisabled={isLoading}
                  label={t('password.modal.change.old-password')}
                  mb={3}
                  onChange={(e) => {
                    onChange(e.target.value.trim());
                  }}
                  {...rest}
                />
              )}
            />
          )}
          <Controller
            control={control}
            name="newPassword"
            render={({ field: { onChange, ...rest } }) => (
              <PasswordInput
                autoComplete="none"
                autoFocus={mode === PasswordUpdateMode.CREATE}
                data-cy={`${mode}-new-password-input`}
                error={newPasswordError}
                hint={t('password.password-hint', {
                  length: MIN_PASSWORD_LENGTH,
                })}
                isDisabled={isLoading}
                label={
                  mode === 'change'
                    ? t('password.modal.change.new-password')
                    : undefined
                }
                mb={[5, 8]}
                onChange={(e) => {
                  onChange(e.target.value.trim());
                }}
                {...rest}
              />
            )}
          />

          <PasswordStrengthWidget mb={4} password={newPassword} />
        </ModalBody>

        <ModalFooter
          borderTop={['1px solid', 'none']}
          borderTopColor="neutral.150"
        >
          <Button
            colorScheme={ColorSchemes.SECONDARY}
            display={['none', 'inline-flex']}
            isDisabled={isLoading}
            mr={[0, 3]}
            mt={[3, 0]}
            onClick={onClose}
          >
            {t('common:forms.cancel')}
          </Button>

          <ButtonWithLoader
            data-cy={`${mode}-password-modal-submit`}
            isDisabled={isLoading || !isValid}
            isLoading={isLoading}
            type="submit"
          >
            {t(`password.modal.${mode}.submit`)}
          </ButtonWithLoader>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
