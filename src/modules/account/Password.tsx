import { Box, Button, Text, useDisclosure } from '@chakra-ui/react';
import { SavePasswordModal } from 'modules/account/save-password-modal';
import { useTranslation } from 'react-i18next';

type Props = {
  hasPassword: boolean;
};

export const Password = ({ hasPassword }: Props) => {
  const { t } = useTranslation('account');
  const { isOpen, onOpen, onClose } = useDisclosure();

  return (
    <Box data-cy="account-password" mb={[10, 14]}>
      <Text textStyle="h4">{t('password.title')}</Text>
      <Text
        data-cy="account-password-create-disclaimer"
        mt={2}
        textStyle="body2"
      >
        {t('password.create-disclaimer')}
      </Text>
      <Button
        data-cy="account-password-open-modal-btn"
        mt={4}
        onClick={onOpen}
        width="full"
      >
        {t('password.change-btn')}
      </Button>
      {!!isOpen && (
        <SavePasswordModal hasPassword={hasPassword} isOpen onClose={onClose} />
      )}
    </Box>
  );
};
