import { Box, type ChakraProps, HStack, Text } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useMemo, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TextInput } from 'shared/components';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { useCurrentUser } from 'shared/hooks/user';
import { useDebouncedValue } from 'shared/hooks/utils';
import { z } from 'zod';

import { useUpdateUserMutation } from './api';

const PersonalInformationSchema = z.object({
  lastName: z.string().min(1, 'required'),
  firstName: z.string().min(1, 'required'),
});

type PersonalInformationSchemaType = z.infer<typeof PersonalInformationSchema>;

type PersonalInformationForm = {
  firstName?: string;
  lastName?: string;
};

type PersonalInformationProps = {
  isDisabled?: boolean;
  defaultValues: PersonalInformationForm;
} & ChakraProps;

export const PersonalInformation = ({
  defaultValues,
  isDisabled,
  ...chakraProps
}: PersonalInformationProps) => {
  const handleGenericError = useHandleGenericError();
  const isInitialSkipped = useRef(false);
  const { t } = useTranslation('account');
  const {
    register,
    watch,
    formState: { errors },
  } = useForm<PersonalInformationSchemaType>({
    defaultValues,
    resolver: zodResolver(PersonalInformationSchema),
  });
  const { firstName, lastName } = watch();

  const formValues = useDebouncedValue(
    useMemo(() => ({ firstName, lastName }), [firstName, lastName]),
    500,
  );
  const { user } = useCurrentUser();
  const [updateUser] = useUpdateUserMutation({
    onError: (error) => {
      handleGenericError(error);
    },
  });

  useEffect(() => {
    if (!isInitialSkipped.current) {
      isInitialSkipped.current = true;
      return;
    }

    if (user?.id) {
      void updateUser({
        variables: {
          userId: user.id,
          ...formValues,
        },
      });
    }
  }, [formValues]);

  return (
    <Box data-cy="account-personal-information" {...chakraProps}>
      <Text mb={4} textStyle="h4">
        {t('personal-information.title')}
      </Text>
      <HStack as="form" spacing={3}>
        <TextInput
          autoComplete="firstName"
          data-cy="account-first-name"
          // @ts-expect-error: zodResolver is not typed
          error={t(errors.firstName)}
          isDisabled={isDisabled}
          label={t('personal-information.first-name-label')}
          {...register('firstName')}
        />
        <TextInput
          autoComplete="lastName"
          data-cy="account-last-name"
          label={t('personal-information.last-name-label')}
          {...register('lastName')}
          isDisabled={isDisabled}
        />
      </HStack>
    </Box>
  );
};
