import { Box, Button, Text } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';

export const ConnectId = () => {
  const { t } = useTranslation('account');

  return (
    <Box data-cy="account-connect-id" mb={[10, 14]}>
      <Text textStyle="h4">{t('connect-id.title')}</Text>
      <Text mt={2} textStyle="body2">
        {t('connect-id.disclaimer')}
      </Text>
      <Button mt={4} width="full">
        {t('connect-id.button')}
      </Button>
    </Box>
  );
};
