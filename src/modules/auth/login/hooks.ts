import { useApolloClient } from '@apollo/client';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { UserDataDocument, type UserDataQuery } from 'shared/api';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { MERCHANT_INVITE_HASH_KEY, setItem } from 'shared/lib';
import { getLangFromAbbr } from 'shared/utils';

import { useAttachUserByInviteHashMutation } from './api';

export function useAttachUserByInviteHash() {
  const handleGenericError = useHandleGenericError();
  const [attachUserByInviteHash, { loading }] =
    useAttachUserByInviteHashMutation({
      // to update user's data
      refetchQueries: [{ query: UserDataDocument }],
      awaitRefetchQueries: true,
      onError: (error) => {
        handleGenericError(error);
      },
    });
  return { loading, attachUserByInviteHash };
}

type UseLoginSuccessParams = {
  redirectUrl: string;
  inviteHash: string | null;
};

export const useLoginSuccess = ({
  redirectUrl,
  inviteHash,
}: UseLoginSuccessParams) => {
  const navigate = useNavigate();

  const apollo = useApolloClient();
  const handleGenericError = useHandleGenericError();
  const { attachUserByInviteHash } = useAttachUserByInviteHash();
  const { i18n } = useTranslation('login');

  const [isAfterLoginSetupInProgress, setIsAfterLoginSetupInProgress] =
    useState(false);

  const onLoginSuccess = useCallback(async () => {
    setIsAfterLoginSetupInProgress(true);
    await apollo.resetStore();
    // prefetch current user
    try {
      const {
        data: { me },
      } = await apollo.query<UserDataQuery>({ query: UserDataDocument });
      const lang = me && getLangFromAbbr(me.language_abbr);
      if (lang) {
        await i18n.changeLanguage(lang);
      }
      // if user visit login page from 'merchant-invite' page then connect user to the merchant using invite hash
      if (me?.id && inviteHash)
        attachUserByInviteHash({
          variables: { userId: me?.id, inviteHash },
        });
      setItem(MERCHANT_INVITE_HASH_KEY, null);
      setIsAfterLoginSetupInProgress(false);
      navigate(redirectUrl);
    } catch (err) {
      handleGenericError(err);
      setIsAfterLoginSetupInProgress(false);
    }
  }, [
    apollo,
    inviteHash,
    attachUserByInviteHash,
    navigate,
    redirectUrl,
    i18n,
    handleGenericError,
  ]);

  return { isAfterLoginSetupInProgress, onLoginSuccess };
};
