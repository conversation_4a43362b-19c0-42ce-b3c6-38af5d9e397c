import {
  type ApolloError,
  type DocumentNode,
  useMutation,
  useQuery,
} from '@apollo/client';
import { useCallback, useEffect, useState } from 'react';
import {
  getItem,
  LOGIN_SESSION_STORAGE_KEY,
  LOGIN_SESSION_STORAGE_TIMEOUT,
  setItem,
} from 'shared/lib';
import type { LoginSession } from 'shared/types';

import type { LoginHookParams } from './types';

const useLoginSession = () => {
  const [session, _setSession] = useState<LoginSession | null>(
    getItem(LOGIN_SESSION_STORAGE_KEY),
  );

  const setSession = useCallback((session: LoginSession | null) => {
    if (session) {
      setItem(LOGIN_SESSION_STORAGE_KEY, session);
    } else {
      setItem(LOGIN_SESSION_STORAGE_KEY, null);
    }
    _setSession(session);
  }, []);

  useEffect(() => {
    if (session) {
      const sessionLifetime = Date.now() - session.time;

      // if session expired - clear it
      if (sessionLifetime > LOGIN_SESSION_STORAGE_TIMEOUT) {
        setSession(null);
      }
      // set timeout otherwise
      else {
        const timer = setTimeout(() => {
          setSession(null);
        }, LOGIN_SESSION_STORAGE_TIMEOUT - sessionLifetime);
        return () => {
          clearTimeout(timer);
        };
      }
    }
  }, [session, setSession]);

  return {
    session,
    setSession,
  };
};

type LoginChallengeData = {
  challenge: {
    challenge_id: string | null;
    session_id: string | null;
    is_authenticated: boolean | null;
  } | null;
};

type LoginChallengeQueryVars = {
  sessionId: string;
};

export const getLoginWithChallengeHook = <ChallengeMutationVars>(
  challengeMutation: DocumentNode,
  pollQuery: DocumentNode,
) => {
  return ({ onSuccess }: LoginHookParams) => {
    const [error, setError] = useState<ApolloError | Error | null>(null);
    const { session, setSession } = useLoginSession();

    const onError = useCallback(
      (err: ApolloError | Error | null) => {
        setError(err);
        setSession(null);
      },
      [setSession],
    );

    // mutation to start login challange, on complete it sets current login session value
    const [loginChallenge, { loading: isLoadingChallenge }] = useMutation<
      LoginChallengeData,
      ChallengeMutationVars
    >(challengeMutation, {
      onCompleted: (resp) => {
        const challengeId = resp.challenge?.challenge_id;
        const sessionId = resp.challenge?.session_id;
        if (challengeId && sessionId) {
          setSession({ challengeId, sessionId, time: Date.now() });
          setError(null);
        } else {
          onError(new Error('session_id or challenge_id is not defined'));
        }
      },
      onError,
    });

    const { data: pollData, error: pollError } = useQuery<
      LoginChallengeData,
      LoginChallengeQueryVars
    >(pollQuery, {
      variables: {
        sessionId: session?.sessionId || '',
      },
      fetchPolicy: 'network-only',
      skip: !session,
      pollInterval: 5000,
    });

    // check poll data
    useEffect(() => {
      if (pollData?.challenge?.is_authenticated) {
        setSession(null);
        setError(null);

        onSuccess();
      }
    }, [pollData, onSuccess, setSession]);

    // check poll error
    useEffect(() => {
      if (pollError) {
        onError(pollError);
      }
    }, [pollError, onError]);

    const onSubmit = useCallback(
      (data: ChallengeMutationVars) => {
        loginChallenge({ variables: data });
      },
      [loginChallenge],
    );

    const onCancel = useCallback(() => {
      setSession(null);
      setError(null);
    }, [setSession]);

    return {
      isLoadingChallenge,
      challengeId: session?.challengeId,
      error,
      onSubmit,
      onCancel,
    };
  };
};
