import { useBreakpointValue } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { Toggle, ToggleItem } from 'shared/components/index';
import { availableLoginMethods } from 'shared/lib';
import { LoginMethods } from 'shared/types';

export type LoginMethodSelectProps = {
  selectedMethod?: LoginMethods;
  onMethodChange: (method: LoginMethods) => void;
};

export const LoginMethodSelect = ({
  selectedMethod,
  onMethodChange,
}: LoginMethodSelectProps): JSX.Element => {
  const { t } = useTranslation('login');
  const isMobile = useBreakpointValue([true, false]);

  return (
    <Toggle
      mb={[5, 7]}
      name="loginMethod"
      onChange={onMethodChange}
      value={selectedMethod}
      w="100%"
    >
      {availableLoginMethods
        .filter((method) => method !== LoginMethods.IdCard || !isMobile)
        .map((method) => (
          <ToggleItem dataCy={`method-${method}`} key={method} value={method}>
            {t(`method.${method}`)}
          </ToggleItem>
        ))}
    </Toggle>
  );
};
