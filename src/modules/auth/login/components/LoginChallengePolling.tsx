import { Box, Button, Text } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import type { LoginMethods } from 'shared/types';

type LoginChallengePollingProps = {
  challengeId: string;
  type: LoginMethods.MobileId | LoginMethods.SmartId;
  onCancel: () => void;
};

export const LoginChallengePolling = ({
  type,
  challengeId,
  onCancel,
}: LoginChallengePollingProps) => {
  const { t } = useTranslation('login');
  return (
    <Box
      alignItems="stretch"
      display="flex"
      flexDir="column"
      height="100%"
      justifyContent={['center', 'flex-start']}
      pb={[0, 12]}
      width="100%"
    >
      <Text mb={[5, 8]} textStyle="h2">
        {t('challange-polling.title')}
      </Text>
      <Text mb={4} textStyle="body1">
        {t(`challange-polling.${type}-text`)}
      </Text>
      <Text
        bg="neutral.50"
        borderRadius="4px"
        color="neutral.must"
        data-cy={`${type}-polling-pin`}
        fontSize="2.5rem"
        fontWeight="bold"
        letterSpacing="0.08em"
        lineHeight={1.15}
        mb={[12, 8]}
        px={5}
        py={4}
        textAlign="center"
      >
        {challengeId}
      </Text>
      <Button
        data-cy={`${type}-cancel-button`}
        onClick={onCancel}
        size="sm"
        variant="ghost"
        width="full"
      >
        {t('challange-polling.cancel')}
      </Button>
    </Box>
  );
};
