/* eslint-disable @typescript-eslint/no-unused-expressions */
import { VStack } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { LoginChallengePolling } from 'modules/auth/login/components/LoginChallengePolling';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  ButtonWithLoader,
  PatternNumberInput,
  TextInput,
} from 'shared/components';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { region } from 'shared/lib';
import { LoginMethods } from 'shared/types';
import {
  APP_LANGUAGE_ABBR,
  AppRegions,
  extractValidationErrors,
  PHONE_FORMAT,
} from 'shared/utils';
import { z } from 'zod';

import type { LoginMethodComponentProps } from '../../types';
import { useMobileIdLogin } from './hooks';

const MobileIdLoginSchema = z.object({
  phone: z.string().min(1, 'required'),
  pin: z.string().min(1, 'required'),
});

type MobileIdLoginSchemaType = z.infer<typeof MobileIdLoginSchema>;

export const MobileIdLogin = ({ onSuccess }: LoginMethodComponentProps) => {
  const { t, i18n } = useTranslation(['login', 'common']);
  const isLatvianRegionRussianSpecificCopy =
    region === AppRegions.LV && i18n.language === APP_LANGUAGE_ABBR.ru;

  const handleGenericError = useHandleGenericError();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<MobileIdLoginSchemaType>({
    defaultValues: {
      pin: '',
      phone: '',
    },
    shouldFocusError: true,
    resolver: zodResolver(MobileIdLoginSchema),
  });
  const {
    isLoadingChallenge,
    error: mobileIdLoginError,
    challengeId,
    onSubmit,
    onCancel,
  } = useMobileIdLogin({
    onSuccess,
  });

  // handle server side error
  useEffect(() => {
    if (!mobileIdLoginError) {
      return;
    }
    const [validationErrors, newError] =
      extractValidationErrors(mobileIdLoginError);
    if (Object.keys(validationErrors).length > 0) {
      const { phone, pin } = validationErrors;
      pin &&
        setError('pin', { message: 'invalid-format' }, { shouldFocus: true });
      phone &&
        setError('phone', { message: 'invalid-format' }, { shouldFocus: true });
    }

    handleGenericError(newError);
  }, [mobileIdLoginError]);

  // if challenge mutation was successful and we started polling
  if (challengeId) {
    return (
      <LoginChallengePolling
        challengeId={challengeId}
        onCancel={onCancel}
        type={LoginMethods.MobileId}
      />
    );
  }

  return (
    <VStack as="form" onSubmit={handleSubmit(onSubmit)} spacing={2}>
      <PatternNumberInput
        allowEmptyFormatting
        data-cy="mobile-id-phone-input"
        disabled={isLoadingChallenge}
        error={
          errors.phone?.message
            ? t(`common:forms.${errors.phone?.message}`)
            : undefined
        }
        format={PHONE_FORMAT}
        label={t('mobile-label')}
        type="tel"
        valueIsNumericString
        {...register('phone')}
      />
      <TextInput
        {...register('pin')}
        autoComplete="pin"
        data-cy="mobile-id-id-input"
        error={
          errors.pin?.message
            ? t(`common:forms.${errors.pin.message}`)
            : undefined
        }
        inputMode="numeric"
        isDisabled={isLoadingChallenge}
        label={t(
          isLatvianRegionRussianSpecificCopy ? 'pin-label-lv' : 'pin-label',
        )}
      />
      <ButtonWithLoader
        data-cy="mobile-id-login-button"
        isDisabled={isLoadingChallenge}
        isLoading={isLoadingChallenge}
        type="submit"
        width="full"
      >
        {t('login-btn')}
      </ButtonWithLoader>
    </VStack>
  );
};
