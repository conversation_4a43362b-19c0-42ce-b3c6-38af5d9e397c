import { Box } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { LoginChallengePolling } from 'modules/auth/login/components/LoginChallengePolling';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ButtonWithLoader, TextInput } from 'shared/components';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { region } from 'shared/lib';
import { LoginMethods } from 'shared/types';
import {
  APP_LANGUAGE_ABBR,
  AppRegions,
  extractValidationErrors,
} from 'shared/utils';
import { z } from 'zod';

import type { LoginMethodComponentProps } from '../../types';
import { useSmartIdLogin } from './hooks';

const SmartIdLoginSchema = z.object({
  pin: z.string().min(1, 'required'),
});

type SmartIdLoginSchemaType = z.infer<typeof SmartIdLoginSchema>;

export const SmartIdLogin = ({ onSuccess }: LoginMethodComponentProps) => {
  const { t, i18n } = useTranslation(['login', 'common']);

  const isLatvianRegionRussianSpecificCopy =
    region === AppRegions.LV && i18n.language === APP_LANGUAGE_ABBR.ru;

  const handleGenericError = useHandleGenericError();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<SmartIdLoginSchemaType>({
    resolver: zodResolver(SmartIdLoginSchema),
  });

  const {
    isLoadingChallenge,
    error: mobileIdLoginError,
    challengeId,
    onSubmit,
    onCancel,
  } = useSmartIdLogin({
    onSuccess,
  });

  // handle server side error
  useEffect(() => {
    if (!mobileIdLoginError) {
      return;
    }
    const [validationErrors, newError] =
      extractValidationErrors(mobileIdLoginError);
    if (Object.keys(validationErrors).length > 0) {
      const { pin } = validationErrors;
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      pin &&
        setError('pin', { message: 'invalid-format' }, { shouldFocus: true });
    } else {
      handleGenericError(newError);
    }
  }, [mobileIdLoginError]);

  // if challange mutation was succesfull and we started polling
  if (challengeId) {
    return (
      <LoginChallengePolling
        challengeId={challengeId}
        onCancel={onCancel}
        type={LoginMethods.SmartId}
      />
    );
  }

  return (
    <Box as="form" onSubmit={handleSubmit(onSubmit)} width="100%">
      <TextInput
        autoComplete="pin"
        data-cy="smart-id-input"
        inputMode="numeric"
        label={t(
          isLatvianRegionRussianSpecificCopy ? 'pin-label-lv' : 'pin-label',
        )}
        mb={[7, 9]}
        {...register('pin')}
        error={
          errors.pin?.message
            ? t(`common:forms.${errors.pin.message}`)
            : undefined
        }
        isDisabled={isLoadingChallenge}
      />
      <ButtonWithLoader
        data-cy="smart-id-login-button"
        isDisabled={isLoadingChallenge}
        isLoading={isLoadingChallenge}
        type="submit"
        width="full"
      >
        {t('login-btn')}
      </ButtonWithLoader>
    </Box>
  );
};
