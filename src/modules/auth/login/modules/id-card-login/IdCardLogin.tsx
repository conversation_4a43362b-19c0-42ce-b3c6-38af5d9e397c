import { Box, Button, Text } from '@chakra-ui/react';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useShowError } from 'shared/hooks/alerts';

import { ColorSchemes } from '../../../../../shared/chakra-theme/foundations/colors';
import type { LoginMethodComponentProps } from '../../types';
import { useIdCardLogin } from './hooks';

export const IdCardLogin = ({ onSuccess }: LoginMethodComponentProps) => {
  const { t } = useTranslation(['login', 'common']);

  const { handleLoginRequest, isLoading, error } = useIdCardLogin({
    onSuccess,
  });
  const showError = useShowError();

  useEffect(() => {
    if (error) {
      showError(t('id-card-login-error'));
    }
  }, [error]);

  return (
    <Box alignItems="center" display="flex" flexDir="column">
      <Text
        bg="primary.100"
        borderRadius="4px"
        color="primary.900"
        mb={8}
        mt={1}
        px={3}
        py={2}
        textStyle="body2"
      >
        {t('insert-id-card')}
      </Text>

      <Button
        colorScheme={ColorSchemes.PRIMARY}
        data-cy="id-card-login-button"
        isDisabled={isLoading}
        isLoading={isLoading}
        onClick={handleLoginRequest}
        width="full"
      >
        {t('login:login-btn')}
      </Button>
    </Box>
  );
};
