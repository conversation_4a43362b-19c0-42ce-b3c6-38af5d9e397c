import { useCallback, useState } from 'react';
import { idUrl } from 'shared/lib';

import type { LoginHookParams } from '../../types';

export const useIdCardLogin = ({ onSuccess }: LoginHookParams) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const handleLoginRequest = useCallback(async () => {
    try {
      if (!idUrl) {
        throw new Error('REACT_APP_ID_URL environment variable is missing');
      }

      setIsLoading(true);

      const resp = await fetch(`${idUrl}/idlogin`, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        },
        credentials: 'include',
      });
      const isAuthenticated = await resp.json();

      if (!isAuthenticated) {
        throw new Error('Failed to authenticate');
      }

      onSuccess();
    } catch (error) {
      if (error instanceof Error) {
        setError(error);
      }
    } finally {
      setIsLoading(false);
    }
  }, [onSuccess]);

  return {
    handleLoginRequest,
    isLoading,
    error,
  };
};
