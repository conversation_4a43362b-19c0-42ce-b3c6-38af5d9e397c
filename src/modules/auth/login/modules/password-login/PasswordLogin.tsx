import { Box, Button } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { ButtonWithLoader, PasswordInput, TextInput } from 'shared/components';
import { ForgotPasswordRoute } from 'shared/constants/routes';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { z } from 'zod';

import type { LoginMethodComponentProps } from '../../types';
import { useLoginPasswordMutation } from './api';

const PasswordLoginSchema = z.object({
  username: z.string().min(1, 'required'),
  password: z.string().min(1, 'required'),
});

type PasswordLoginSchemaType = z.infer<typeof PasswordLoginSchema>;

export const PasswordLogin = ({ onSuccess }: LoginMethodComponentProps) => {
  const { t } = useTranslation(['login', 'common', 'reset-password']);
  const handleGenericError = useHandleGenericError();

  const [loginPasswordMutation, { loading }] = useLoginPasswordMutation({
    onCompleted: () => {
      onSuccess();
    },
    onError: (error) => {
      handleGenericError(error);
    },
  });

  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<PasswordLoginSchemaType>({
    resolver: zodResolver(PasswordLoginSchema),
  });

  const handleFormSubmit = async ({
    password,
    username,
  }: PasswordLoginSchemaType) => {
    await loginPasswordMutation({
      variables: {
        password,
        username,
      },
    });
  };

  return (
    <>
      <Box as="form" onSubmit={handleSubmit(handleFormSubmit)} width="100%">
        <TextInput
          data-cy="username-input"
          label={t('username-label')}
          mb={[0, 2]}
          {...register('username')}
          autoComplete="pin"
          error={
            errors.username?.message
              ? t(`common:forms.${errors.username.message}`)
              : undefined
          }
          isDisabled={loading}
        />
        <Controller
          control={control}
          name="password"
          render={({ field: { onChange, ...rest } }) => (
            <PasswordInput
              autoComplete="password"
              data-cy="password-input"
              error={
                errors.password?.message
                  ? t(`common:forms.${errors.password.message}`)
                  : undefined
              }
              isDisabled={loading}
              label={t('password-label')}
              mb={[7, 9]}
              onChange={(e) => {
                onChange(e.target.value.trim());
              }}
              {...rest}
            />
          )}
        />
        <ButtonWithLoader
          data-cy="password-login-button"
          isDisabled={loading}
          isLoading={loading}
          type="submit"
          width="full"
        >
          {t('login-btn')}
        </ButtonWithLoader>
      </Box>
      <Button
        as={Link}
        bg="transparent"
        color="primary.800"
        data-cy="forgot-password-btn"
        mt={4}
        to={ForgotPasswordRoute.format()}
        width="full"
      >
        {t('reset-password:forgot-password-btn')}
      </Button>
    </>
  );
};
