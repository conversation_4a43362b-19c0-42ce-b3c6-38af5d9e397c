import { Box, Button, Text, useDisclosure, VStack } from '@chakra-ui/react';
import { memo, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { Loader } from 'shared/components';
import { useShowMessage } from 'shared/hooks/alerts';
import { useMerchantDetails } from 'shared/hooks/merchant';
import { SettingsRouteTabs } from 'shared/types';

import { TabDesktopTitle } from '../shared';
import {
  type MerchantDataUpdateSchemaType,
  MerchantInfoModal,
} from './MerchantInfoModal';

type Props = {
  fieldName: string;
  value: string;
};

const FieldInfo = memo(({ fieldName, value }: Props) => {
  const { t } = useTranslation('settings');
  const suffix = fieldName.split('_').join('-');

  return (
    <Box w="100%">
      <Text textStyle="body2">{t(`merchant.labels.${suffix}`)}</Text>
      <Text
        data-cy={`merchant-settings-merchant-${suffix}`}
        textStyle="body1-highlight"
      >
        {value}
      </Text>
    </Box>
  );
});
FieldInfo.displayName = 'MerchantSettingsFieldInfo';

export const MerchantSettings = () => {
  const { t } = useTranslation('settings');
  const { loading, data } = useMerchantDetails();

  const { isOpen, onOpen, onClose } = useDisclosure();
  const showMessage = useShowMessage();

  const merchantInfo: MerchantDataUpdateSchemaType = useMemo(() => {
    return {
      name: data?.merchant?.name ?? '',
      registry_code: data?.merchant?.registry_code ?? '',
      email: data?.merchant?.email ?? '',
      phone: data?.merchant?.phone ?? '',
      address: data?.merchant?.address ?? '',
      iban: data?.merchant?.iban ?? '',
      beneficiary_name: data?.merchant?.beneficiary_name ?? '',
    };
  }, [data?.merchant]);

  const onUpdateSuccess = useCallback(() => {
    onClose();
    showMessage(t('notifications.merchant-info-updated'));
  }, [t, showMessage, onClose]);

  if (loading) {
    return <Loader />;
  }

  return (
    <Box>
      <TabDesktopTitle tab={SettingsRouteTabs.Merchant} />
      <VStack
        bg="neutral.50"
        borderRadius="4px"
        mb={[5, null, 6]}
        mt={[1, null, 4]}
        px={5}
        py={4}
        spacing={3}
      >
        {Object.entries(merchantInfo).map(([fieldName, value]) => (
          <FieldInfo fieldName={fieldName} key={fieldName} value={value} />
        ))}
      </VStack>
      <Button
        colorScheme={ColorSchemes.PRIMARY}
        data-cy="merchant-settings-open-modal-btn"
        onClick={onOpen}
        width="full"
      >
        {t('merchant.open-modal-btn')}
      </Button>
      {!!isOpen && (
        <MerchantInfoModal
          defaultValues={merchantInfo}
          isOpen
          onClose={onClose}
          onSuccess={onUpdateSuccess}
        />
      )}
    </Box>
  );
};
