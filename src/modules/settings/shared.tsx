import { Box, Button, type ChakraProps, Text } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { CountBadge } from 'shared/components';
import type { SettingsRouteTabs } from 'shared/types';

type TabDesktopTitleProps = {
  tab: SettingsRouteTabs;
  count?: number;
  actionText?: string;
  onAction?: () => void | Promise<void>;
} & ChakraProps;

export const TabDesktopTitle = ({
  tab,
  count,
  actionText,
  onAction,
  ...chakraProps
}: TabDesktopTitleProps) => {
  const { t } = useTranslation('settings');

  return (
    <Box alignItems="center" display={['none', null, 'flex']} {...chakraProps}>
      <Text data-cy={`${tab}-settings-title`} textStyle="h4">
        {t(`${tab}.title`)}
      </Text>
      {!!count && <CountBadge count={count} />}
      {!!actionText && !!onAction && (
        <Button
          colorScheme={ColorSchemes.PRIMARY}
          data-cy={`${tab}-settings-title-action`}
          ml="auto"
          onClick={onAction}
          size="sm"
        >
          {actionText}
        </Button>
      )}
    </Box>
  );
};
