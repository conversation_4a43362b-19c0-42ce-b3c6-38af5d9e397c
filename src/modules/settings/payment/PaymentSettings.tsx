import { Text, VStack } from '@chakra-ui/react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationScheduleType, MerchantSettingsBonusType } from 'shared/api';
import { useMerchantDetails } from 'shared/hooks/merchant';
import {
  type PaymentMethodInfo,
  usePaymentMethodInfo,
} from 'shared/hooks/payment-plan';
import { PaymentProvider } from 'shared/utils/app-settings';

import { PaymentMethodListItem } from './PaymentMethodListItem';

type MethodInfo = {
  mainDescription: string;
  secondaryDescription: string;
  isLastEnabled: boolean;
  info: PaymentMethodInfo;
};

function useMethods(): Record<ApplicationScheduleType, MethodInfo> {
  const { t } = useTranslation('settings');
  const merchatDetails = useMerchantDetails();
  const campaign = merchatDetails?.data?.merchant?.campaign;
  const settings = merchatDetails?.data?.merchant?.settings;

  const methods = useMemo(() => {
    const methods: Record<ApplicationScheduleType, MethodInfo> = {} as any;

    methods[ApplicationScheduleType.REGULAR] = {} as any;
    methods[ApplicationScheduleType.REGULAR].mainDescription = t(
      `payment-method.${ApplicationScheduleType.REGULAR}.description-main`,
    );
    if ((settings?.reverse_kickback_pct ?? 0) > 0) {
      methods[ApplicationScheduleType.REGULAR].secondaryDescription = t(
        `payment-method.${ApplicationScheduleType.REGULAR}.description-secondary-3`,
        {
          bonus: `${settings?.reverse_kickback_pct ?? 0}€`,
        },
      );
    } else if (
      (settings?.bonus_pct ?? 0) > 0 &&
      settings?.bonus_type !== MerchantSettingsBonusType.NONE
    ) {
      if (settings?.bonus_type === MerchantSettingsBonusType.PRINCIPAL) {
        methods[ApplicationScheduleType.REGULAR].secondaryDescription = t(
          `payment-method.${ApplicationScheduleType.REGULAR}.description-secondary-1`,
          {
            bonus: `${settings?.bonus_pct ?? 0}%`,
          },
        );
      } else {
        methods[ApplicationScheduleType.REGULAR].secondaryDescription = t(
          `payment-method.${ApplicationScheduleType.REGULAR}.description-secondary-2`,
          {
            bonus: `${settings?.bonus_pct ?? 0}%`,
          },
        );
      }
    }
    methods[ApplicationScheduleType.REGULAR].isLastEnabled =
      !!campaign?.regular_hp_enabled &&
      !campaign?.converting_schedule_enabled &&
      !campaign?.pay_later_enabled &&
      !campaign?.esto_pay_enabled;

    methods[ApplicationScheduleType.ESTO_X] = {} as any;
    methods[ApplicationScheduleType.ESTO_X].mainDescription = t(
      `payment-method.${ApplicationScheduleType.ESTO_X}.description-main`,
      {
        period: campaign?.converting_schedule_months ?? 0,
      },
    );
    if ((campaign?.converting_schedule_reverse_kickback_pct ?? 0) > 0) {
      methods[ApplicationScheduleType.ESTO_X].secondaryDescription = t(
        `payment-method.${ApplicationScheduleType.ESTO_X}.description-secondary`,
        {
          feePct: `${campaign?.converting_schedule_reverse_kickback_pct ?? 0}%`,
        },
      );
    }
    methods[ApplicationScheduleType.ESTO_X].isLastEnabled =
      !campaign?.regular_hp_enabled &&
      !!campaign?.converting_schedule_enabled &&
      !campaign?.pay_later_enabled &&
      !campaign?.esto_pay_enabled;

    methods[ApplicationScheduleType.PAY_LATER] = {} as any;
    methods[ApplicationScheduleType.PAY_LATER].mainDescription = t(
      `payment-method.${ApplicationScheduleType.PAY_LATER}.description-main`,
    );
    if ((campaign?.pay_later_reverse_kickback_pct ?? 0) > 0) {
      methods[ApplicationScheduleType.PAY_LATER].secondaryDescription = t(
        `payment-method.${ApplicationScheduleType.PAY_LATER}.description-secondary`,
        {
          feePct: `${campaign?.pay_later_reverse_kickback_pct ?? 0}%`,
        },
      );
    }
    methods[ApplicationScheduleType.PAY_LATER].isLastEnabled =
      !campaign?.regular_hp_enabled &&
      !campaign?.converting_schedule_enabled &&
      !!campaign?.pay_later_enabled &&
      !campaign?.esto_pay_enabled;

    methods[ApplicationScheduleType.ESTO_PAY] = {} as any;
    methods[ApplicationScheduleType.ESTO_PAY].mainDescription = t(
      `payment-method.${ApplicationScheduleType.ESTO_PAY}.description-main`,
    );

    // TODO: Refactor to unhardcode getkevin provider. Providers can be multiple
    if (
      (campaign?.direct_payment_gateways?.find(
        (gateway) => gateway?.provider === PaymentProvider.KLIX_BANKLINK,
      )?.fee_fixed ?? 0) > 0 ||
      (campaign?.direct_payment_gateways?.find(
        (gateway) => gateway?.provider === PaymentProvider.KLIX_BANKLINK,
      )?.fee_pct ?? 0) > 0
    ) {
      methods[ApplicationScheduleType.ESTO_PAY].secondaryDescription = t(
        `payment-method.${ApplicationScheduleType.ESTO_PAY}.description-secondary`,
        {
          fixed: `${
            campaign?.direct_payment_gateways?.find(
              (gateway) => gateway?.provider === PaymentProvider.KLIX_BANKLINK,
            )?.fee_fixed ?? 0
          }€`,
          feePct: `${
            campaign?.direct_payment_gateways?.find(
              (gateway) => gateway?.provider === PaymentProvider.KLIX_BANKLINK,
            )?.fee_pct ?? 0
          }%`,
          min: `${
            campaign?.direct_payment_gateways?.find(
              (gateway) => gateway?.provider === PaymentProvider.KLIX_BANKLINK,
            )?.fee_total_min ?? 0
          }€`,
          max: `${
            campaign?.direct_payment_gateways?.find(
              (gateway) => gateway?.provider === PaymentProvider.KLIX_BANKLINK,
            )?.fee_total_max ?? 0
          }€`,
        },
      );
    }
    methods[ApplicationScheduleType.ESTO_PAY].isLastEnabled =
      !campaign?.regular_hp_enabled &&
      !campaign?.converting_schedule_enabled &&
      !campaign?.pay_later_enabled &&
      !!campaign?.esto_pay_enabled;

    return methods;
  }, [campaign, settings, t]);

  methods[ApplicationScheduleType.REGULAR].info = usePaymentMethodInfo(
    ApplicationScheduleType.REGULAR,
  );
  methods[ApplicationScheduleType.ESTO_X].info = usePaymentMethodInfo(
    ApplicationScheduleType.ESTO_X,
  );
  methods[ApplicationScheduleType.PAY_LATER].info = usePaymentMethodInfo(
    ApplicationScheduleType.PAY_LATER,
  );
  methods[ApplicationScheduleType.ESTO_PAY].info = usePaymentMethodInfo(
    ApplicationScheduleType.ESTO_PAY,
  );

  return methods;
}

export const PaymentSettings = () => {
  const { t } = useTranslation('settings');
  const methods = useMethods();

  return (
    <>
      <Text display={['none', null, 'block']} mb={2} textStyle="h4">
        {t('payment-method.title')}
      </Text>
      <Text mb={4} textStyle="body2">
        {t('payment-method.name-logo-note')}
      </Text>
      <VStack mb={10} spacing={[3, 4]}>
        {(Object.keys(methods) as [ApplicationScheduleType]).map(
          (method: ApplicationScheduleType) => {
            const methodDetails = methods[method];

            return (
              <PaymentMethodListItem
                key={method}
                mainDescription={methodDetails.mainDescription}
                method={method}
                methodInfo={methodDetails.info}
                secondaryDescription={methodDetails.secondaryDescription}
                shouldDisable
              />
            );
          },
        )}
      </VStack>
    </>
  );
};
