import { Box, Button, Text, useDisclosure, VStack } from '@chakra-ui/react';
import { SaveStoreModal } from 'modules/settings/stores/SaveStoreModal';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FiTrash2 } from 'react-icons/fi';
import type { MerchantStoreBasicFragment } from 'shared/api';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { Loader } from 'shared/components';
import { useShowMessage } from 'shared/hooks/alerts';
import {
  useAvailableStores,
  useDeleteMerchantStore,
} from 'shared/hooks/merchant';
import { SettingsRouteTabs } from 'shared/types';

import { TabDesktopTitle } from '../shared';
import { StoreListItem } from './StoreListItem';

export const StoresSettings = () => {
  const { t } = useTranslation('settings');

  const { isLoading, availableStores } = useAvailableStores();
  const stores = availableStores || [];

  const [selectedStore, setSelectedStore] =
    useState<MerchantStoreBasicFragment>();

  const { isOpen, onOpen, onClose } = useDisclosure({
    onClose() {
      setSelectedStore(undefined);
    },
  });
  const showMessage = useShowMessage();

  const { isLoading: isDeleting, deleteStore } = useDeleteMerchantStore();

  const onUpdateSuccess = useCallback(() => {
    onClose();
    showMessage(t('notifications.store-saved'));
  }, [t, showMessage, onClose]);

  if (isLoading) {
    return <Loader />;
  }

  return (
    <Box>
      <TabDesktopTitle
        count={stores.length}
        mb={2}
        tab={SettingsRouteTabs.Stores}
      />
      <Box mb={4}>
        <Text as="span" textStyle="body2">
          {t('stores.disclaimer-1')}
        </Text>{' '}
        <Text as="span" textStyle="body2-highlight">
          {t('stores.disclaimer-2')}
        </Text>
      </Box>
      {stores.length > 0 && (
        <VStack data-cy="stores-settings-list" mb={5} spacing={0}>
          {stores.map((store) => (
            <StoreListItem
              dataCy={`stores-settings-list-item-${store.id}`}
              isDisabled={isDeleting}
              key={store.id}
              name={store.name}
              onDelete={async () => {
                await deleteStore({ storeId: store.id });
                showMessage(t('notifications.store-deleted'), FiTrash2);
              }}
              onEdit={() => {
                setSelectedStore(store);
                onOpen();
              }}
            />
          ))}
        </VStack>
      )}

      <Button
        colorScheme={ColorSchemes.PRIMARY}
        data-cy="stores-settings-add-store-btn"
        onClick={onOpen}
        width="full"
      >
        {t('stores.add-store-btn')}
      </Button>
      {!!isOpen && (
        <SaveStoreModal
          isOpen
          onClose={onClose}
          onSuccess={onUpdateSuccess}
          store={selectedStore}
        />
      )}
    </Box>
  );
};
