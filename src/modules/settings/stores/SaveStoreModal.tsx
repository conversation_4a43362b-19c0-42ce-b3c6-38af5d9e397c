import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  ModalOverlay,
  VStack,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import type { MerchantStoreBasicFragment } from 'shared/api';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  ButtonWithLoader,
  ModalCloseButton,
  TextInput,
} from 'shared/components';
import {
  useCreateMerchantStore,
  useUpdateMerchantStore,
} from 'shared/hooks/merchant';
import { z } from 'zod';

const StoreSchema = z.object({
  name: z.string().min(1, 'required'),
});

type StoreSchemaType = z.infer<typeof StoreSchema>;

type Props = {
  isOpen: boolean;
  store?: MerchantStoreBasicFragment;
  onSuccess: () => void;
  onClose: () => void;
};

export const SaveStoreModal = ({
  isOpen,
  onClose,
  onSuccess,
  store,
}: Props) => {
  const { t } = useTranslation(['settings', 'common']);
  const mode = store ? 'edit' : 'add';

  const {
    register,
    formState: { errors, isValid },
    handleSubmit,
  } = useForm<StoreSchemaType>({
    defaultValues: { name: store?.name },
    resolver: zodResolver(StoreSchema),
  });
  const { isLoading: isCreating, createStore } = useCreateMerchantStore();
  const { isLoading: isUpdating, updateStore } = useUpdateMerchantStore();
  const isLoading = isCreating || isUpdating;

  const onSubmit = useCallback(
    async ({ name }: StoreSchemaType) => {
      const result = store
        ? await updateStore({ storeId: store.id, name })
        : await createStore({ name });
      if (result !== null) {
        onSuccess();
      }
    },
    [store, onSuccess, createStore, updateStore],
  );

  return (
    <Modal isOpen={isOpen} onClose={onClose} scrollBehavior="inside">
      <ModalOverlay display={['none', 'flex']} />
      <ModalContent
        as="form"
        data-cy={`${mode}-store-settings-modal-content`}
        maxW="29rem"
        onSubmit={handleSubmit(onSubmit)}
      >
        <ModalHeader>{t(`stores.modal.${mode}.title`)}</ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={[4, 1]}>
          <VStack spacing={1}>
            <TextInput
              autoFocus
              data-cy="stores-settings-name-input"
              inputMode="text"
              label={t('stores.labels.name')}
              {...register('name')}
              error={
                errors.name?.message
                  ? t(`common:forms.${errors.name.message}`)
                  : undefined
              }
              isDisabled={isLoading}
            />
          </VStack>
        </ModalBody>

        <ModalFooter
          borderTop={['1px solid', 'none']}
          borderTopColor="neutral.150"
        >
          <Button
            colorScheme={ColorSchemes.SECONDARY}
            display={['none', 'inline-flex']}
            isDisabled={isLoading}
            mr={[0, 3]}
            mt={[3, 0]}
            onClick={onClose}
          >
            {t('common:forms.cancel')}
          </Button>
          <ButtonWithLoader
            data-cy="store-settings-modal-submit"
            isDisabled={isLoading || !isValid}
            isLoading={isLoading}
            type="submit"
          >
            {t(`stores.modal.${mode}.submit`)}
          </ButtonWithLoader>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
