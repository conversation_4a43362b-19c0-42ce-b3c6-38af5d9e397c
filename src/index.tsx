import './sentry.config';

import * as Sentry from '@sentry/react';
import App from 'app';
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';

import reportWebVitals from './reportWebVitals';

const container = document.getElementById('root');

if (!container) throw new Error('Root element not found');
const root = createRoot(container);

root.render(
  <StrictMode>
    <Sentry.ErrorBoundary>
      <App />
    </Sentry.ErrorBoundary>
  </StrictMode>,
);

reportWebVitals();
