import { AppLanguages, AppRegions } from './app.constants';

/**
 * Privacy policy URLs based on region and language
 * Data source: https://estoas.atlassian.net/wiki/spaces/PROD/pages/3790340110/QA+Testing+Credit+Eligibility+check
 */
export const PRIVACY_POLICY_URLS = {
  [AppRegions.et]: {
    [AppLanguages.en]: 'https://esto-public.s3.eu-west-2.amazonaws.com/docs/en-us/Rules%20of%20processing%20personal%20data.pdf',
    [AppLanguages.et]: 'https://esto-public.s3.eu-west-2.amazonaws.com/docs/et-ee/Isikuandmete%20t%C3%B6%C3%B6tlemise%20reeglid.pdf',
    [AppLanguages.ru]: 'https://esto-public.s3.eu-west-2.amazonaws.com/docs/ru-ru/%D0%9F%D1%80%D0%B0%D0%B2%D0%B8%D0%BB%D0%B0%20%D0%BE%D0%B1%D1%80%D0%B0%D0%B1%D0%BE%D1%82%D0%BA%D0%B8%20%D0%BB%D0%B8%D1%87%D0%BD%D1%8B%D1%85%20%D0%B4%D0%B0%D0%BD%D0%BD%D1%8B%D1%85.pdf',
  },
  [AppRegions.lv]: {
    // LV region has one common link for all languages
    [AppLanguages.en]: 'https://esto-lv-public.s3.eu-central-1.amazonaws.com/docs/lv-lv/Priva%CC%84tuma%20politika.pdf',
    [AppLanguages.lv]: 'https://esto-lv-public.s3.eu-central-1.amazonaws.com/docs/lv-lv/Priva%CC%84tuma%20politika.pdf',
    [AppLanguages.ru]: 'https://esto-lv-public.s3.eu-central-1.amazonaws.com/docs/lv-lv/Priva%CC%84tuma%20politika.pdf',
  },
  [AppRegions.lt]: {
    [AppLanguages.en]: 'https://esto-lt-public.s3.eu-central-1.amazonaws.com/docs/lt-lt/ESTO%20Privatumo%20politika.pdf',
    [AppLanguages.lt]: 'https://esto-lt-public.s3.eu-central-1.amazonaws.com/docs/lt-lt/ESTO%20Privatumo%20politika.pdf',
    [AppLanguages.ru]: 'https://esto-lt-public.s3.eu-central-1.amazonaws.com/docs/ru-ru/%D0%9F%D1%80%D0%B0%D0%B2%D0%B8%D0%BB%D0%B0%20%D0%BE%D0%B1%D1%80%D0%B0%D0%B1%D0%BE%D1%82%D0%BA%D0%B8%20%D0%BB%D0%B8%D1%87%D0%BD%D1%8B%D1%85%20%D0%B4%D0%B0%D0%BD%D0%BD%D1%8B%D1%85.pdf',
  },
} as const;

/**
 * Get privacy policy URL based on current region and language
 * @param region - Current app region (EE, LV, LT)
 * @param language - Current app language (en, et, lv, lt, ru)
 * @returns Privacy policy URL for the given region and language
 */
export const getPrivacyPolicyUrl = (region: AppRegions, language: AppLanguages): string => {
  const regionUrls = PRIVACY_POLICY_URLS[region];
  
  if (!regionUrls) {
    // Fallback to EE region English if region not found
    return PRIVACY_POLICY_URLS[AppRegions.et][AppLanguages.en];
  }
  
  const url = regionUrls[language];
  
  if (!url) {
    // Fallback to first available URL in the region if language not found
    const firstAvailableUrl = Object.values(regionUrls)[0];
    return firstAvailableUrl;
  }
  
  return url;
};
