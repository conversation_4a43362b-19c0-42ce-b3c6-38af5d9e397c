import type { CodegenConfig } from '@graphql-codegen/cli';

const config: CodegenConfig = {
  schema: process.env.REACT_APP_API_ENDPOINT || 'https://api.dekker.ee/graphql',
  documents: ['src/**/*.graphql'],
  config: {
    content:
      '/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */',
    withHooks: true,
    preResolveTypes: true,
    strictScalars: true,
    scalars: {
      Upload: 'number',
    },
    namingConvention: {
      enumValues: 'change-case-all#upperCase',
      transformUnderscore: true,
    },
  },
  hooks: {
    afterAllFileWrite: [
      'eslint --fix **/*.gen.ts && prettier --write **/*.gen.ts',
    ],
  },
  generates: {
    'src/shared/api/models.gen.ts': {
      plugins: ['add', 'typescript'],
    },
    'src/': {
      preset: 'near-operation-file',
      presetConfig: {
        extension: '.gen.ts',
        baseTypesPath: 'shared/api/models.gen.ts',
      },
      plugins: ['add', 'typescript-operations', 'typescript-react-apollo'],
    },
  },
};

export default config;
