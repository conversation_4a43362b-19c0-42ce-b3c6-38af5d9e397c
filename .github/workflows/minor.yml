# Release minor version update

name: minor
on:
  pull_request:
    types: [closed]

jobs:
  minor:
    runs-on: ubuntu-latest
    if: github.event.pull_request.merged && contains(github.head_ref, 'feature') && github.base_ref == 'master'
    env:
      JIRA_BASE_URL: ${{ secrets.JIRA_BASE_URL }}
      JIRA_USER_EMAIL: ${{ secrets.JIRA_USER_EMAIL }}
      JIRA_API_TOKEN: ${{ secrets.JIRA_API_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Jira CLI
        uses: atlassian/gajira-cli@master

      - name: Login to JIRA
        uses: atlassian/gajira-login@master

      - name: Find in commit messages
        id: jira_key
        uses: atlassian/gajira-find-issue-key@master
        with:
          string: ${{ github.head_ref }}

      - name: Get task description
        id: issue_details
        run: |
          echo "::set-output name=summary::$(jira view --template=json --gjq="fields.summary" ${{ steps.jira_key.outputs.issue }})"
          echo "::set-output name=description::$(jira view --template=json --expand=renderedFields --gjq="renderedFields.description" ${{ steps.jira_key.outputs.issue }} | tr -d '\n\t')"

      - name: Bump version and push tag
        id: tag_version
        uses: mathieudutour/github-tag-action@v5.6
        with:
          default_bump: minor
          github_token: ${{ github.token }}

      - name: Create a GitHub release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ github.token }}
        with:
          tag_name: ${{ steps.tag_version.outputs.new_tag }}
          release_name: Release ${{ steps.tag_version.outputs.new_tag }}
          body: 'Added: [${{ steps.jira_key.outputs.issue }}](https://estoas.atlassian.net/browse/${{ steps.jira_key.outputs.issue }}) ${{ steps.issue_details.outputs.summary }}'

      - name: Notify Slack
        id: slack
        uses: slackapi/slack-github-action@v1.14.0
        with:
          payload: "{\"text\":\"*Feature Released!* 🚀 \\n <https://estoas.atlassian.net/browse/${{ steps.jira_key.outputs.issue }}|${{ steps.jira_key.outputs.issue }}> ${{ steps.issue_details.outputs.summary }}\"}"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_RELEASE_WEBHOOK_URL }}

      - name: Merge to develop
        uses: devmasx/merge-branch@1.4.0
        with:
          type: now
          github_token: ${{ secrets.GHA_TOKEN_FOR_SYNC }}
          from_branch: master
          target_branch: develop

      - name: Create Pull Request in case a conflict
        if: ${{ failure() }}
        uses: repo-sync/pull-request@v2
        with:
          github_token: ${{ github.token }}
          destination_branch: develop
          pr_title: Resolve master → develop conflict
          pr_body: This PR was created automatically because of conflicts while syncing master to develop
          pr_assignee: ${{ github.actor }}
