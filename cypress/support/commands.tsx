/// <reference types="cypress" />
import { MockedProvider } from '@apollo/client/testing';
import { AppChakraProvider } from 'app/providers/with-chakra/withChakra';
import { mount } from 'cypress/react18';
import { GlobalStateProvider } from 'modules/GlobalStateProvider';
import { I18nextProvider } from 'react-i18next';
import { MemoryRouter } from 'react-router-dom';
import i18n from 'shared/lib/i18n';

Cypress.Commands.add('mountWithProvider', (node, config) => {
  localStorage.setItem('i18nextLng', config?.lang || 'en');

  return mount(
    <MemoryRouter>
      <I18nextProvider i18n={i18n}>
        <MockedProvider mocks={config?.apolloMocks} showWarnings={true}>
          <AppChakraProvider>
            <GlobalStateProvider>{node}</GlobalStateProvider>
          </AppChakraProvider>
        </MockedProvider>
      </I18nextProvider>
    </MemoryRouter>,
  );
});

Cypress.Commands.add('dataCy', (value) => {
  return cy.get(`[data-cy="${value}"]`);
});
