/* eslint-disable @typescript-eslint/no-namespace */
/* eslint-disable @typescript-eslint/no-require-imports */
// ***********************************************************
// This example support/component.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
// import 'cypress/support/commands';

import type { MockedResponse } from '@apollo/client/testing/core';
// Alternatively you can use CommonJS syntax:
import type { ReactNode } from 'react';
import type { AvailableLanguage } from 'shared/types';

require('./commands');

// Augment the Cypress namespace to include type definitions for
// your custom command.
// Alternatively, can be defined in cypress/support/component.d.ts
// with a <reference path="./component" /> at the top of your spec.
declare global {
  namespace Cypress {
    interface Chainable {
      mountWithProvider: (
        node: ReactNode,
        config?: {
          apolloMocks?: ReadonlyArray<MockedResponse>;
          lang?: AvailableLanguage;
        },
      ) => void;
      dataCy(value: string): Chainable<JQuery<HTMLElement>>;
    }
  }
}

// Example use:
// cy.mount(<MyComponent />)
