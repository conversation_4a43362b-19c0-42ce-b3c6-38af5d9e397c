import type { MockedResponse } from '@apollo/client/testing/core';
import { ADMIN_MERCHANTS_QUERY } from 'shared/api';

export const adminMerchantsMock: Readonly<MockedResponse> = {
  request: {
    query: ADMIN_MERCHANTS_QUERY,
    variables: { name: '', limit: 1 },
  },
  result: {
    data: {
      merchants: {
        __typename: 'merchantPagination',
        data: [
          {
            __typename: 'Merchant',
            id: 1,
            name: 'Test Merchant',
            logo_path:
              'https://esto-public-dev.s3.eu-west-2.amazonaws.com/merchant/profile/2y10ry2m0i06j1uzqjcybavyuf86kbihelzmqpysfc9stc2papotrj2a.png',
          },
        ],
      },
    },
  },
};
