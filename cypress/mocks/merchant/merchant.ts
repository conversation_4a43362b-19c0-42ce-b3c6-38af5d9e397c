import type { MockedResponse } from '@apollo/client/testing/core';
import { MerchantDocument } from 'shared/api';

export const merchantMock: Readonly<MockedResponse> = {
  request: {
    query: MerchantDocument,
    variables: {
      merchantId: 1,
    },
  },
  result: {
    data: {
      merchant: {
        id: 1,
        shop_id: 'test-123',
        name: 'Test Merchant',
        return_url: 'https://esto.ee',
        notification_url: 'https://api.esto.ee/test/merchant/notification',
        cancel_url: 'https://api.esto.ee/test/merchant/cancel',
        home_url: 'https://esto.ee',
        logo_path:
          'https://esto-public.s3.eu-west-2.amazonaws.com/merchant/profile/2y10ry2m0i06j1uzqjcybavyuf86kbihelzmqpysfc9stc2papotrj2a.png',
        registry_code: '66666666',
        phone: '+3725219541',
        address: 'test address 123',
        iban: '********************',
        beneficiary_name: '<PERSON><PERSON><PERSON> NIMI',
        cashier_loyalty_enabled: true,
        email: '<EMAIL>',
        secret_key: 'EbcauTDVsFZtbUz74KO5rcrS7wrIzoRO',
        settings: {
          merchant_financing_pct: 80,
          net_total_min: 0.1,
          net_total_max: 10000,
          can_create_small_loan: false,
          can_create_dynamic_loan: true,
          can_enable_special_settings: true,
          can_see_eligibility_status: false,
          buyback_guarantee_days: 90,
          buyback_discount_pct: 50,
          bonus_pct: 5,
          bonus_type: 'PRINCIPAL',
          reverse_kickback_pct: 0,
          max_amount_id_verification_not_required: 2000,
          min_months_period: 3,
          max_months_period: 60,
          __typename: 'MerchantSettings',
        },
        campaign: {
          is_active: true,
          regular_hp_enabled: true,
          converting_schedule_enabled: true,
          converting_schedule_name: 'ESTO 3',
          converting_schedule_logo_url:
            'https://esto-public.s3.eu-west-2.amazonaws.com/merchant/profile/2y105lfccq6yysfvmuv0jxosobjokuwtunj98hbhvqwatw9oe8roparg.png',
          converting_schedule_net_total_min: 0.01,
          converting_schedule_net_total_max: 10000,
          converting_schedule_months: 3,
          converting_schedule_regular_months: 12,
          converting_schedule_reverse_kickback_pct: 2,
          pay_later_enabled: true,
          pay_later_name: 'Maksa hiljem',
          pay_later_logo_url:
            'https://esto-public.s3.eu-west-2.amazonaws.com/merchant/profile/2y101ot2odqavhcdy2ck54hmxebzwwdip6dxesy8ck4rye0tcbokygbs.png',
          pay_later_net_total_min: 0.01,
          pay_later_net_total_max: 10000,
          pay_later_reverse_kickback_pct: 1,
          esto_pay_enabled: true,
          esto_pay_name: 'ESTO Pay',
          esto_pay_logo_url: null,
          esto_pay_net_total_min: 0.01,
          esto_pay_net_total_max: 99999.99,
          fixed_annual_pct_rate: null,
          fixed_management_fee: 0.9,
          fixed_contract_fee: 2,
          converting_schedule_fixed_contract_fee: 0,
          direct_payment_gateways: [
            {
              enabled: true,
              provider: 'KLIX_BANKLINK',
              fee_fixed: 0,
              fee_pct: 1,
              fee_total_min: 0.05,
              fee_total_max: 2,
              __typename: 'MerchantDirectPaymentGateway',
            },
            {
              enabled: true,
              provider: 'STRIPE_CARD',
              fee_fixed: 0,
              fee_pct: 1,
              fee_total_min: 0.05,
              fee_total_max: 2,
              __typename: 'MerchantDirectPaymentGateway',
            },
          ],
          __typename: 'MerchantCampaign',
        },
        calculator_skin: {
          merchant_id: 1,
          main: '#36775e',
          secondary: '#ffb300',
          text: '#fafafa',
          period: '#fafafa',
          active_period: '#ffd54f',
          period_text: '#212121',
          monthly_text: '#ffb300',
          button: '#ffca28',
          button_text: '#333333',
          __typename: 'MerchantCalculatorSkin',
        },
        __typename: 'Merchant',
        users: [
          {
            id: 1,
            merchant_permission_bits: 5,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Mikk Mihkel',
              last_name: 'Nurges',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 4,
            merchant_permission_bits: 15,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Martin',
              last_name: 'Ustaal',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 16,
            merchant_permission_bits: 15,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'SIIM',
              last_name: 'NURGES',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 23661,
            merchant_permission_bits: 15,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'ALENA',
              last_name: 'STARIKOVA',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 29180,
            merchant_permission_bits: 0,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'NADE\u017dDA',
              last_name: 'BOT\u0160AROVA-SUVOROVA',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 36296,
            merchant_permission_bits: 15,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Ronald',
              last_name: 'Uus',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 44042,
            merchant_permission_bits: 15,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Testing',
              last_name: 'Finestmedia',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 53298,
            merchant_permission_bits: 15,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Dmytro',
              last_name: 'Vostrikov',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 67235,
            merchant_permission_bits: 15,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Teet Egert',
              last_name: 'Vaabel',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 67510,
            merchant_permission_bits: 15,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Pavel',
              last_name: 'Sakalo',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 69520,
            merchant_permission_bits: 2,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Andrew',
              last_name: 'Galavan',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 70379,
            merchant_permission_bits: 15,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Jan',
              last_name: 'Stupka',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 81315,
            merchant_permission_bits: 4,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Adolf',
              last_name: 'Kroll',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 85830,
            merchant_permission_bits: 4,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Petro',
              last_name: 'Karimov',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 92398,
            merchant_permission_bits: 1,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Andrii',
              last_name: 'Hafych',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 106685,
            merchant_permission_bits: 4,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Karl',
              last_name: 'Lennart',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 115836,
            merchant_permission_bits: 15,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Andrus-eerik',
              last_name: 'Paaksi',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 119144,
            merchant_permission_bits: 15,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'EDWARD',
              last_name: 'TOONELA',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 129990,
            merchant_permission_bits: 15,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Karyna',
              last_name: 'Chelpan',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 137678,
            merchant_permission_bits: 1,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Test ',
              last_name: 'M\u00fc\u00fcgiesindaja',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 145670,
            merchant_permission_bits: 4,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Nade\u017eda',
              last_name: 'Bot\u0161arova-Suvorova',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 147974,
            merchant_permission_bits: 15,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Juska',
              last_name: 'Matintalo',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 151478,
            merchant_permission_bits: 4,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'JANELLE',
              last_name: 'KOEL',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 154299,
            merchant_permission_bits: 4,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Renata',
              last_name: 'Ambrazaitiene',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 163352,
            merchant_permission_bits: 0,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Mykhailo',
              last_name: 'Alekseiev',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 166569,
            merchant_permission_bits: 15,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Karl',
              last_name: 'Korvek',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
          {
            id: 166643,
            merchant_permission_bits: 3,
            send_emails: 0,
            email: '<EMAIL>',
            profile: {
              first_name: 'Kermo',
              last_name: 'Kalle',
              __typename: 'UserProfile',
            },
            __typename: 'MerchantUser',
          },
        ],
        stores: [
          {
            id: 491,
            name: 'application',
            cashiers: [],
            __typename: 'MerchantStore',
          },
          {
            id: 484,
            name: 'test',
            cashiers: [],
            __typename: 'MerchantStore',
          },
          {
            id: 485,
            name: 'test2',
            cashiers: [],
            __typename: 'MerchantStore',
          },
        ],
      },
    },
  },
};
