import type { MockedResponse } from '@apollo/client/testing/core';
import { PricingDocument } from 'shared/api';

export const pricingMock: Record<
  'noDownPayment' | 'downPayment',
  MockedResponse
> = {
  noDownPayment: {
    request: {
      query: PricingDocument,
      variables: {
        keys: [
          'down_payment.minimum_loan_amount',
          'down_payment.minimum_required_pct',
        ],
      },
    },
    result: {
      data: {
        pricing: [
          {
            key: 'down_payment.minimum_loan_amount',
            value: '0',
            type: 'FLOAT',
            __typename: 'Pricing',
          },
          {
            key: 'down_payment.minimum_required_pct',
            value: '0',
            type: 'FLOAT',
            __typename: 'Pricing',
          },
        ],
      },
    },
  },
  downPayment: {
    request: {
      query: PricingDocument,
      variables: {
        keys: [
          'down_payment.minimum_loan_amount',
          'down_payment.minimum_required_pct',
        ],
      },
    },
    result: {
      data: {
        pricing: [
          {
            key: 'down_payment.minimum_loan_amount',
            value: '1400',
            type: 'FLOAT',
            __typename: 'Pricing',
          },
          {
            key: 'down_payment.minimum_required_pct',
            value: '10',
            type: 'FLOAT',
            __typename: 'Pricing',
          },
        ],
      },
    },
  },
};
