import type { MockedResponse } from '@apollo/client/testing/core';
import { SendPurchaseUrlDocument, UserMessageSentType } from 'shared/api';

export const SendPurchaseUrlMocks: Readonly<
  Record<
    'successFlow',
    (variables: {
      email?: string;
      userMessageType: UserMessageSentType;
      phone?: string;
    }) => MockedResponse
  >
> = {
  successFlow: ({ email, userMessageType, phone }) => ({
    request: {
      query: SendPurchaseUrlDocument,
      variables:
        userMessageType === UserMessageSentType.SMS
          ? { applicationId: 1875917, type: userMessageType, phone }
          : {
              applicationId: 1875917,
              type: userMessageType,
              email,
            },
    },
    result: {
      data: {
        sent: true,
      },
    },
  }),
};
