import type { MockedResponse } from '@apollo/client/testing/core';
import { ApplicationScheduleType, CreditSettingsDocument } from 'shared/api';

export const CreditSettingsMock: Readonly<
  Record<
    ApplicationScheduleType.REGULAR | ApplicationScheduleType.ESTO_X,
    (amount: number) => MockedResponse
  >
> = {
  [ApplicationScheduleType.REGULAR]: (value) => ({
    request: {
      query: CreditSettingsDocument,
      variables: {
        scheduleType: ApplicationScheduleType.REGULAR,
        merchantId: 1,
        netTotal: value,
      },
    },
    result: {
      data: {
        credit_settings: [
          {
            net_total: value,
            month: 3,
            contract_fee: 2,
            annual_pct_rate: 26.1,
            management_fee: 0.9,
            irr: 49.61,
            monthly_payment: 439.38,
            __typename: 'CreditSetting',
          },
          {
            net_total: value,
            month: 6,
            contract_fee: 2,
            annual_pct_rate: 23.3,
            management_fee: 0.9,
            irr: 49.44,
            monthly_payment: 230.67,
            __typename: 'CreditSetting',
          },
          {
            net_total: value,
            month: 12,
            contract_fee: 2,
            annual_pct_rate: 22.6,
            management_fee: 0.9,
            irr: 49.81,
            monthly_payment: 127.04,
            __typename: 'CreditSetting',
          },
          {
            net_total: value,
            month: 18,
            contract_fee: 2,
            annual_pct_rate: 22.7,
            management_fee: 0.9,
            irr: 49.66,
            monthly_payment: 92.84,
            __typename: 'CreditSetting',
          },
          {
            net_total: value,
            month: 24,
            contract_fee: 2,
            annual_pct_rate: 23,
            management_fee: 0.9,
            irr: 49.39,
            monthly_payment: 75.99,
            __typename: 'CreditSetting',
          },
          {
            net_total: value,
            month: 36,
            contract_fee: 2,
            annual_pct_rate: 24.3,
            management_fee: 0.9,
            irr: 49.91,
            monthly_payment: 60.17,
            __typename: 'CreditSetting',
          },
          {
            net_total: value,
            month: 48,
            contract_fee: 2,
            annual_pct_rate: 25.2,
            management_fee: 0.9,
            irr: 49.47,
            monthly_payment: 52.52,
            __typename: 'CreditSetting',
          },
          {
            net_total: value,
            month: 60,
            contract_fee: 2,
            annual_pct_rate: 26.3,
            management_fee: 0.9,
            irr: 49.49,
            monthly_payment: 48.51,
            __typename: 'CreditSetting',
          },
        ],
      },
    },
  }),
  [ApplicationScheduleType.ESTO_X]: (value) => ({
    request: {
      query: CreditSettingsDocument,
      variables: {
        scheduleType: ApplicationScheduleType.ESTO_X,
        merchantId: 1,
        netTotal: value,
      },
    },
    result: {
      data: {
        credit_settings: [
          {
            net_total: value,
            month: 3,
            contract_fee: 0,
            annual_pct_rate: 0,
            management_fee: 0,
            irr: 0,
            monthly_payment: 411,
            __typename: 'CreditSetting',
          },
        ],
      },
    },
  }),
};
