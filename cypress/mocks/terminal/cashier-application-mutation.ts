import type { MockedResponse } from '@apollo/client/testing/core';
import {
  CashierApplicationDocument,
  type CashierApplicationMutationVariables,
} from 'shared/api';

export const CreateApplicationMutationMocks: Record<
  'successFlow' | 'errorFlow',
  (variables?: Partial<CashierApplicationMutationVariables>) => MockedResponse
> = {
  successFlow: (variables) => ({
    request: {
      query: CashierApplicationDocument,
      variables,
    },
    result: {
      data: {
        application: {
          id: 1875917,
          purchase_url: 'https://test-123.esto.ee/p/6OMYJ53Z?lang=et',
          __typename: 'Application',
        },
      },
    },
  }),
  errorFlow: (variables) => ({
    request: {
      query: CashierApplicationDocument,
      variables,
    },
    result: {
      error: [
        {
          message: 'validation',
          locations: [
            {
              line: 2,
              column: 3,
            },
          ],
          validation: {
            pin: ['validation.pin'],
          },
        },
      ],
      data: {
        application: null,
      },
    },
  }),
} as const;
