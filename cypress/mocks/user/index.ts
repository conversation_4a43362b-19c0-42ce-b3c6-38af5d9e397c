import type { MockedResponse } from '@apollo/client/testing/core';
import { UserDataDocument } from 'shared/api';

export const userMock: Readonly<MockedResponse> = {
  request: {
    query: UserDataDocument,
  },
  result: {
    data: {
      me: {
        __typename: 'User',
        id: 163352,
        email: 'my<PERSON><PERSON>.<PERSON><PERSON><PERSON><PERSON>@esto.eu',
        pin: null,
        is_password_set: true,
        language_abbr: 'en-us',
        profile: {
          first_name: '<PERSON><PERSON><PERSON>',
          last_name: '<PERSON><PERSON><PERSON><PERSON>',
          __typename: 'UserProfile',
        },
        permission_bits: 79,
        merchants: [
          {
            id: 1,
            name: 'Test Merchant',
            merchant_permission_bits: 0,
            send_emails: 0,
            settings: {
              merchant_financing_pct: 80,
              __typename: 'MerchantSettings',
            },
            logo_path:
              'https://esto-public.s3.eu-west-2.amazonaws.com/merchant/profile/2y10ry2m0i06j1uzqjcybavyuf86kbihelzmqpysfc9stc2papotrj2a.png',
            stores: [],
            __typename: 'UserMerchant',
          },
        ],
      },
    },
  },
};
