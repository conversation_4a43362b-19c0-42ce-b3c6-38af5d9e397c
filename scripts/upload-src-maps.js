/* eslint-disable no-undef */
/* eslint-disable @typescript-eslint/no-require-imports */
const SentryCli = require('@sentry/cli');
const { getCurrentVersion } = require('./get-version');

(async () => {
  const cli = new SentryCli();

  try {
    console.log('Creating release...');
    await cli.execute(['releases', 'new', getCurrentVersion()], true);

    console.log('Uploading source maps...');
    await cli.execute(
      [
        'releases',
        'files',
        getCurrentVersion(),
        'upload-sourcemaps',
        `${__dirname}/../build`,
        '--rewrite',
      ],
      true,
    );

    console.log('Finalizing release...');
    await cli.execute(['releases', 'finalize', getCurrentVersion()], true);
  } catch (e) {
    console.error(e);
    process.exit(e.code || 1);
  }
})();
